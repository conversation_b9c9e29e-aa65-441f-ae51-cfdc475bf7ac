{"extensionUris": [{"extensionUriAnchor": 1, "uri": "/functions_boolean.yaml"}, {"extensionUriAnchor": 4, "uri": "/functions_arithmetic_decimal.yaml"}, {"extensionUriAnchor": 3, "uri": "/functions_datetime.yaml"}, {"extensionUriAnchor": 2, "uri": "/functions_comparison.yaml"}], "extensions": [{"extensionFunction": {"extensionUriReference": 1, "name": "and:bool"}}, {"extensionFunction": {"extensionUriReference": 2, "functionAnchor": 1, "name": "equal:any_any"}}, {"extensionFunction": {"extensionUriReference": 3, "functionAnchor": 2, "name": "lt:date_date"}}, {"extensionFunction": {"extensionUriReference": 3, "functionAnchor": 3, "name": "gt:date_date"}}, {"extensionFunction": {"extensionUriReference": 4, "functionAnchor": 4, "name": "multiply:dec_dec"}}, {"extensionFunction": {"extensionUriReference": 4, "functionAnchor": 5, "name": "subtract:dec_dec"}}, {"extensionFunction": {"extensionUriReference": 4, "functionAnchor": 6, "name": "sum:dec"}}], "relations": [{"root": {"input": {"fetch": {"common": {"direct": {}}, "input": {"sort": {"common": {"direct": {}}, "input": {"project": {"common": {"emit": {"outputMapping": [4, 5, 6, 7]}}, "input": {"aggregate": {"common": {"direct": {}}, "input": {"project": {"common": {"emit": {"outputMapping": [33, 34, 35, 36]}}, "input": {"filter": {"common": {"direct": {}}, "input": {"cross": {"common": {"direct": {}}, "left": {"cross": {"common": {"direct": {}}, "left": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["L_ORDERKEY", "L_PARTKEY", "L_SUPPKEY", "L_LINENUMBER", "L_QUANTITY", "L_EXTENDEDPRICE", "L_DISCOUNT", "L_TAX", "L_RETURNFLAG", "L_LINESTATUS", "L_SHIPDATE", "L_COMMITDATE", "L_RECEIPTDATE", "L_SHIPINSTRUCT", "L_SHIPMODE", "L_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["LINEITEM"]}}}, "right": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["C_CUSTKEY", "C_NAME", "C_ADDRESS", "C_NATIONKEY", "C_PHONE", "C_ACCTBAL", "C_MKTSEGMENT", "C_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["CUSTOMER"]}}}}}, "right": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["O_ORDERKEY", "O_CUSTKEY", "O_ORDERSTATUS", "O_TOTALPRICE", "O_ORDERDATE", "O_ORDERPRIORITY", "O_CLERK", "O_SHIPPRIORITY", "O_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["ORDERS"]}}}}}, "condition": {"scalarFunction": {"outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 22}}, "rootReference": {}}}}, {"value": {"literal": {"string": "BUILDING"}}}]}}}, {"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 16}}, "rootReference": {}}}}, {"value": {"selection": {"directReference": {"structField": {"field": 25}}, "rootReference": {}}}}]}}}, {"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {}}, "rootReference": {}}}}, {"value": {"selection": {"directReference": {"structField": {"field": 24}}, "rootReference": {}}}}]}}}, {"value": {"scalarFunction": {"functionReference": 2, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 28}}, "rootReference": {}}}}, {"value": {"cast": {"type": {"date": {"nullability": "NULLABILITY_REQUIRED"}}, "input": {"literal": {"fixedChar": "1995-03-15"}}, "failureBehavior": "FAILURE_BEHAVIOR_THROW_EXCEPTION"}}}]}}}, {"value": {"scalarFunction": {"functionReference": 3, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 10}}, "rootReference": {}}}}, {"value": {"cast": {"type": {"date": {"nullability": "NULLABILITY_REQUIRED"}}, "input": {"literal": {"fixedChar": "1995-03-15"}}, "failureBehavior": "FAILURE_BEHAVIOR_THROW_EXCEPTION"}}}]}}}]}}}}, "expressions": [{"selection": {"directReference": {"structField": {}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 28}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 31}}, "rootReference": {}}}, {"scalarFunction": {"functionReference": 4, "outputType": {"decimal": {"scale": 4, "precision": 19, "nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 5}}, "rootReference": {}}}}, {"value": {"scalarFunction": {"functionReference": 5, "outputType": {"decimal": {"scale": 2, "precision": 16, "nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"cast": {"type": {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, "input": {"literal": {"i32": 1}}, "failureBehavior": "FAILURE_BEHAVIOR_THROW_EXCEPTION"}}}, {"value": {"selection": {"directReference": {"structField": {"field": 6}}, "rootReference": {}}}}]}}}]}}]}}, "groupings": [{"groupingExpressions": [{"selection": {"directReference": {"structField": {}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 2}}, "rootReference": {}}}]}], "measures": [{"measure": {"functionReference": 6, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"decimal": {"scale": 4, "precision": 19, "nullability": "NULLABILITY_NULLABLE"}}, "invocation": "AGGREGATION_INVOCATION_ALL", "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 3}}, "rootReference": {}}}}]}}]}}, "expressions": [{"selection": {"directReference": {"structField": {}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 3}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 2}}, "rootReference": {}}}]}}, "sorts": [{"expr": {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}, "direction": "SORT_DIRECTION_DESC_NULLS_FIRST"}, {"expr": {"selection": {"directReference": {"structField": {"field": 2}}, "rootReference": {}}}, "direction": "SORT_DIRECTION_ASC_NULLS_LAST"}]}}, "count": "10"}}, "names": ["L_ORDERKEY", "REVENUE", "O_ORDERDATE", "O_SHIPPRIORITY"]}}]}