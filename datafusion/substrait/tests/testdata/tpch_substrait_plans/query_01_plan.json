{"extensionUris": [{"extensionUriAnchor": 3, "uri": "/functions_aggregate_generic.yaml"}, {"extensionUriAnchor": 2, "uri": "/functions_arithmetic_decimal.yaml"}, {"extensionUriAnchor": 1, "uri": "/functions_datetime.yaml"}], "extensions": [{"extensionFunction": {"extensionUriReference": 1, "name": "lte:date_date"}}, {"extensionFunction": {"extensionUriReference": 1, "functionAnchor": 1, "name": "subtract:date_iday"}}, {"extensionFunction": {"extensionUriReference": 2, "functionAnchor": 2, "name": "multiply:dec_dec"}}, {"extensionFunction": {"extensionUriReference": 2, "functionAnchor": 3, "name": "subtract:dec_dec"}}, {"extensionFunction": {"extensionUriReference": 2, "functionAnchor": 4, "name": "add:dec_dec"}}, {"extensionFunction": {"extensionUriReference": 2, "functionAnchor": 5, "name": "sum:dec"}}, {"extensionFunction": {"extensionUriReference": 2, "functionAnchor": 6, "name": "avg:dec"}}, {"extensionFunction": {"extensionUriReference": 3, "functionAnchor": 7, "name": "count:"}}], "relations": [{"root": {"input": {"sort": {"common": {"direct": {}}, "input": {"aggregate": {"common": {"direct": {}}, "input": {"project": {"common": {"emit": {"outputMapping": [16, 17, 18, 19, 20, 21, 22]}}, "input": {"filter": {"common": {"direct": {}}, "input": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["L_ORDERKEY", "L_PARTKEY", "L_SUPPKEY", "L_LINENUMBER", "L_QUANTITY", "L_EXTENDEDPRICE", "L_DISCOUNT", "L_TAX", "L_RETURNFLAG", "L_LINESTATUS", "L_SHIPDATE", "L_COMMITDATE", "L_RECEIPTDATE", "L_SHIPINSTRUCT", "L_SHIPMODE", "L_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["LINEITEM"]}}}, "condition": {"scalarFunction": {"outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 10}}, "rootReference": {}}}}, {"value": {"scalarFunction": {"functionReference": 1, "outputType": {"date": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"literal": {"date": 10561}}}, {"value": {"literal": {"intervalDayToSecond": {"seconds": 10368}}}}]}}}]}}}}, "expressions": [{"selection": {"directReference": {"structField": {"field": 8}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 9}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 4}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 5}}, "rootReference": {}}}, {"scalarFunction": {"functionReference": 2, "outputType": {"decimal": {"scale": 4, "precision": 19, "nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 5}}, "rootReference": {}}}}, {"value": {"scalarFunction": {"functionReference": 3, "outputType": {"decimal": {"scale": 2, "precision": 16, "nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"cast": {"type": {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, "input": {"literal": {"i32": 1}}, "failureBehavior": "FAILURE_BEHAVIOR_THROW_EXCEPTION"}}}, {"value": {"selection": {"directReference": {"structField": {"field": 6}}, "rootReference": {}}}}]}}}]}}, {"scalarFunction": {"functionReference": 2, "outputType": {"decimal": {"scale": 6, "precision": 19, "nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"scalarFunction": {"functionReference": 2, "outputType": {"decimal": {"scale": 4, "precision": 19, "nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 5}}, "rootReference": {}}}}, {"value": {"scalarFunction": {"functionReference": 3, "outputType": {"decimal": {"scale": 2, "precision": 16, "nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"cast": {"type": {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, "input": {"literal": {"i32": 1}}, "failureBehavior": "FAILURE_BEHAVIOR_THROW_EXCEPTION"}}}, {"value": {"selection": {"directReference": {"structField": {"field": 6}}, "rootReference": {}}}}]}}}]}}}, {"value": {"scalarFunction": {"functionReference": 4, "outputType": {"decimal": {"scale": 2, "precision": 16, "nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"cast": {"type": {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, "input": {"literal": {"i32": 1}}, "failureBehavior": "FAILURE_BEHAVIOR_THROW_EXCEPTION"}}}, {"value": {"selection": {"directReference": {"structField": {"field": 7}}, "rootReference": {}}}}]}}}]}}, {"selection": {"directReference": {"structField": {"field": 6}}, "rootReference": {}}}]}}, "groupings": [{"groupingExpressions": [{"selection": {"directReference": {"structField": {}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}]}], "measures": [{"measure": {"functionReference": 5, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_NULLABLE"}}, "invocation": "AGGREGATION_INVOCATION_ALL", "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 2}}, "rootReference": {}}}}]}}, {"measure": {"functionReference": 5, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_NULLABLE"}}, "invocation": "AGGREGATION_INVOCATION_ALL", "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 3}}, "rootReference": {}}}}]}}, {"measure": {"functionReference": 5, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"decimal": {"scale": 4, "precision": 19, "nullability": "NULLABILITY_NULLABLE"}}, "invocation": "AGGREGATION_INVOCATION_ALL", "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 4}}, "rootReference": {}}}}]}}, {"measure": {"functionReference": 5, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"decimal": {"scale": 6, "precision": 19, "nullability": "NULLABILITY_NULLABLE"}}, "invocation": "AGGREGATION_INVOCATION_ALL", "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 5}}, "rootReference": {}}}}]}}, {"measure": {"functionReference": 6, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_NULLABLE"}}, "invocation": "AGGREGATION_INVOCATION_ALL", "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 2}}, "rootReference": {}}}}]}}, {"measure": {"functionReference": 6, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_NULLABLE"}}, "invocation": "AGGREGATION_INVOCATION_ALL", "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 3}}, "rootReference": {}}}}]}}, {"measure": {"functionReference": 6, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_NULLABLE"}}, "invocation": "AGGREGATION_INVOCATION_ALL", "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 6}}, "rootReference": {}}}}]}}, {"measure": {"functionReference": 7, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, "invocation": "AGGREGATION_INVOCATION_ALL"}}]}}, "sorts": [{"expr": {"selection": {"directReference": {"structField": {}}, "rootReference": {}}}, "direction": "SORT_DIRECTION_ASC_NULLS_LAST"}, {"expr": {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}, "direction": "SORT_DIRECTION_ASC_NULLS_LAST"}]}}, "names": ["L_RETURNFLAG", "L_LINESTATUS", "SUM_QTY", "SUM_BASE_PRICE", "SUM_DISC_PRICE", "SUM_CHARGE", "AVG_QTY", "AVG_PRICE", "AVG_DISC", "COUNT_ORDER"]}}]}