{"extensionUris": [{"extensionUriAnchor": 1, "uri": "/functions_boolean.yaml"}, {"extensionUriAnchor": 3, "uri": "/functions_string.yaml"}, {"extensionUriAnchor": 4, "uri": "/functions_arithmetic_decimal.yaml"}, {"extensionUriAnchor": 2, "uri": "/functions_comparison.yaml"}], "extensions": [{"extensionFunction": {"extensionUriReference": 1, "name": "and:bool"}}, {"extensionFunction": {"extensionUriReference": 2, "functionAnchor": 1, "name": "equal:any_any"}}, {"extensionFunction": {"extensionUriReference": 3, "functionAnchor": 2, "name": "like:str_str"}}, {"extensionFunction": {"extensionUriReference": 4, "functionAnchor": 3, "name": "min:dec"}}], "relations": [{"root": {"input": {"fetch": {"common": {"direct": {}}, "input": {"sort": {"common": {"direct": {}}, "input": {"project": {"common": {"emit": {"outputMapping": [28, 29, 30, 31, 32, 33, 34, 35]}}, "input": {"filter": {"common": {"direct": {}}, "input": {"cross": {"common": {"direct": {}}, "left": {"cross": {"common": {"direct": {}}, "left": {"cross": {"common": {"direct": {}}, "left": {"cross": {"common": {"direct": {}}, "left": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["P_PARTKEY", "P_NAME", "P_MFGR", "P_BRAND", "P_TYPE", "P_SIZE", "P_CONTAINER", "P_RETAILPRICE", "P_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["PART"]}}}, "right": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["S_SUPPKEY", "S_NAME", "S_ADDRESS", "S_NATIONKEY", "S_PHONE", "S_ACCTBAL", "S_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["SUPPLIER"]}}}}}, "right": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["PS_PARTKEY", "PS_SUPPKEY", "PS_AVAILQTY", "PS_SUPPLYCOST", "PS_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["PARTSUPP"]}}}}}, "right": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["N_NATIONKEY", "N_NAME", "N_REGIONKEY", "N_COMMENT"], "struct": {"types": [{"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["NATION"]}}}}}, "right": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["R_REGIONKEY", "R_NAME", "R_COMMENT"], "struct": {"types": [{"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["REGION"]}}}}}, "condition": {"scalarFunction": {"outputType": {"bool": {"nullability": "NULLABILITY_NULLABLE"}}, "arguments": [{"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {}}, "rootReference": {}}}}, {"value": {"selection": {"directReference": {"structField": {"field": 16}}, "rootReference": {}}}}]}}}, {"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 9}}, "rootReference": {}}}}, {"value": {"selection": {"directReference": {"structField": {"field": 17}}, "rootReference": {}}}}]}}}, {"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 5}}, "rootReference": {}}}}, {"value": {"literal": {"i32": 15}}}]}}}, {"value": {"scalarFunction": {"functionReference": 2, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 4}}, "rootReference": {}}}}, {"value": {"cast": {"type": {"string": {"nullability": "NULLABILITY_REQUIRED"}}, "input": {"literal": {"fixedChar": "%BRASS"}}, "failureBehavior": "FAILURE_BEHAVIOR_THROW_EXCEPTION"}}}]}}}, {"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 12}}, "rootReference": {}}}}, {"value": {"selection": {"directReference": {"structField": {"field": 21}}, "rootReference": {}}}}]}}}, {"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 23}}, "rootReference": {}}}}, {"value": {"selection": {"directReference": {"structField": {"field": 25}}, "rootReference": {}}}}]}}}, {"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 26}}, "rootReference": {}}}}, {"value": {"literal": {"string": "EUROPE"}}}]}}}, {"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_NULLABLE"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 19}}, "rootReference": {}}}}, {"value": {"subquery": {"scalar": {"input": {"aggregate": {"common": {"direct": {}}, "input": {"project": {"common": {"emit": {"outputMapping": [19]}}, "input": {"filter": {"common": {"direct": {}}, "input": {"cross": {"common": {"direct": {}}, "left": {"cross": {"common": {"direct": {}}, "left": {"cross": {"common": {"direct": {}}, "left": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["PS_PARTKEY", "PS_SUPPKEY", "PS_AVAILQTY", "PS_SUPPLYCOST", "PS_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["PARTSUPP"]}}}, "right": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["S_SUPPKEY", "S_NAME", "S_ADDRESS", "S_NATIONKEY", "S_PHONE", "S_ACCTBAL", "S_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["SUPPLIER"]}}}}}, "right": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["N_NATIONKEY", "N_NAME", "N_REGIONKEY", "N_COMMENT"], "struct": {"types": [{"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["NATION"]}}}}}, "right": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["R_REGIONKEY", "R_NAME", "R_COMMENT"], "struct": {"types": [{"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["REGION"]}}}}}, "condition": {"scalarFunction": {"outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {}}, "outerReference": {"stepsOut": 1}}}}, {"value": {"selection": {"directReference": {"structField": {}}, "rootReference": {}}}}]}}}, {"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 5}}, "rootReference": {}}}}, {"value": {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}}]}}}, {"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 8}}, "rootReference": {}}}}, {"value": {"selection": {"directReference": {"structField": {"field": 12}}, "rootReference": {}}}}]}}}, {"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 14}}, "rootReference": {}}}}, {"value": {"selection": {"directReference": {"structField": {"field": 16}}, "rootReference": {}}}}]}}}, {"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 17}}, "rootReference": {}}}}, {"value": {"literal": {"string": "EUROPE"}}}]}}}]}}}}, "expressions": [{"selection": {"directReference": {"structField": {"field": 3}}, "rootReference": {}}}]}}, "groupings": [{}], "measures": [{"measure": {"functionReference": 3, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_NULLABLE"}}, "invocation": "AGGREGATION_INVOCATION_ALL", "arguments": [{"value": {"selection": {"directReference": {"structField": {}}, "rootReference": {}}}}]}}]}}}}}}]}}}]}}}}, "expressions": [{"selection": {"directReference": {"structField": {"field": 14}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 10}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 22}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 2}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 11}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 13}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 15}}, "rootReference": {}}}]}}, "sorts": [{"expr": {"selection": {"directReference": {"structField": {}}, "rootReference": {}}}, "direction": "SORT_DIRECTION_DESC_NULLS_FIRST"}, {"expr": {"selection": {"directReference": {"structField": {"field": 2}}, "rootReference": {}}}, "direction": "SORT_DIRECTION_ASC_NULLS_LAST"}, {"expr": {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}, "direction": "SORT_DIRECTION_ASC_NULLS_LAST"}, {"expr": {"selection": {"directReference": {"structField": {"field": 3}}, "rootReference": {}}}, "direction": "SORT_DIRECTION_ASC_NULLS_LAST"}]}}, "count": "100"}}, "names": ["S_ACCTBAL", "S_NAME", "N_NAME", "P_PARTKEY", "P_MFGR", "S_ADDRESS", "S_PHONE", "S_COMMENT"]}}]}