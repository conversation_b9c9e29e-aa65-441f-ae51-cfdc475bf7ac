{"extensionUris": [{"extensionUriAnchor": 1, "uri": "/functions_boolean.yaml"}, {"extensionUriAnchor": 4, "uri": "/functions_string.yaml"}, {"extensionUriAnchor": 5, "uri": "/functions_arithmetic_decimal.yaml"}, {"extensionUriAnchor": 3, "uri": "/functions_datetime.yaml"}, {"extensionUriAnchor": 2, "uri": "/functions_comparison.yaml"}], "extensions": [{"extensionFunction": {"extensionUriReference": 1, "name": "and:bool"}}, {"extensionFunction": {"extensionUriReference": 2, "functionAnchor": 1, "name": "equal:any_any"}}, {"extensionFunction": {"extensionUriReference": 3, "functionAnchor": 2, "name": "gte:date_date"}}, {"extensionFunction": {"extensionUriReference": 3, "functionAnchor": 3, "name": "lt:date_date"}}, {"extensionFunction": {"extensionUriReference": 4, "functionAnchor": 4, "name": "like:str_str"}}, {"extensionFunction": {"extensionUriReference": 5, "functionAnchor": 5, "name": "multiply:dec_dec"}}, {"extensionFunction": {"extensionUriReference": 5, "functionAnchor": 6, "name": "subtract:dec_dec"}}, {"extensionFunction": {"extensionUriReference": 5, "functionAnchor": 7, "name": "sum:dec"}}, {"extensionFunction": {"extensionUriReference": 5, "functionAnchor": 8, "name": "divide:dec_dec"}}], "relations": [{"root": {"input": {"project": {"common": {"emit": {"outputMapping": [2]}}, "input": {"aggregate": {"common": {"direct": {}}, "input": {"project": {"common": {"emit": {"outputMapping": [25, 26]}}, "input": {"filter": {"common": {"direct": {}}, "input": {"cross": {"common": {"direct": {}}, "left": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["L_ORDERKEY", "L_PARTKEY", "L_SUPPKEY", "L_LINENUMBER", "L_QUANTITY", "L_EXTENDEDPRICE", "L_DISCOUNT", "L_TAX", "L_RETURNFLAG", "L_LINESTATUS", "L_SHIPDATE", "L_COMMITDATE", "L_RECEIPTDATE", "L_SHIPINSTRUCT", "L_SHIPMODE", "L_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["LINEITEM"]}}}, "right": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["P_PARTKEY", "P_NAME", "P_MFGR", "P_BRAND", "P_TYPE", "P_SIZE", "P_CONTAINER", "P_RETAILPRICE", "P_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["PART"]}}}}}, "condition": {"scalarFunction": {"outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}}, {"value": {"selection": {"directReference": {"structField": {"field": 16}}, "rootReference": {}}}}]}}}, {"value": {"scalarFunction": {"functionReference": 2, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 10}}, "rootReference": {}}}}, {"value": {"literal": {"date": 9374}}}]}}}, {"value": {"scalarFunction": {"functionReference": 3, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 10}}, "rootReference": {}}}}, {"value": {"cast": {"type": {"date": {"nullability": "NULLABILITY_REQUIRED"}}, "input": {"literal": {"fixedChar": "1995-10-01"}}, "failureBehavior": "FAILURE_BEHAVIOR_THROW_EXCEPTION"}}}]}}}]}}}}, "expressions": [{"ifThen": {"ifs": [{"if": {"scalarFunction": {"functionReference": 4, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 20}}, "rootReference": {}}}}, {"value": {"cast": {"type": {"string": {"nullability": "NULLABILITY_REQUIRED"}}, "input": {"literal": {"fixedChar": "PROMO%"}}, "failureBehavior": "FAILURE_BEHAVIOR_THROW_EXCEPTION"}}}]}}, "then": {"scalarFunction": {"functionReference": 5, "outputType": {"decimal": {"scale": 4, "precision": 19, "nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 5}}, "rootReference": {}}}}, {"value": {"scalarFunction": {"functionReference": 6, "outputType": {"decimal": {"scale": 2, "precision": 16, "nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"cast": {"type": {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, "input": {"literal": {"i32": 1}}, "failureBehavior": "FAILURE_BEHAVIOR_THROW_EXCEPTION"}}}, {"value": {"selection": {"directReference": {"structField": {"field": 6}}, "rootReference": {}}}}]}}}]}}}], "else": {"literal": {"decimal": {"value": "AAAAAAAAAAAAAAAAAAAAAA==", "precision": 19, "scale": 4}}}}}, {"scalarFunction": {"functionReference": 5, "outputType": {"decimal": {"scale": 4, "precision": 19, "nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 5}}, "rootReference": {}}}}, {"value": {"scalarFunction": {"functionReference": 6, "outputType": {"decimal": {"scale": 2, "precision": 16, "nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"cast": {"type": {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, "input": {"literal": {"i32": 1}}, "failureBehavior": "FAILURE_BEHAVIOR_THROW_EXCEPTION"}}}, {"value": {"selection": {"directReference": {"structField": {"field": 6}}, "rootReference": {}}}}]}}}]}}]}}, "groupings": [{}], "measures": [{"measure": {"functionReference": 7, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"decimal": {"scale": 4, "precision": 19, "nullability": "NULLABILITY_NULLABLE"}}, "invocation": "AGGREGATION_INVOCATION_ALL", "arguments": [{"value": {"selection": {"directReference": {"structField": {}}, "rootReference": {}}}}]}}, {"measure": {"functionReference": 7, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"decimal": {"scale": 4, "precision": 19, "nullability": "NULLABILITY_NULLABLE"}}, "invocation": "AGGREGATION_INVOCATION_ALL", "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}}]}}]}}, "expressions": [{"scalarFunction": {"functionReference": 8, "outputType": {"decimal": {"scale": 2, "precision": 19, "nullability": "NULLABILITY_NULLABLE"}}, "arguments": [{"value": {"scalarFunction": {"functionReference": 5, "outputType": {"decimal": {"scale": 6, "precision": 19, "nullability": "NULLABILITY_NULLABLE"}}, "arguments": [{"value": {"literal": {"decimal": {"value": "ECcAAAAAAAAAAAAAAAAAAA==", "precision": 5, "scale": 2}}}}, {"value": {"selection": {"directReference": {"structField": {}}, "rootReference": {}}}}]}}}, {"value": {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}}]}}]}}, "names": ["PROMO_REVENUE"]}}]}