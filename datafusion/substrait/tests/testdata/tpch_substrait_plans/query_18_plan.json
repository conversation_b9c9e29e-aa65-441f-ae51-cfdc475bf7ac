{"extensionUris": [{"extensionUriAnchor": 1, "uri": "/functions_boolean.yaml"}, {"extensionUriAnchor": 2, "uri": "/functions_arithmetic_decimal.yaml"}, {"extensionUriAnchor": 3, "uri": "/functions_comparison.yaml"}], "extensions": [{"extensionFunction": {"extensionUriReference": 1, "name": "and:bool"}}, {"extensionFunction": {"extensionUriReference": 2, "functionAnchor": 1, "name": "sum:dec"}}, {"extensionFunction": {"extensionUriReference": 3, "functionAnchor": 2, "name": "gt:any_any"}}, {"extensionFunction": {"extensionUriReference": 3, "functionAnchor": 3, "name": "equal:any_any"}}], "relations": [{"root": {"input": {"fetch": {"common": {"direct": {}}, "input": {"sort": {"common": {"direct": {}}, "input": {"aggregate": {"common": {"direct": {}}, "input": {"project": {"common": {"emit": {"outputMapping": [33, 34, 35, 36, 37, 38]}}, "input": {"filter": {"common": {"direct": {}}, "input": {"cross": {"common": {"direct": {}}, "left": {"cross": {"common": {"direct": {}}, "left": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["C_CUSTKEY", "C_NAME", "C_ADDRESS", "C_NATIONKEY", "C_PHONE", "C_ACCTBAL", "C_MKTSEGMENT", "C_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["CUSTOMER"]}}}, "right": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["O_ORDERKEY", "O_CUSTKEY", "O_ORDERSTATUS", "O_TOTALPRICE", "O_ORDERDATE", "O_ORDERPRIORITY", "O_CLERK", "O_SHIPPRIORITY", "O_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["ORDERS"]}}}}}, "right": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["L_ORDERKEY", "L_PARTKEY", "L_SUPPKEY", "L_LINENUMBER", "L_QUANTITY", "L_EXTENDEDPRICE", "L_DISCOUNT", "L_TAX", "L_RETURNFLAG", "L_LINESTATUS", "L_SHIPDATE", "L_COMMITDATE", "L_RECEIPTDATE", "L_SHIPINSTRUCT", "L_SHIPMODE", "L_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["LINEITEM"]}}}}}, "condition": {"scalarFunction": {"outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"subquery": {"inPredicate": {"needles": [{"selection": {"directReference": {"structField": {"field": 8}}, "rootReference": {}}}], "haystack": {"project": {"common": {"emit": {"outputMapping": [2]}}, "input": {"filter": {"common": {"direct": {}}, "input": {"aggregate": {"common": {"direct": {}}, "input": {"project": {"common": {"emit": {"outputMapping": [16, 17]}}, "input": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["L_ORDERKEY", "L_PARTKEY", "L_SUPPKEY", "L_LINENUMBER", "L_QUANTITY", "L_EXTENDEDPRICE", "L_DISCOUNT", "L_TAX", "L_RETURNFLAG", "L_LINESTATUS", "L_SHIPDATE", "L_COMMITDATE", "L_RECEIPTDATE", "L_SHIPINSTRUCT", "L_SHIPMODE", "L_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["LINEITEM"]}}}, "expressions": [{"selection": {"directReference": {"structField": {}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 4}}, "rootReference": {}}}]}}, "groupings": [{"groupingExpressions": [{"selection": {"directReference": {"structField": {}}, "rootReference": {}}}]}], "measures": [{"measure": {"functionReference": 1, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_NULLABLE"}}, "invocation": "AGGREGATION_INVOCATION_ALL", "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}}]}}]}}, "condition": {"scalarFunction": {"functionReference": 2, "outputType": {"bool": {"nullability": "NULLABILITY_NULLABLE"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}}, {"value": {"cast": {"type": {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_NULLABLE"}}, "input": {"literal": {"i32": 300}}, "failureBehavior": "FAILURE_BEHAVIOR_THROW_EXCEPTION"}}}]}}}}, "expressions": [{"selection": {"directReference": {"structField": {}}, "rootReference": {}}}]}}}}}}, {"value": {"scalarFunction": {"functionReference": 3, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {}}, "rootReference": {}}}}, {"value": {"selection": {"directReference": {"structField": {"field": 9}}, "rootReference": {}}}}]}}}, {"value": {"scalarFunction": {"functionReference": 3, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 8}}, "rootReference": {}}}}, {"value": {"selection": {"directReference": {"structField": {"field": 17}}, "rootReference": {}}}}]}}}]}}}}, "expressions": [{"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 8}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 12}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 11}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 21}}, "rootReference": {}}}]}}, "groupings": [{"groupingExpressions": [{"selection": {"directReference": {"structField": {}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 2}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 3}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 4}}, "rootReference": {}}}]}], "measures": [{"measure": {"functionReference": 1, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_NULLABLE"}}, "invocation": "AGGREGATION_INVOCATION_ALL", "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 5}}, "rootReference": {}}}}]}}]}}, "sorts": [{"expr": {"selection": {"directReference": {"structField": {"field": 4}}, "rootReference": {}}}, "direction": "SORT_DIRECTION_DESC_NULLS_FIRST"}, {"expr": {"selection": {"directReference": {"structField": {"field": 3}}, "rootReference": {}}}, "direction": "SORT_DIRECTION_ASC_NULLS_LAST"}]}}, "count": "100"}}, "names": ["C_NAME", "C_CUSTKEY", "O_ORDERKEY", "O_ORDERDATE", "O_TOTALPRICE", "EXPR$5"]}}]}