{"extensionUris": [{"extensionUriAnchor": 4, "uri": "/functions_arithmetic.yaml"}, {"extensionUriAnchor": 1, "uri": "/functions_boolean.yaml"}, {"extensionUriAnchor": 3, "uri": "/functions_datetime.yaml"}, {"extensionUriAnchor": 2, "uri": "/functions_comparison.yaml"}], "extensions": [{"extensionFunction": {"extensionUriReference": 1, "name": "and:bool"}}, {"extensionFunction": {"extensionUriReference": 2, "functionAnchor": 1, "name": "equal:any_any"}}, {"extensionFunction": {"extensionUriReference": 1, "functionAnchor": 2, "name": "or:bool"}}, {"extensionFunction": {"extensionUriReference": 3, "functionAnchor": 3, "name": "lt:date_date"}}, {"extensionFunction": {"extensionUriReference": 3, "functionAnchor": 4, "name": "gte:date_date"}}, {"extensionFunction": {"extensionUriReference": 2, "functionAnchor": 5, "name": "not_equal:any_any"}}, {"extensionFunction": {"extensionUriReference": 4, "functionAnchor": 6, "name": "sum:i32"}}], "relations": [{"root": {"input": {"sort": {"common": {"direct": {}}, "input": {"aggregate": {"common": {"direct": {}}, "input": {"project": {"common": {"emit": {"outputMapping": [25, 26, 27]}}, "input": {"filter": {"common": {"direct": {}}, "input": {"cross": {"common": {"direct": {}}, "left": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["O_ORDERKEY", "O_CUSTKEY", "O_ORDERSTATUS", "O_TOTALPRICE", "O_ORDERDATE", "O_ORDERPRIORITY", "O_CLERK", "O_SHIPPRIORITY", "O_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["ORDERS"]}}}, "right": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["L_ORDERKEY", "L_PARTKEY", "L_SUPPKEY", "L_LINENUMBER", "L_QUANTITY", "L_EXTENDEDPRICE", "L_DISCOUNT", "L_TAX", "L_RETURNFLAG", "L_LINESTATUS", "L_SHIPDATE", "L_COMMITDATE", "L_RECEIPTDATE", "L_SHIPINSTRUCT", "L_SHIPMODE", "L_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["LINEITEM"]}}}}}, "condition": {"scalarFunction": {"outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {}}, "rootReference": {}}}}, {"value": {"selection": {"directReference": {"structField": {"field": 9}}, "rootReference": {}}}}]}}}, {"value": {"scalarFunction": {"functionReference": 2, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 23}}, "rootReference": {}}}}, {"value": {"cast": {"type": {"string": {"nullability": "NULLABILITY_REQUIRED"}}, "input": {"literal": {"fixedChar": "MAIL"}}, "failureBehavior": "FAILURE_BEHAVIOR_THROW_EXCEPTION"}}}]}}}, {"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 23}}, "rootReference": {}}}}, {"value": {"cast": {"type": {"string": {"nullability": "NULLABILITY_REQUIRED"}}, "input": {"literal": {"fixedChar": "SHIP"}}, "failureBehavior": "FAILURE_BEHAVIOR_THROW_EXCEPTION"}}}]}}}]}}}, {"value": {"scalarFunction": {"functionReference": 3, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 20}}, "rootReference": {}}}}, {"value": {"selection": {"directReference": {"structField": {"field": 21}}, "rootReference": {}}}}]}}}, {"value": {"scalarFunction": {"functionReference": 3, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 19}}, "rootReference": {}}}}, {"value": {"selection": {"directReference": {"structField": {"field": 20}}, "rootReference": {}}}}]}}}, {"value": {"scalarFunction": {"functionReference": 4, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 21}}, "rootReference": {}}}}, {"value": {"cast": {"type": {"date": {"nullability": "NULLABILITY_REQUIRED"}}, "input": {"literal": {"fixedChar": "1994-01-01"}}, "failureBehavior": "FAILURE_BEHAVIOR_THROW_EXCEPTION"}}}]}}}, {"value": {"scalarFunction": {"functionReference": 3, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 21}}, "rootReference": {}}}}, {"value": {"cast": {"type": {"date": {"nullability": "NULLABILITY_REQUIRED"}}, "input": {"literal": {"fixedChar": "1995-01-01"}}, "failureBehavior": "FAILURE_BEHAVIOR_THROW_EXCEPTION"}}}]}}}]}}}}, "expressions": [{"selection": {"directReference": {"structField": {"field": 23}}, "rootReference": {}}}, {"ifThen": {"ifs": [{"if": {"scalarFunction": {"functionReference": 2, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 5}}, "rootReference": {}}}}, {"value": {"literal": {"string": "1-URGENT"}}}]}}}, {"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 5}}, "rootReference": {}}}}, {"value": {"literal": {"string": "2-HIGH"}}}]}}}]}}, "then": {"literal": {"i32": 1}}}], "else": {"literal": {"i32": 0}}}}, {"ifThen": {"ifs": [{"if": {"scalarFunction": {"outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"scalarFunction": {"functionReference": 5, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 5}}, "rootReference": {}}}}, {"value": {"literal": {"string": "1-URGENT"}}}]}}}, {"value": {"scalarFunction": {"functionReference": 5, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 5}}, "rootReference": {}}}}, {"value": {"literal": {"string": "2-HIGH"}}}]}}}]}}, "then": {"literal": {"i32": 1}}}], "else": {"literal": {"i32": 0}}}}]}}, "groupings": [{"groupingExpressions": [{"selection": {"directReference": {"structField": {}}, "rootReference": {}}}]}], "measures": [{"measure": {"functionReference": 6, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"i32": {"nullability": "NULLABILITY_NULLABLE"}}, "invocation": "AGGREGATION_INVOCATION_ALL", "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}}]}}, {"measure": {"functionReference": 6, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"i32": {"nullability": "NULLABILITY_NULLABLE"}}, "invocation": "AGGREGATION_INVOCATION_ALL", "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 2}}, "rootReference": {}}}}]}}]}}, "sorts": [{"expr": {"selection": {"directReference": {"structField": {}}, "rootReference": {}}}, "direction": "SORT_DIRECTION_ASC_NULLS_LAST"}]}}, "names": ["L_SHIPMODE", "HIGH_LINE_COUNT", "LOW_LINE_COUNT"]}}]}