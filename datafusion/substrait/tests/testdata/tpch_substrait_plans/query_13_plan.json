{"extensionUris": [{"extensionUriAnchor": 4, "uri": "/functions_aggregate_generic.yaml"}, {"extensionUriAnchor": 1, "uri": "/functions_boolean.yaml"}, {"extensionUriAnchor": 3, "uri": "/functions_string.yaml"}, {"extensionUriAnchor": 2, "uri": "/functions_comparison.yaml"}], "extensions": [{"extensionFunction": {"extensionUriReference": 1, "name": "and:bool"}}, {"extensionFunction": {"extensionUriReference": 2, "functionAnchor": 1, "name": "equal:any_any"}}, {"extensionFunction": {"extensionUriReference": 1, "functionAnchor": 2, "name": "not:bool"}}, {"extensionFunction": {"extensionUriReference": 3, "functionAnchor": 3, "name": "like:str_str"}}, {"extensionFunction": {"extensionUriReference": 4, "functionAnchor": 4, "name": "count:any"}}, {"extensionFunction": {"extensionUriReference": 4, "functionAnchor": 5, "name": "count:"}}], "relations": [{"root": {"input": {"sort": {"common": {"direct": {}}, "input": {"project": {"common": {"emit": {"outputMapping": [2, 3]}}, "input": {"aggregate": {"common": {"direct": {}}, "input": {"project": {"common": {"emit": {"outputMapping": [2]}}, "input": {"aggregate": {"common": {"direct": {}}, "input": {"project": {"common": {"emit": {"outputMapping": [17, 18]}}, "input": {"join": {"common": {"direct": {}}, "left": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["C_CUSTKEY", "C_NAME", "C_ADDRESS", "C_NATIONKEY", "C_PHONE", "C_ACCTBAL", "C_MKTSEGMENT", "C_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["CUSTOMER"]}}}, "right": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["O_ORDERKEY", "O_CUSTKEY", "O_ORDERSTATUS", "O_TOTALPRICE", "O_ORDERDATE", "O_ORDERPRIORITY", "O_CLERK", "O_SHIPPRIORITY", "O_COMMENT"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"decimal": {"scale": 2, "precision": 15, "nullability": "NULLABILITY_REQUIRED"}}, {"date": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["ORDERS"]}}}, "expression": {"scalarFunction": {"outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {}}, "rootReference": {}}}}, {"value": {"selection": {"directReference": {"structField": {"field": 9}}, "rootReference": {}}}}]}}}, {"value": {"scalarFunction": {"functionReference": 2, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"scalarFunction": {"functionReference": 3, "outputType": {"bool": {"nullability": "NULLABILITY_REQUIRED"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 16}}, "rootReference": {}}}}, {"value": {"cast": {"type": {"string": {"nullability": "NULLABILITY_REQUIRED"}}, "input": {"literal": {"fixedChar": "%special%requests%"}}, "failureBehavior": "FAILURE_BEHAVIOR_THROW_EXCEPTION"}}}]}}}]}}}]}}, "type": "JOIN_TYPE_LEFT"}}, "expressions": [{"selection": {"directReference": {"structField": {}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 8}}, "rootReference": {}}}]}}, "groupings": [{"groupingExpressions": [{"selection": {"directReference": {"structField": {}}, "rootReference": {}}}]}], "measures": [{"measure": {"functionReference": 4, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, "invocation": "AGGREGATION_INVOCATION_ALL", "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}}]}}]}}, "expressions": [{"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}]}}, "groupings": [{"groupingExpressions": [{"selection": {"directReference": {"structField": {}}, "rootReference": {}}}]}], "measures": [{"measure": {"functionReference": 5, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"i64": {"nullability": "NULLABILITY_REQUIRED"}}, "invocation": "AGGREGATION_INVOCATION_ALL"}}]}}, "expressions": [{"selection": {"directReference": {"structField": {}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}]}}, "sorts": [{"expr": {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}, "direction": "SORT_DIRECTION_DESC_NULLS_FIRST"}, {"expr": {"selection": {"directReference": {"structField": {}}, "rootReference": {}}}, "direction": "SORT_DIRECTION_DESC_NULLS_FIRST"}]}}, "names": ["C_COUNT", "CUSTDIST"]}}]}