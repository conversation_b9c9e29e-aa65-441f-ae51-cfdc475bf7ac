{"extensionUris": [{"extensionUriAnchor": 1, "uri": "https://github.com/substrait-io/substrait/blob/main/extensions/functions_string.yaml"}], "extensions": [{"extensionFunction": {"extensionUriReference": 1, "functionAnchor": 1, "name": "contains:str_str"}}], "relations": [{"root": {"input": {"project": {"common": {"emit": {"outputMapping": [4]}}, "input": {"filter": {"input": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["n_nationkey", "n_name", "n_regionkey", "n_comment"], "struct": {"types": [{"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}, {"i32": {"nullability": "NULLABILITY_REQUIRED"}}, {"string": {"nullability": "NULLABILITY_REQUIRED"}}], "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["nation"]}}}, "condition": {"scalarFunction": {"functionReference": 1, "outputType": {"bool": {"nullability": "NULLABILITY_NULLABLE"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}}, {"value": {"literal": {"string": "IA"}}}]}}}}, "expressions": [{"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}]}}, "names": ["n_name"]}}], "version": {"minorNumber": 38, "producer": "ibis-substrait"}}