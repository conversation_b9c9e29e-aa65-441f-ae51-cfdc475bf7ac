{"extensionUris": [], "extensions": [], "relations": [{"root": {"input": {"project": {"common": {"emit": {"outputMapping": [2, 3]}}, "input": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["a", "b"], "struct": {"types": [{"i32": {"typeVariationReference": 0, "nullability": "NULLABILITY_NULLABLE"}}, {"i32": {"typeVariationReference": 0, "nullability": "NULLABILITY_REQUIRED"}}], "typeVariationReference": 0, "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["DATA"]}}}, "expressions": [{"selection": {"directReference": {"structField": {"field": 0}}, "rootReference": {}}}, {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}]}}, "names": ["a", "b"]}}], "expectedTypeUrls": []}