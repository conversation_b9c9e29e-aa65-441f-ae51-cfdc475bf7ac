{"extensionUris": [{"uri": "https://github.com/substrait-io/substrait/blob/main/extensions/functions_aggregate_generic.yaml"}], "extensions": [{"extensionFunction": {"functionAnchor": 185, "name": "count:any"}}], "relations": [{"root": {"input": {"aggregate": {"input": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["a"], "struct": {"types": [{"i64": {"nullability": "NULLABILITY_NULLABLE"}}], "nullability": "NULLABILITY_NULLABLE"}}, "namedTable": {"names": ["data"]}}}, "groupings": [{"groupingExpressions": [{"selection": {"directReference": {"structField": {}}, "rootReference": {}}}]}], "measures": [{"measure": {"functionReference": 185, "phase": "AGGREGATION_PHASE_INITIAL_TO_RESULT", "outputType": {"i64": {}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {}}, "rootReference": {}}}}]}}]}}, "names": ["a", "countA"]}}], "version": {"minorNumber": 54, "producer": "subframe"}}