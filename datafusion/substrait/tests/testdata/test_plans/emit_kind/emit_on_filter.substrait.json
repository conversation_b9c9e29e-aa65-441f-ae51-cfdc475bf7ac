{"extensionUris": [{"extensionUriAnchor": 1, "uri": "/functions_comparison.yaml"}], "extensions": [{"extensionFunction": {"extensionUriReference": 1, "functionAnchor": 0, "name": "equal:any_any"}}], "relations": [{"root": {"input": {"filter": {"common": {"emit": {"outputMapping": [1, 0, 0]}}, "input": {"read": {"common": {"direct": {}}, "baseSchema": {"names": ["A", "B"], "struct": {"types": [{"i64": {"typeVariationReference": 0, "nullability": "NULLABILITY_NULLABLE"}}, {"i64": {"typeVariationReference": 0, "nullability": "NULLABILITY_NULLABLE"}}], "typeVariationReference": 0, "nullability": "NULLABILITY_REQUIRED"}}, "namedTable": {"names": ["DATA"]}}}, "condition": {"scalarFunction": {"functionReference": 0, "args": [], "outputType": {"bool": {"typeVariationReference": 0, "nullability": "NULLABILITY_NULLABLE"}}, "arguments": [{"value": {"selection": {"directReference": {"structField": {"field": 1}}, "rootReference": {}}}}, {"value": {"literal": {"i64": "2", "nullable": false, "typeVariationReference": 0}}}], "options": []}}}}, "names": ["B", "A1", "A2"]}}], "expectedTypeUrls": []}