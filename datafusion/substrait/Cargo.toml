# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

[package]
name = "datafusion-substrait"
description = "DataFusion Substrait Producer and Consumer"
readme = "README.md"
version = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }
license = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }

[lints]
workspace = true

[dependencies]
async-recursion = "1.0"
async-trait = { workspace = true }
chrono = { workspace = true }
datafusion = { workspace = true }
itertools = { workspace = true }
object_store = { workspace = true }
pbjson-types = { workspace = true }
prost = { workspace = true }
substrait = { version = "0.55", features = ["serde"] }
url = { workspace = true }
tokio = { workspace = true, features = ["fs"] }

[dev-dependencies]
datafusion = { workspace = true, features = ["nested_expressions"] }
datafusion-functions-aggregate = { workspace = true }
serde_json = "1.0"
tokio = { workspace = true }
insta = { workspace = true }

[features]
default = ["physical"]
physical = ["datafusion/parquet"]
protoc = ["substrait/protoc"]

[package.metadata.docs.rs]
# Use default features ("physical") for docs, plus "protoc". "protoc" is needed
# to get a consistent version of the protobuf compiler in the docs build;
# without that, an outdated protobuf compiler may fail to compile the protobuf
# files as it did in versions 42.0.0 through 44.0.0.
all-features = true
