# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

[package]
authors = { workspace = true }
description = "DataFusion sqllogictest driver"
edition = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
name = "datafusion-sqllogictest"
readme = "README.md"
repository = { workspace = true }
rust-version = { workspace = true }
version = { workspace = true }

[package.metadata.docs.rs]
all-features = true

[lints]
workspace = true

[lib]
name = "datafusion_sqllogictest"

[dependencies]
arrow = { workspace = true }
async-trait = { workspace = true }
bigdecimal = { workspace = true }
bytes = { workspace = true, optional = true }
chrono = { workspace = true, optional = true }
clap = { version = "4.5.37", features = ["derive", "env"] }
datafusion = { workspace = true, default-features = true, features = ["avro"] }
futures = { workspace = true }
half = { workspace = true, default-features = true }
indicatif = "0.17"
itertools = { workspace = true }
log = { workspace = true }
object_store = { workspace = true }
postgres-protocol = { version = "0.6.7", optional = true }
postgres-types = { version = "0.2.8", features = ["derive", "with-chrono-0_4"], optional = true }
rust_decimal = { version = "1.37.1", features = ["tokio-pg"] }
# When updating the following dependency verify that sqlite test file regeneration works correctly
# by running the regenerate_sqlite_files.sh script.
sqllogictest = "0.28.1"
sqlparser = { workspace = true }
tempfile = { workspace = true }
testcontainers = { version = "0.23", features = ["default"], optional = true }
testcontainers-modules = { version = "0.11", features = ["postgres"], optional = true }
thiserror = "2.0.12"
tokio = { workspace = true }
tokio-postgres = { version = "0.7.12", optional = true }

[features]
avro = ["datafusion/avro"]
backtrace = ["datafusion/backtrace"]
postgres = [
    "bytes",
    "chrono",
    "postgres-types",
    "postgres-protocol",
    "testcontainers",
    "testcontainers-modules",
    "tokio-postgres",
]

[dev-dependencies]
env_logger = { workspace = true }
tokio = { workspace = true, features = ["rt-multi-thread"] }

[[test]]
harness = false
name = "sqllogictests"
path = "bin/sqllogictests.rs"
