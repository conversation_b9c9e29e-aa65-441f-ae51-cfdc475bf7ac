# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

statement ok
CREATE TABLE test(
  num INT,
  bin_field  BYTEA,
  base64_field TEXT,
  hex_field TEXT
) as VALUES
  (0, 'abc',  encode('abc', 'base64'), encode('abc', 'hex')),
  (1, 'qweqwe', encode('qweqwe', 'base64'), encode('qweqwe', 'hex')),
  (2, NULL, NULL, NULL),
  (3, X'8f50d3f60eae370ddbf85c86219c55108a350165', encode('8f50d3f60eae370ddbf85c86219c55108a350165', 'base64'), encode('8f50d3f60eae370ddbf85c86219c55108a350165', 'hex'))
;

# errors
query error 1st argument should be Utf8 or Binary or Null, got Int64
select encode(12, 'hex');

query error DataFusion error: Error during planning: There is no built\-in encoding named 'non_encoding', currently supported encodings are: base64, hex
select encode(bin_field, 'non_encoding') from test;

query error 1st argument should be Utf8 or Binary or Null, got Int64
select decode(12, 'hex');

query error DataFusion error: Error during planning: There is no built\-in encoding named 'non_encoding', currently supported encodings are: base64, hex
select decode(hex_field, 'non_encoding') from test;

query error
select to_hex(hex_field) from test;

query error
select arrow_cast(decode(X'8f50d3f60eae370ddbf85c86219c55108a350165', 'base64'), 'Utf8');

# Arrays tests
query T
SELECT encode(bin_field, 'hex') FROM test ORDER BY num;
----
616263
717765717765
NULL
8f50d3f60eae370ddbf85c86219c55108a350165

query T
SELECT arrow_cast(decode(base64_field, 'base64'), 'Utf8') FROM test ORDER BY num;
----
abc
qweqwe
NULL
8f50d3f60eae370ddbf85c86219c55108a350165

query T
SELECT arrow_cast(decode(hex_field, 'hex'), 'Utf8') FROM test ORDER BY num;
----
abc
qweqwe
NULL
8f50d3f60eae370ddbf85c86219c55108a350165

query T
select to_hex(num) from test ORDER BY num;
----
0
1
2
3

query T
select encode(bin_field, 'base64') FROM test WHERE num = 3;
----
j1DT9g6uNw3b+FyGIZxVEIo1AWU

query B
select decode(encode(bin_field, 'base64'), 'base64') = X'8f50d3f60eae370ddbf85c86219c55108a350165' FROM test WHERE num = 3;
----
true

# test for Utf8View support for encode
statement ok
CREATE TABLE test_source AS VALUES
    ('Andrew', 'X'),
    ('Xiangpeng', 'Xiangpeng'),
    ('Raphael', 'R'),
    (NULL, 'R');

statement ok
CREATE TABLE test_utf8view AS
select
  arrow_cast(column1, 'Utf8View') AS column1_utf8view,
  arrow_cast(column2, 'Utf8View') AS column2_utf8view
FROM test_source;

query TTTTTT
SELECT
  column1_utf8view,
  encode(column1_utf8view, 'base64') AS column1_base64,
  encode(column1_utf8view, 'hex') AS column1_hex,
  
  column2_utf8view,
  encode(column2_utf8view, 'base64') AS column2_base64,
  encode(column2_utf8view, 'hex') AS column2_hex
FROM test_utf8view;
----
Andrew QW5kcmV3 416e64726577 X WA 58
Xiangpeng WGlhbmdwZW5n 5869616e6770656e67 Xiangpeng WGlhbmdwZW5n 5869616e6770656e67
Raphael UmFwaGFlbA 5261706861656c R Ug 52
NULL NULL NULL R Ug 52

# test for hex digest
query T
select encode(digest('hello', 'sha256'), 'hex');
----
2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824
