# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.


# prepare the tables

statement ok
create table t1 (a int, b int);

statement ok
create table t2 (a int, b int);

statement ok
create table t3 (a int, b int);

statement ok
insert into t1 values (1, 2);

statement ok
insert into t2 values (3, 4);

statement ok
insert into t3 values (5, 6);

query IIIIII
select * from (t1 cross join t2) as t cross join t3;
-------
----
1 2 3 4 5 6



query IIIIIIII
select * from (t1 cross join t2) as t cross join (t2 cross join t3)
-------
----
1 2 3 4 3 4 5 6


query IIIIIIIIIIII
select * from (t1 cross join t2) as t cross join (t2 cross join t3) cross join (t1 cross join t3) as tt
--------
----
1 2 3 4 3 4 5 6 1 2 5 6

query IIIIIIIIIIIIIIII
select * from (t1 cross join t2) as t cross join (t2 cross join t3) cross join (t1 cross join t3) as tt cross join (t2 cross join t3) as ttt;
--------
----
1 2 3 4 3 4 5 6 1 2 5 6 3 4 5 6
