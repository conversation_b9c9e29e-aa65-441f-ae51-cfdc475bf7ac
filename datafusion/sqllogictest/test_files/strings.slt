# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

statement ok
CREATE TABLE test(
  s TEXT
) as VALUES
  ('p1'),
  ('p1e1'),
  ('p1m1e1'),
  ('P1'),
  ('P1e1'),
  ('P1m1e1'),
  ('e1'),
  ('p2'),
  ('p2e1'),
  ('p2m1e1')
;

# LIKE
query T rowsort
SELECT s FROM test WHERE s LIKE 'p1%';
----
p1
p1e1
p1m1e1

query T rowsort
SELECT s FROM test WHERE s LIKE '%m1%';
----
P1m1e1
p1m1e1
p2m1e1

# REGEX
query T rowsort
SELECT s FROM test WHERE s ~ 'p[12].*';
----
p1
p1e1
p1m1e1
p2
p2e1
p2m1e1

# REGEX nocase
query T rowsort
SELECT s FROM test WHERE s ~* 'p[12].*';
----
P1
P1e1
P1m1e1
p1
p1e1
p1m1e1
p2
p2e1
p2m1e1

# SIMILAR TO
query T rowsort
SELECT s FROM test WHERE s SIMILAR TO 'p[12].*';
----
p1
p1e1
p1m1e1
p2
p2e1
p2m1e1

# NOT SIMILAR TO
query T rowsort
SELECT s FROM test WHERE s NOT SIMILAR TO 'p[12].*';
----
P1
P1e1
P1m1e1
e1

# NOT LIKE
query T rowsort
SELECT s FROM test WHERE s NOT LIKE 'p1%';
----
P1
P1e1
P1m1e1
e1
p2
p2e1
p2m1e1


# ILIKE
query T rowsort
SELECT s FROM test WHERE s ILIKE 'p1%';
----
P1
P1e1
P1m1e1
p1
p1e1
p1m1e1

# NOT ILIKE
query T rowsort
SELECT s FROM test WHERE s NOT ILIKE 'p1%';
----
e1
p2
p2e1
p2m1e1

## VARCHAR with length support

# Lengths can be used by default
query T
SELECT '12345'::VARCHAR(2);
----
12345

# Lengths can not be used when the config setting is disabled

statement ok
set datafusion.sql_parser.support_varchar_with_length = false;

query error
SELECT '12345'::VARCHAR(2);

query error
SELECT s::VARCHAR(2) FROM (VALUES ('12345')) t(s);

statement ok
create table vals(s char) as values('abc'), ('def');

query error
SELECT s::VARCHAR(2) FROM vals

# Lengths can be used when the config setting is enabled

statement ok
set datafusion.sql_parser.support_varchar_with_length = true;

query T
SELECT '12345'::VARCHAR(2)
----
12345

query T
SELECT s::VARCHAR(2) FROM (VALUES ('12345')) t(s)
----
12345

query T
SELECT s::VARCHAR(2) FROM vals
----
abc
def

statement ok
drop table vals;
