# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

##########
## Test type coercion
##########

# test utf8 and large utf8
query B
select 's' is distinct from arrow_cast('s', 'LargeUtf8');
----
false

query B
select 's' is not distinct from arrow_cast('s', 'LargeUtf8');
----
true

# date - interval
query D
select '2023-05-01'::date - interval '1 month';
----
2023-04-01

# timestamp - interval
query P
SELECT '2023-05-01 12:30:00'::timestamp - interval '1 month';
----
2023-04-01T12:30:00

# interval - date
query error DataFusion error: Error during planning: Cannot coerce arithmetic expression Interval\(MonthDayNano\) \- Date32 to valid types
select interval '1 month' - '2023-05-01'::date;

# interval - timestamp
query error DataFusion error: Error during planning: Cannot coerce arithmetic expression Interval\(MonthDayNano\) \- Timestamp\(Nanosecond, None\) to valid types
SELECT interval '1 month' - '2023-05-01 12:30:00'::timestamp;

# dictionary(int32, utf8) -> utf8
query T
select arrow_cast('foo', 'Dictionary(Int32, Utf8)') || arrow_cast('bar', 'Dictionary(Int32, Utf8)');
----
foobar

# dictionary(int32, largeUtf8) -> largeUtf8
query T
select arrow_cast('foo', 'Dictionary(Int32, LargeUtf8)') || arrow_cast('bar', 'Dictionary(Int32, LargeUtf8)');
----
foobar

####################################
## Concat column dictionary test  ##
####################################
statement ok
create table t as values (arrow_cast('foo', 'Dictionary(Int32, Utf8)'), arrow_cast('bar', 'Dictionary(Int32, Utf8)'));

query T
select column1 || column2 from t;
----
foobar

statement ok
DROP TABLE t

#######################################
## Concat column dictionary test end ##
#######################################

####################################
## Test type coercion with UNIONs ##
####################################

# Disable optimizer to test only the analyzer with type coercion
statement ok
set datafusion.optimizer.max_passes = 0;

statement ok
set datafusion.explain.logical_plan_only = true;

# Create test table
statement ok
CREATE TABLE orders(
    order_id INT UNSIGNED NOT NULL,
    customer_id INT UNSIGNED NOT NULL,
    o_item_id VARCHAR NOT NULL,
    qty INT NOT NULL,
    price DOUBLE NOT NULL,
    delivered BOOLEAN NOT NULL
);

# union_different_num_columns_error() / UNION
query error DataFusion error: Error during planning: UNION queries have different number of columns
SELECT order_id FROM orders UNION SELECT customer_id, o_item_id FROM orders

# union_different_num_columns_error() / UNION ALL
query error DataFusion error: Error during planning: UNION queries have different number of columns
SELECT order_id FROM orders UNION ALL SELECT customer_id, o_item_id FROM orders

# union_with_different_column_names()
query TT
EXPLAIN SELECT order_id from orders UNION ALL SELECT customer_id FROM orders
----
logical_plan
01)Union
02)--Projection: orders.order_id
03)----TableScan: orders
04)--Projection: orders.customer_id AS order_id
05)----TableScan: orders

# union_values_with_no_alias()
query TT
EXPLAIN SELECT 1, 2 UNION ALL SELECT 3, 4
----
logical_plan
01)Union
02)--Projection: Int64(1) AS Int64(1), Int64(2) AS Int64(2)
03)----EmptyRelation
04)--Projection: Int64(3) AS Int64(1), Int64(4) AS Int64(2)
05)----EmptyRelation

# union_with_incompatible_data_type()
query error Incompatible inputs for Union: Previous inputs were of type Interval\(MonthDayNano\), but got incompatible type Int64 on column 'Int64\(1\)'
SELECT interval '1 year 1 day' UNION ALL SELECT 1

# union_with_different_decimal_data_types()
query TT
EXPLAIN SELECT 1 a UNION ALL SELECT 1.1 a
----
logical_plan
01)Union
02)--Projection: CAST(Int64(1) AS Float64) AS a
03)----EmptyRelation
04)--Projection: Float64(1.1) AS a
05)----EmptyRelation

# union_with_null()
query TT
EXPLAIN SELECT NULL a UNION ALL SELECT 1.1 a
----
logical_plan
01)Union
02)--Projection: CAST(NULL AS Float64) AS a
03)----EmptyRelation
04)--Projection: Float64(1.1) AS a
05)----EmptyRelation

# union_with_float_and_string()
query TT
EXPLAIN SELECT 'a' a UNION ALL SELECT 1.1 a
----
logical_plan
01)Union
02)--Projection: Utf8("a") AS a
03)----EmptyRelation
04)--Projection: CAST(Float64(1.1) AS Utf8) AS a
05)----EmptyRelation

# union_with_multiply_cols()
query TT
EXPLAIN SELECT 'a' a, 1 b UNION ALL SELECT 1.1 a, 1.1 b
----
logical_plan
01)Union
02)--Projection: Utf8("a") AS a, CAST(Int64(1) AS Float64) AS b
03)----EmptyRelation
04)--Projection: CAST(Float64(1.1) AS Utf8) AS a, Float64(1.1) AS b
05)----EmptyRelation

# sorted_union_with_different_types_and_group_by()
query TT
EXPLAIN SELECT a FROM (select 1 a) x GROUP BY 1
    UNION ALL
(SELECT a FROM (select 1.1 a) x GROUP BY 1) ORDER BY 1
----
logical_plan
01)Sort: a ASC NULLS LAST
02)--Union
03)----Projection: CAST(x.a AS Float64) AS a
04)------Aggregate: groupBy=[[x.a]], aggr=[[]]
05)--------SubqueryAlias: x
06)----------Projection: Int64(1) AS a
07)------------EmptyRelation
08)----Projection: x.a
09)------Aggregate: groupBy=[[x.a]], aggr=[[]]
10)--------SubqueryAlias: x
11)----------Projection: Float64(1.1) AS a
12)------------EmptyRelation

# union_with_binary_expr_and_cast()
query TT
EXPLAIN SELECT cast(0.0 + a as integer) FROM (select 1 a) x GROUP BY 1
    UNION ALL
(SELECT 2.1 + a FROM (select 1 a) x GROUP BY 1)
----
logical_plan
01)Union
02)--Projection: CAST(Float64(0) + x.a AS Float64) AS Float64(0) + x.a
03)----Aggregate: groupBy=[[CAST(Float64(0) + CAST(x.a AS Float64) AS Int32)]], aggr=[[]]
04)------SubqueryAlias: x
05)--------Projection: Int64(1) AS a
06)----------EmptyRelation
07)--Projection: Float64(2.1) + x.a AS Float64(0) + x.a
08)----Aggregate: groupBy=[[Float64(2.1) + CAST(x.a AS Float64)]], aggr=[[]]
09)------SubqueryAlias: x
10)--------Projection: Int64(1) AS a
11)----------EmptyRelation

# union_with_aliases()
query TT
EXPLAIN SELECT a as a1 FROM (select 1 a) x GROUP BY 1
    UNION ALL
(SELECT a as a1 FROM (select 1.1 a) x GROUP BY 1)
----
logical_plan
01)Union
02)--Projection: CAST(x.a AS Float64) AS a1
03)----Aggregate: groupBy=[[x.a]], aggr=[[]]
04)------SubqueryAlias: x
05)--------Projection: Int64(1) AS a
06)----------EmptyRelation
07)--Projection: x.a AS a1
08)----Aggregate: groupBy=[[x.a]], aggr=[[]]
09)------SubqueryAlias: x
10)--------Projection: Float64(1.1) AS a
11)----------EmptyRelation

# union_with_incompatible_data_types()
query error Incompatible inputs for Union: Previous inputs were of type Utf8, but got incompatible type Boolean on column 'a'
SELECT 'a' a UNION ALL SELECT true a

statement ok
SET datafusion.optimizer.max_passes = 3;

statement ok
SET datafusion.explain.logical_plan_only = false;

statement ok
DROP TABLE orders;

########################################
## Test type coercion with UNIONs end ##
########################################
