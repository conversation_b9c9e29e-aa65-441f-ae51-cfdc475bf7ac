# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

#############
## Arrow Files Format support
#############


statement ok

CREATE EXTERNAL TABLE arrow_simple
STORED AS ARROW
LOCATION '../core/tests/data/example.arrow';


# physical plan
query TT
EXPLAIN SELECT * FROM arrow_simple
----
logical_plan TableScan: arrow_simple projection=[f0, f1, f2]
physical_plan DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/example.arrow]]}, projection=[f0, f1, f2], file_type=arrow

# correct content
query ITB
SELECT * FROM arrow_simple
----
1 foo true
2 bar NULL
3 baz false
4 NULL true

# Ensure that local files can not be read by default (a potential security issue)
# (url table is only supported when DynamicFileCatalog is enabled)
statement error DataFusion error: Error during planning: table 'datafusion.public.../core/tests/data/example.arrow' not found
SELECT * FROM '../core/tests/data/example.arrow';

# ARROW partitioned table
statement ok
CREATE EXTERNAL TABLE arrow_partitioned (
    part Int,
    f0 Bigint,
    f1 String,
    f2 Boolean
)
STORED AS ARROW
LOCATION '../core/tests/data/partitioned_table_arrow/'
PARTITIONED BY (part);

# select wildcard
query ITBI
SELECT * FROM arrow_partitioned ORDER BY f0;
----
1 foo true 123
2 bar false 123
3 baz true 456
4 NULL NULL 456

# select all fields
query IITB
SELECT part, f0, f1, f2 FROM arrow_partitioned ORDER BY f0;
----
123 1 foo true
123 2 bar false
456 3 baz true
456 4 NULL NULL

# select without partition column
query IB
SELECT f0, f2 FROM arrow_partitioned ORDER BY f0
----
1 true
2 false
3 true
4 NULL

# select only partition column
query I
SELECT part FROM arrow_partitioned ORDER BY part
----
123
123
456
456

# select without any table-related columns in projection
query I
SELECT 1 FROM arrow_partitioned
----
1
1
1
1

# select with partition filter
query I
SELECT f0 FROM arrow_partitioned WHERE part = 123 ORDER BY f0
----
1
2

# select with partition filter should scan only one directory
query TT
EXPLAIN SELECT f0 FROM arrow_partitioned WHERE part = 456
----
logical_plan TableScan: arrow_partitioned projection=[f0], full_filters=[arrow_partitioned.part = Int32(456)]
physical_plan DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/partitioned_table_arrow/part=456/data.arrow]]}, projection=[f0], file_type=arrow


# Errors in partition filters should be reported
query error Divide by zero error
SELECT f0 FROM arrow_partitioned WHERE CASE WHEN true THEN 1 / 0 ELSE part END = 1;
