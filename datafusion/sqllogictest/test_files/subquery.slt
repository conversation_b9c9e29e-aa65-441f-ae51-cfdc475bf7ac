# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# make sure to a batch size smaller than row number of the table.
statement ok
set datafusion.execution.batch_size = 2;

#############
## Subquery Tests
#############


#############
## Setup test data table
#############
# there tables for subquery
statement ok
CREATE TABLE t0(t0_id INT, t0_name TEXT, t0_int INT) AS VALUES
(11, 'o', 6),
(22, 'p', 7),
(33, 'q', 8),
(44, 'r', 9);

statement ok
CREATE TABLE t1(t1_id INT, t1_name TEXT, t1_int INT) AS VALUES
(11, 'a', 1),
(22, 'b', 2),
(33, 'c', 3),
(44, 'd', 4);

statement ok
CREATE TABLE t2(t2_id INT, t2_name TEXT, t2_int INT) AS VALUES
(11, 'z', 3),
(22, 'y', 1),
(44, 'x', 3),
(55, 'w', 3);

statement ok
CREATE TABLE t3(t3_id INT PRIMARY KEY, t3_name TEXT, t3_int INT) AS VALUES
(11, 'e', 3),
(22, 'f', 1),
(44, 'g', 3),
(55, 'h', 3);

statement ok
CREATE EXTERNAL TABLE IF NOT EXISTS customer (
        c_custkey BIGINT,
        c_name VARCHAR,
        c_address VARCHAR,
        c_nationkey BIGINT,
        c_phone VARCHAR,
        c_acctbal DECIMAL(15, 2),
        c_mktsegment VARCHAR,
        c_comment VARCHAR,
) STORED AS CSV LOCATION '../core/tests/tpch-csv/customer.csv' OPTIONS ('format.delimiter' ',', 'format.has_header' 'true');

statement ok
CREATE EXTERNAL TABLE IF NOT EXISTS orders (
        o_orderkey BIGINT,
        o_custkey BIGINT,
        o_orderstatus VARCHAR,
        o_totalprice DECIMAL(15, 2),
        o_orderdate DATE,
        o_orderpriority VARCHAR,
        o_clerk VARCHAR,
        o_shippriority INTEGER,
        o_comment VARCHAR,
) STORED AS CSV LOCATION '../core/tests/tpch-csv/orders.csv' OPTIONS ('format.delimiter' ',', 'format.has_header' 'true');

statement ok
CREATE EXTERNAL TABLE IF NOT EXISTS lineitem (
        l_orderkey BIGINT,
        l_partkey BIGINT,
        l_suppkey BIGINT,
        l_linenumber INTEGER,
        l_quantity DECIMAL(15, 2),
        l_extendedprice DECIMAL(15, 2),
        l_discount DECIMAL(15, 2),
        l_tax DECIMAL(15, 2),
        l_returnflag VARCHAR,
        l_linestatus VARCHAR,
        l_shipdate DATE,
        l_commitdate DATE,
        l_receiptdate DATE,
        l_shipinstruct VARCHAR,
        l_shipmode VARCHAR,
        l_comment VARCHAR,
) STORED AS CSV LOCATION '../core/tests/tpch-csv/lineitem.csv' OPTIONS ('format.delimiter' ',', 'format.has_header' 'true');

# in_subquery_to_join_with_correlated_outer_filter
query ITI rowsort
select t1.t1_id,
       t1.t1_name,
       t1.t1_int
from t1
where t1.t1_id + 12 in (
                           select t2.t2_id + 1 from t2 where t1.t1_int > 0
                       )
----
11 a 1
33 c 3
44 d 4

# not_in_subquery_to_join_with_correlated_outer_filter
query ITI rowsort
select t1.t1_id,
       t1.t1_name,
       t1.t1_int
from t1
where t1.t1_id + 12 not in (
                               select t2.t2_id + 1 from t2 where t1.t1_int > 0
                           )
----
22 b 2

# wrapped_not_in_subquery_to_join_with_correlated_outer_filter
query ITI rowsort
select t1.t1_id,
       t1.t1_name,
       t1.t1_int
from t1
where not t1.t1_id + 12 in (
                               select t2.t2_id + 1 from t2 where t1.t1_int > 0
                           )
----
22 b 2


# in subquery with two parentheses, see #5529
query ITI rowsort
select t1.t1_id,
       t1.t1_name,
       t1.t1_int
from t1
where t1.t1_id in ((
                       select t2.t2_id from t2
                  ))
----
11 a 1
22 b 2
44 d 4

query ITI rowsort
select t1.t1_id,
       t1.t1_name,
       t1.t1_int
from t1
where t1.t1_id in ((
                       select t2.t2_id from t2
                  ))
and t1.t1_int < 3
----
11 a 1
22 b 2

query ITI rowsort
select t1.t1_id,
       t1.t1_name,
       t1.t1_int
from t1
where t1.t1_id not in ((
                            select t2.t2_id from t2 where t2.t2_int = 3
                      ))
----
22 b 2
33 c 3

# VALUES in subqueries, see 6017
query I
select t1_id
from t1
where t1_int = (select max(i) from (values (1)) as s(i));
----
11

# aggregated_correlated_scalar_subquery
query TT
explain SELECT t1_id, (SELECT sum(t2_int) FROM t2 WHERE t2.t2_id = t1.t1_id) as t2_sum from t1
----
logical_plan
01)Projection: t1.t1_id, __scalar_sq_1.sum(t2.t2_int) AS t2_sum
02)--Left Join: t1.t1_id = __scalar_sq_1.t2_id
03)----TableScan: t1 projection=[t1_id]
04)----SubqueryAlias: __scalar_sq_1
05)------Projection: sum(t2.t2_int), t2.t2_id
06)--------Aggregate: groupBy=[[t2.t2_id]], aggr=[[sum(CAST(t2.t2_int AS Int64))]]
07)----------TableScan: t2 projection=[t2_id, t2_int]
physical_plan
01)ProjectionExec: expr=[t1_id@1 as t1_id, sum(t2.t2_int)@0 as t2_sum]
02)--CoalesceBatchesExec: target_batch_size=2
03)----HashJoinExec: mode=CollectLeft, join_type=Right, on=[(t2_id@1, t1_id@0)], projection=[sum(t2.t2_int)@0, t1_id@2]
04)------CoalescePartitionsExec
05)--------ProjectionExec: expr=[sum(t2.t2_int)@1 as sum(t2.t2_int), t2_id@0 as t2_id]
06)----------AggregateExec: mode=FinalPartitioned, gby=[t2_id@0 as t2_id], aggr=[sum(t2.t2_int)]
07)------------CoalesceBatchesExec: target_batch_size=2
08)--------------RepartitionExec: partitioning=Hash([t2_id@0], 4), input_partitions=4
09)----------------AggregateExec: mode=Partial, gby=[t2_id@0 as t2_id], aggr=[sum(t2.t2_int)]
10)------------------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
11)--------------------DataSourceExec: partitions=1, partition_sizes=[1]
12)------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
13)--------DataSourceExec: partitions=1, partition_sizes=[1]

query II rowsort
SELECT t1_id, (SELECT sum(t2_int) FROM t2 WHERE t2.t2_id = t1.t1_id) as t2_sum from t1
----
11 3
22 1
33 NULL
44 3

# aggregated_correlated_scalar_subquery_with_cast
query TT
explain SELECT t1_id, (SELECT sum(t2_int * 1.0) + 1 FROM t2 WHERE t2.t2_id = t1.t1_id) as t2_sum from t1
----
logical_plan
01)Projection: t1.t1_id, __scalar_sq_1.sum(t2.t2_int * Float64(1)) + Int64(1) AS t2_sum
02)--Left Join: t1.t1_id = __scalar_sq_1.t2_id
03)----TableScan: t1 projection=[t1_id]
04)----SubqueryAlias: __scalar_sq_1
05)------Projection: sum(t2.t2_int * Float64(1)) + Float64(1) AS sum(t2.t2_int * Float64(1)) + Int64(1), t2.t2_id
06)--------Aggregate: groupBy=[[t2.t2_id]], aggr=[[sum(CAST(t2.t2_int AS Float64)) AS sum(t2.t2_int * Float64(1))]]
07)----------TableScan: t2 projection=[t2_id, t2_int]
physical_plan
01)ProjectionExec: expr=[t1_id@1 as t1_id, sum(t2.t2_int * Float64(1)) + Int64(1)@0 as t2_sum]
02)--CoalesceBatchesExec: target_batch_size=2
03)----HashJoinExec: mode=CollectLeft, join_type=Right, on=[(t2_id@1, t1_id@0)], projection=[sum(t2.t2_int * Float64(1)) + Int64(1)@0, t1_id@2]
04)------CoalescePartitionsExec
05)--------ProjectionExec: expr=[sum(t2.t2_int * Float64(1))@1 + 1 as sum(t2.t2_int * Float64(1)) + Int64(1), t2_id@0 as t2_id]
06)----------AggregateExec: mode=FinalPartitioned, gby=[t2_id@0 as t2_id], aggr=[sum(t2.t2_int * Float64(1))]
07)------------CoalesceBatchesExec: target_batch_size=2
08)--------------RepartitionExec: partitioning=Hash([t2_id@0], 4), input_partitions=4
09)----------------AggregateExec: mode=Partial, gby=[t2_id@0 as t2_id], aggr=[sum(t2.t2_int * Float64(1))]
10)------------------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
11)--------------------DataSourceExec: partitions=1, partition_sizes=[1]
12)------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
13)--------DataSourceExec: partitions=1, partition_sizes=[1]

query IR rowsort
SELECT t1_id, (SELECT sum(t2_int * 1.0) + 1 FROM t2 WHERE t2.t2_id = t1.t1_id) as t2_sum from t1
----
11 4
22 2
33 NULL
44 4

# aggregated_correlated_scalar_subquery_with_extra_group_by_constant
query TT
explain SELECT t1_id, (SELECT sum(t2_int) FROM t2 WHERE t2.t2_id = t1.t1_id group by t2_id, 'a') as t2_sum from t1
----
logical_plan
01)Projection: t1.t1_id, __scalar_sq_1.sum(t2.t2_int) AS t2_sum
02)--Left Join: t1.t1_id = __scalar_sq_1.t2_id
03)----TableScan: t1 projection=[t1_id]
04)----SubqueryAlias: __scalar_sq_1
05)------Projection: sum(t2.t2_int), t2.t2_id
06)--------Aggregate: groupBy=[[t2.t2_id]], aggr=[[sum(CAST(t2.t2_int AS Int64))]]
07)----------TableScan: t2 projection=[t2_id, t2_int]
physical_plan
01)ProjectionExec: expr=[t1_id@1 as t1_id, sum(t2.t2_int)@0 as t2_sum]
02)--CoalesceBatchesExec: target_batch_size=2
03)----HashJoinExec: mode=CollectLeft, join_type=Right, on=[(t2_id@1, t1_id@0)], projection=[sum(t2.t2_int)@0, t1_id@2]
04)------CoalescePartitionsExec
05)--------ProjectionExec: expr=[sum(t2.t2_int)@1 as sum(t2.t2_int), t2_id@0 as t2_id]
06)----------AggregateExec: mode=FinalPartitioned, gby=[t2_id@0 as t2_id], aggr=[sum(t2.t2_int)]
07)------------CoalesceBatchesExec: target_batch_size=2
08)--------------RepartitionExec: partitioning=Hash([t2_id@0], 4), input_partitions=4
09)----------------AggregateExec: mode=Partial, gby=[t2_id@0 as t2_id], aggr=[sum(t2.t2_int)]
10)------------------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
11)--------------------DataSourceExec: partitions=1, partition_sizes=[1]
12)------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
13)--------DataSourceExec: partitions=1, partition_sizes=[1]

query II rowsort
SELECT t1_id, (SELECT sum(t2_int) FROM t2 WHERE t2.t2_id = t1.t1_id group by t2_id, 'a') as t2_sum from t1
----
11 3
22 1
33 NULL
44 3

# aggregated_correlated_scalar_subquery_with_having
query TT
explain SELECT t1_id, (SELECT sum(t2_int) FROM t2 WHERE t2.t2_id = t1.t1_id having sum(t2_int) < 3) as t2_sum from t1
----
logical_plan
01)Projection: t1.t1_id, __scalar_sq_1.sum(t2.t2_int) AS t2_sum
02)--Left Join: t1.t1_id = __scalar_sq_1.t2_id
03)----TableScan: t1 projection=[t1_id]
04)----SubqueryAlias: __scalar_sq_1
05)------Projection: sum(t2.t2_int), t2.t2_id
06)--------Filter: sum(t2.t2_int) < Int64(3)
07)----------Aggregate: groupBy=[[t2.t2_id]], aggr=[[sum(CAST(t2.t2_int AS Int64))]]
08)------------TableScan: t2 projection=[t2_id, t2_int]
physical_plan
01)ProjectionExec: expr=[t1_id@1 as t1_id, sum(t2.t2_int)@0 as t2_sum]
02)--CoalesceBatchesExec: target_batch_size=2
03)----HashJoinExec: mode=CollectLeft, join_type=Right, on=[(t2_id@1, t1_id@0)], projection=[sum(t2.t2_int)@0, t1_id@2]
04)------CoalescePartitionsExec
05)--------ProjectionExec: expr=[sum(t2.t2_int)@1 as sum(t2.t2_int), t2_id@0 as t2_id]
06)----------CoalesceBatchesExec: target_batch_size=2
07)------------FilterExec: sum(t2.t2_int)@1 < 3
08)--------------AggregateExec: mode=FinalPartitioned, gby=[t2_id@0 as t2_id], aggr=[sum(t2.t2_int)]
09)----------------CoalesceBatchesExec: target_batch_size=2
10)------------------RepartitionExec: partitioning=Hash([t2_id@0], 4), input_partitions=4
11)--------------------AggregateExec: mode=Partial, gby=[t2_id@0 as t2_id], aggr=[sum(t2.t2_int)]
12)----------------------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
13)------------------------DataSourceExec: partitions=1, partition_sizes=[1]
14)------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
15)--------DataSourceExec: partitions=1, partition_sizes=[1]

query II rowsort
SELECT t1_id, (SELECT sum(t2_int) FROM t2 WHERE t2.t2_id = t1.t1_id having sum(t2_int) < 3) as t2_sum from t1
----
11 NULL
22 1
33 NULL
44 NULL


statement ok
set datafusion.explain.logical_plan_only = true;

# correlated_recursive_scalar_subquery
query TT
explain select c_custkey from customer
where c_acctbal < (
    select sum(o_totalprice) from orders
    where o_custkey = c_custkey
    and o_totalprice < (
            select sum(l_extendedprice) as price from lineitem where l_orderkey = o_orderkey
    )
) order by c_custkey;
----
logical_plan
01)Sort: customer.c_custkey ASC NULLS LAST
02)--Projection: customer.c_custkey
03)----Inner Join: customer.c_custkey = __scalar_sq_1.o_custkey Filter: CAST(customer.c_acctbal AS Decimal128(25, 2)) < __scalar_sq_1.sum(orders.o_totalprice)
04)------TableScan: customer projection=[c_custkey, c_acctbal]
05)------SubqueryAlias: __scalar_sq_1
06)--------Projection: sum(orders.o_totalprice), orders.o_custkey
07)----------Aggregate: groupBy=[[orders.o_custkey]], aggr=[[sum(orders.o_totalprice)]]
08)------------Projection: orders.o_custkey, orders.o_totalprice
09)--------------Inner Join: orders.o_orderkey = __scalar_sq_2.l_orderkey Filter: CAST(orders.o_totalprice AS Decimal128(25, 2)) < __scalar_sq_2.price
10)----------------TableScan: orders projection=[o_orderkey, o_custkey, o_totalprice]
11)----------------SubqueryAlias: __scalar_sq_2
12)------------------Projection: sum(lineitem.l_extendedprice) AS price, lineitem.l_orderkey
13)--------------------Aggregate: groupBy=[[lineitem.l_orderkey]], aggr=[[sum(lineitem.l_extendedprice)]]
14)----------------------TableScan: lineitem projection=[l_orderkey, l_extendedprice]

# correlated_where_in
query TT
explain select o_orderkey from orders
where o_orderstatus in (
    select l_linestatus from lineitem where l_orderkey = orders.o_orderkey
);
----
logical_plan
01)Projection: orders.o_orderkey
02)--LeftSemi Join: orders.o_orderstatus = __correlated_sq_1.l_linestatus, orders.o_orderkey = __correlated_sq_1.l_orderkey
03)----TableScan: orders projection=[o_orderkey, o_orderstatus]
04)----SubqueryAlias: __correlated_sq_1
05)------Projection: lineitem.l_linestatus, lineitem.l_orderkey
06)--------TableScan: lineitem projection=[l_orderkey, l_linestatus]

query I rowsort
select o_orderkey from orders
where o_orderstatus in (
    select l_linestatus from lineitem where l_orderkey = orders.o_orderkey
);
----
2
3

# uncorrelated exists
query I
SELECT 1 WHERE EXISTS (SELECT 1)
----
1

#exists_subquery_with_same_table
#Subquery and outer query refer to the same table.
query TT
explain SELECT t1_id, t1_name, t1_int FROM t1 WHERE EXISTS(SELECT t1_int FROM t1 WHERE t1.t1_id > t1.t1_int)
----
logical_plan
01)LeftSemi Join: 
02)--TableScan: t1 projection=[t1_id, t1_name, t1_int]
03)--SubqueryAlias: __correlated_sq_1
04)----Projection: 
05)------Filter: t1.t1_int < t1.t1_id
06)--------TableScan: t1 projection=[t1_id, t1_int]


#in_subquery_with_same_table
#Subquery and outer query refer to the same table.
#It will be rewritten to join because in-subquery has extra predicate(`t1.t1_id = __correlated_sq_10.t1_int`).
query TT
explain SELECT t1_id, t1_name, t1_int FROM t1 WHERE t1_id IN(SELECT t1_int FROM t1 WHERE t1.t1_id > t1.t1_int)
----
logical_plan
01)LeftSemi Join: t1.t1_id = __correlated_sq_1.t1_int
02)--TableScan: t1 projection=[t1_id, t1_name, t1_int]
03)--SubqueryAlias: __correlated_sq_1
04)----Projection: t1.t1_int
05)------Filter: t1.t1_int < t1.t1_id
06)--------TableScan: t1 projection=[t1_id, t1_int]

#in_subquery_nested_exist_subquery
query TT
explain SELECT t1_id, t1_name, t1_int FROM t1 WHERE t1_id IN(SELECT t2_id FROM t2 WHERE EXISTS(select * from t1 WHERE t1.t1_int > t2.t2_int))
----
logical_plan
01)LeftSemi Join: t1.t1_id = __correlated_sq_2.t2_id
02)--TableScan: t1 projection=[t1_id, t1_name, t1_int]
03)--SubqueryAlias: __correlated_sq_2
04)----Projection: t2.t2_id
05)------LeftSemi Join:  Filter: __correlated_sq_1.t1_int > t2.t2_int
06)--------TableScan: t2 projection=[t2_id, t2_int]
07)--------SubqueryAlias: __correlated_sq_1
08)----------TableScan: t1 projection=[t1_int]

#invalid_scalar_subquery
statement error DataFusion error: Error during planning: Too many columns! The subquery should only return one column: t2.t2_id, t2.t2_name
SELECT t1_id, t1_name, t1_int, (select t2_id, t2_name FROM t2 WHERE t2.t2_id = t1.t1_int) FROM t1

#subquery_not_allowed
#In/Exist Subquery is not allowed in ORDER BY clause.
statement error DataFusion error: Invalid \(non-executable\) plan after Analyzer\ncaused by\nError during planning: In/Exist subquery can only be used in Projection, Filter, TableScan, Window functions, Aggregate and Join plan nodes, but was used in \[Sort: t1.t1_int IN \(<subquery>\) ASC NULLS LAST\]
SELECT t1_id, t1_name, t1_int FROM t1 order by t1_int in (SELECT t2_int FROM t2 WHERE t1.t1_id > t1.t1_int)

#non_aggregated_correlated_scalar_subquery
statement error DataFusion error: Invalid \(non-executable\) plan after Analyzer\ncaused by\nError during planning: Correlated scalar subquery must be aggregated to return at most one row
SELECT t1_id, (SELECT t2_int FROM t2 WHERE t2.t2_int = t1.t1_int) as t2_int from t1

#non_aggregated_correlated_scalar_subquery_unique
query II rowsort
SELECT t1_id, (SELECT t3_int FROM t3 WHERE t3.t3_id = t1.t1_id) as t3_int from t1
----
11 3
22 1
33 NULL
44 3


#non_aggregated_correlated_scalar_subquery
statement error DataFusion error: Invalid \(non-executable\) plan after Analyzer\ncaused by\nError during planning: Correlated scalar subquery must be aggregated to return at most one row
SELECT t1_id, (SELECT t2_int FROM t2 WHERE t2.t2_int = t1_int group by t2_int) as t2_int from t1

#non_aggregated_correlated_scalar_subquery_with_limit
statement error DataFusion error: Invalid \(non-executable\) plan after Analyzer\ncaused by\nError during planning: Correlated scalar subquery must be aggregated to return at most one row
SELECT t1_id, (SELECT t2_int FROM t2 WHERE t2.t2_int = t1.t1_int limit 2) as t2_int from t1

#non_aggregated_correlated_scalar_subquery_with_single_row
query TT
explain SELECT t1_id, (SELECT t2_int FROM t2 WHERE t2.t2_int = t1.t1_int limit 1) as t2_int from t1
----
logical_plan
01)Projection: t1.t1_id, (<subquery>) AS t2_int
02)--Subquery:
03)----Projection: t2.t2_int
04)------Limit: skip=0, fetch=1
05)--------Filter: t2.t2_int = outer_ref(t1.t1_int)
06)----------TableScan: t2
07)--TableScan: t1 projection=[t1_id, t1_int]

query TT
explain SELECT t1_id from t1 where t1_int = (SELECT t2_int FROM t2 WHERE t2.t2_int = t1.t1_int limit 1)
----
logical_plan
01)Projection: t1.t1_id
02)--Filter: t1.t1_int = (<subquery>)
03)----Subquery:
04)------Projection: t2.t2_int
05)--------Limit: skip=0, fetch=1
06)----------Filter: t2.t2_int = outer_ref(t1.t1_int)
07)------------TableScan: t2
08)----TableScan: t1 projection=[t1_id, t1_int]

query TT
explain SELECT t1_id, (SELECT a FROM (select 1 as a) WHERE a = t1.t1_int) as t2_int from t1
----
logical_plan
01)Projection: t1.t1_id, __scalar_sq_1.a AS t2_int
02)--Left Join: CAST(t1.t1_int AS Int64) = __scalar_sq_1.a
03)----TableScan: t1 projection=[t1_id, t1_int]
04)----SubqueryAlias: __scalar_sq_1
05)------Projection: Int64(1) AS a
06)--------EmptyRelation

query II rowsort
SELECT t1_id, (SELECT a FROM (select 1 as a) WHERE a = t1.t1_int) as t2_int from t1
----
11 1
22 NULL
33 NULL
44 NULL

#non_equal_correlated_scalar_subquery
# Currently not supported and should not be decorrelated
query TT
explain SELECT t1_id, (SELECT sum(t2_int) FROM t2 WHERE t2.t2_id < t1.t1_id) as t2_sum from t1
----
logical_plan
01)Projection: t1.t1_id, (<subquery>) AS t2_sum
02)--Subquery:
03)----Projection: sum(t2.t2_int)
04)------Aggregate: groupBy=[[]], aggr=[[sum(CAST(t2.t2_int AS Int64))]]
05)--------Filter: t2.t2_id < outer_ref(t1.t1_id)
06)----------TableScan: t2
07)--TableScan: t1 projection=[t1_id]

#aggregated_correlated_scalar_subquery_with_extra_group_by_columns
statement error DataFusion error: Invalid \(non-executable\) plan after Analyzer\ncaused by\nError during planning: A GROUP BY clause in a scalar correlated subquery cannot contain non-correlated columns
SELECT t1_id, (SELECT sum(t2_int) FROM t2 WHERE t2.t2_id = t1.t1_id group by t2_name) as t2_sum from t1

#support_agg_correlated_columns
query TT
explain SELECT t1_id, t1_name FROM t1 WHERE EXISTS (SELECT sum(t1.t1_int + t2.t2_id) FROM t2 WHERE t1.t1_name = t2.t2_name)
----
logical_plan
01)Projection: t1.t1_id, t1.t1_name
02)--Filter: EXISTS (<subquery>)
03)----Subquery:
04)------Projection: sum(outer_ref(t1.t1_int) + t2.t2_id)
05)--------Aggregate: groupBy=[[]], aggr=[[sum(CAST(outer_ref(t1.t1_int) + t2.t2_id AS Int64))]]
06)----------Filter: outer_ref(t1.t1_name) = t2.t2_name
07)------------TableScan: t2
08)----TableScan: t1 projection=[t1_id, t1_name, t1_int]

#support_agg_correlated_columns2
query TT
explain SELECT t1_id, t1_name FROM t1 WHERE EXISTS (SELECT count(*) FROM t2 WHERE t1.t1_name = t2.t2_name having sum(t1_int + t2_id) >0)
----
logical_plan
01)Projection: t1.t1_id, t1.t1_name
02)--Filter: EXISTS (<subquery>)
03)----Subquery:
04)------Projection: count(Int64(1)) AS count(*)
05)--------Filter: sum(outer_ref(t1.t1_int) + t2.t2_id) > Int64(0)
06)----------Aggregate: groupBy=[[]], aggr=[[count(Int64(1)), sum(CAST(outer_ref(t1.t1_int) + t2.t2_id AS Int64))]]
07)------------Filter: outer_ref(t1.t1_name) = t2.t2_name
08)--------------TableScan: t2
09)----TableScan: t1 projection=[t1_id, t1_name, t1_int]

#support_join_correlated_columns
query TT
explain SELECT t0_id, t0_name FROM t0 WHERE EXISTS (SELECT 1 FROM t1 INNER JOIN t2 ON(t1.t1_id = t2.t2_id and t1.t1_name = t0.t0_name))
----
logical_plan
01)LeftSemi Join: t0.t0_name = __correlated_sq_2.t1_name
02)--TableScan: t0 projection=[t0_id, t0_name]
03)--SubqueryAlias: __correlated_sq_2
04)----Projection: t1.t1_name
05)------Inner Join: t1.t1_id = t2.t2_id
06)--------TableScan: t1 projection=[t1_id, t1_name]
07)--------TableScan: t2 projection=[t2_id]

#subquery_contains_join_contains_correlated_columns
query TT
explain SELECT t0_id, t0_name FROM t0 WHERE EXISTS (SELECT 1 FROM t1 INNER JOIN (select * from t2 where t2.t2_name = t0.t0_name) as t2 ON(t1.t1_id = t2.t2_id ))
----
logical_plan
01)LeftSemi Join: t0.t0_name = __correlated_sq_1.t2_name
02)--TableScan: t0 projection=[t0_id, t0_name]
03)--SubqueryAlias: __correlated_sq_1
04)----Projection: t2.t2_name
05)------Inner Join: t1.t1_id = t2.t2_id
06)--------TableScan: t1 projection=[t1_id]
07)--------SubqueryAlias: t2
08)----------TableScan: t2 projection=[t2_id, t2_name]

#subquery_contains_join_contains_sub_query_alias_correlated_columns
query TT
explain SELECT t0_id, t0_name FROM t0 WHERE EXISTS (select 1 from (SELECT * FROM t1 where t1.t1_id = t0.t0_id) as x INNER JOIN (select * from t2 where t2.t2_name = t0.t0_name) as y ON(x.t1_id = y.t2_id))
----
logical_plan
01)LeftSemi Join: t0.t0_id = __correlated_sq_1.t1_id, t0.t0_name = __correlated_sq_1.t2_name
02)--TableScan: t0 projection=[t0_id, t0_name]
03)--SubqueryAlias: __correlated_sq_1
04)----Projection: x.t1_id, y.t2_name
05)------Inner Join: x.t1_id = y.t2_id
06)--------SubqueryAlias: x
07)----------TableScan: t1 projection=[t1_id]
08)--------SubqueryAlias: y
09)----------TableScan: t2 projection=[t2_id, t2_name]

#support_order_by_correlated_columns
query TT
explain SELECT t1_id, t1_name FROM t1 WHERE EXISTS (SELECT * FROM t2 WHERE t2_id >= t1_id order by t1_id)
----
logical_plan
01)Filter: EXISTS (<subquery>)
02)--Subquery:
03)----Sort: outer_ref(t1.t1_id) ASC NULLS LAST
04)------Projection: t2.t2_id, t2.t2_name, t2.t2_int
05)--------Filter: t2.t2_id >= outer_ref(t1.t1_id)
06)----------TableScan: t2
07)--TableScan: t1 projection=[t1_id, t1_name]

#exists_subquery_with_select_null
query TT
explain SELECT t1_id, t1_name FROM t1 WHERE EXISTS (SELECT NULL)
----
logical_plan
01)LeftSemi Join: 
02)--TableScan: t1 projection=[t1_id, t1_name]
03)--SubqueryAlias: __correlated_sq_1
04)----EmptyRelation

#exists_subquery_with_limit
#de-correlated, limit is removed
query TT
explain SELECT t1_id, t1_name FROM t1 WHERE EXISTS (SELECT * FROM t2 WHERE t2_id = t1_id limit 1)
----
logical_plan
01)LeftSemi Join: t1.t1_id = __correlated_sq_1.t2_id
02)--TableScan: t1 projection=[t1_id, t1_name]
03)--SubqueryAlias: __correlated_sq_1
04)----TableScan: t2 projection=[t2_id]

query IT rowsort
SELECT t1_id, t1_name FROM t1 WHERE EXISTS (SELECT * FROM t2 WHERE t2_id = t1_id limit 1)
----
11 a
22 b
44 d

#exists_subquery_with_limit0
#de-correlated, limit is removed and replaced with EmptyRelation
query TT
explain SELECT t1_id, t1_name FROM t1 WHERE EXISTS (SELECT * FROM t2 WHERE t2_id = t1_id limit 0)
----
logical_plan EmptyRelation

query IT rowsort
SELECT t1_id, t1_name FROM t1 WHERE EXISTS (SELECT * FROM t2 WHERE t2_id = t1_id limit 0)
----


#not_exists_subquery_with_limit0
#de-correlated, limit is removed and replaced with EmptyRelation
query TT
explain SELECT t1_id, t1_name FROM t1 WHERE NOT EXISTS (SELECT * FROM t2 WHERE t2_id = t1_id limit 0)
----
logical_plan TableScan: t1 projection=[t1_id, t1_name]

query IT rowsort
SELECT t1_id, t1_name FROM t1 WHERE NOT EXISTS (SELECT * FROM t2 WHERE t2_id = t1_id limit 0)
----
11 a
22 b
33 c
44 d

#in_correlated_subquery_with_limit
#not de-correlated
query TT
explain SELECT t1_id, t1_name FROM t1 WHERE t1_id in (SELECT t2_id FROM t2 where t1_name = t2_name limit 10)
----
logical_plan
01)Filter: t1.t1_id IN (<subquery>)
02)--Subquery:
03)----Projection: t2.t2_id
04)------Limit: skip=0, fetch=10
05)--------Filter: outer_ref(t1.t1_name) = t2.t2_name
06)----------TableScan: t2
07)--TableScan: t1 projection=[t1_id, t1_name]

#in_non_correlated_subquery_with_limit
#de-correlated, limit is kept
query TT
explain SELECT t1_id, t1_name FROM t1 WHERE t1_id in (SELECT t2_id FROM t2 limit 10)
----
logical_plan
01)LeftSemi Join: t1.t1_id = __correlated_sq_1.t2_id
02)--TableScan: t1 projection=[t1_id, t1_name]
03)--SubqueryAlias: __correlated_sq_1
04)----Limit: skip=0, fetch=10
05)------TableScan: t2 projection=[t2_id], fetch=10


#uncorrelated_scalar_subquery_with_limit0
query TT
explain SELECT t1_id, (SELECT t2_id FROM t2 limit 0) FROM t1
----
logical_plan
01)Projection: t1.t1_id, __scalar_sq_1.t2_id AS t2_id
02)--Left Join: 
03)----TableScan: t1 projection=[t1_id]
04)----EmptyRelation

query II rowsort
SELECT t1_id, (SELECT t2_id FROM t2 limit 0) FROM t1
----
11 NULL
22 NULL
33 NULL
44 NULL

#support_union_subquery
query TT
explain SELECT t1_id, t1_name FROM t1 WHERE EXISTS (SELECT * FROM t2 WHERE t2_id = t1_id UNION ALL SELECT * FROM t2 WHERE upper(t2_name) = upper(t1.t1_name))
----
logical_plan
01)Filter: EXISTS (<subquery>)
02)--Subquery:
03)----Union
04)------Projection: t2.t2_id, t2.t2_name, t2.t2_int
05)--------Filter: t2.t2_id = outer_ref(t1.t1_id)
06)----------TableScan: t2
07)------Projection: t2.t2_id, t2.t2_name, t2.t2_int
08)--------Filter: upper(t2.t2_name) = upper(outer_ref(t1.t1_name))
09)----------TableScan: t2
10)--TableScan: t1 projection=[t1_id, t1_name]

#simple_uncorrelated_scalar_subquery
query TT
explain select (select count(*) from t1) as b
----
logical_plan
01)Projection: __scalar_sq_1.count(*) AS b
02)--SubqueryAlias: __scalar_sq_1
03)----Projection: count(Int64(1)) AS count(*)
04)------Aggregate: groupBy=[[]], aggr=[[count(Int64(1))]]
05)--------TableScan: t1 projection=[]

#simple_uncorrelated_scalar_subquery2
query TT
explain select (select count(*) from t1) as b, (select count(1) from t2)
----
logical_plan
01)Projection: __scalar_sq_1.count(*) AS b, __scalar_sq_2.count(Int64(1)) AS count(Int64(1))
02)--Left Join: 
03)----SubqueryAlias: __scalar_sq_1
04)------Projection: count(Int64(1)) AS count(*)
05)--------Aggregate: groupBy=[[]], aggr=[[count(Int64(1))]]
06)----------TableScan: t1 projection=[]
07)----SubqueryAlias: __scalar_sq_2
08)------Aggregate: groupBy=[[]], aggr=[[count(Int64(1))]]
09)--------TableScan: t2 projection=[]

statement ok
set datafusion.explain.logical_plan_only = false;

query TT
explain select (select count(*) from t1) as b, (select count(1) from t2)
----
logical_plan
01)Projection: __scalar_sq_1.count(*) AS b, __scalar_sq_2.count(Int64(1)) AS count(Int64(1))
02)--Left Join: 
03)----SubqueryAlias: __scalar_sq_1
04)------Projection: count(Int64(1)) AS count(*)
05)--------Aggregate: groupBy=[[]], aggr=[[count(Int64(1))]]
06)----------TableScan: t1 projection=[]
07)----SubqueryAlias: __scalar_sq_2
08)------Aggregate: groupBy=[[]], aggr=[[count(Int64(1))]]
09)--------TableScan: t2 projection=[]
physical_plan
01)ProjectionExec: expr=[count(*)@0 as b, count(Int64(1))@1 as count(Int64(1))]
02)--NestedLoopJoinExec: join_type=Left
03)----ProjectionExec: expr=[4 as count(*)]
04)------PlaceholderRowExec
05)----ProjectionExec: expr=[4 as count(Int64(1))]
06)------PlaceholderRowExec

statement ok
set datafusion.explain.logical_plan_only = true;

query II
select (select count(*) from t1) as b, (select count(1) from t2)
----
4 4

#correlated_scalar_subquery_count_agg
query TT
explain SELECT t1_id, (SELECT count(*) FROM t2 WHERE t2.t2_int = t1.t1_int) from t1
----
logical_plan
01)Projection: t1.t1_id, CASE WHEN __scalar_sq_1.__always_true IS NULL THEN Int64(0) ELSE __scalar_sq_1.count(*) END AS count(*)
02)--Left Join: t1.t1_int = __scalar_sq_1.t2_int
03)----TableScan: t1 projection=[t1_id, t1_int]
04)----SubqueryAlias: __scalar_sq_1
05)------Projection: count(Int64(1)) AS count(*), t2.t2_int, Boolean(true) AS __always_true
06)--------Aggregate: groupBy=[[t2.t2_int]], aggr=[[count(Int64(1))]]
07)----------TableScan: t2 projection=[t2_int]

query II rowsort
SELECT t1_id, (SELECT count(*) FROM t2 WHERE t2.t2_int = t1.t1_int) from t1
----
11 1
22 0
33 3
44 0


#correlated_scalar_subquery_count_agg2
query TT
explain SELECT t1_id, (SELECT count(*) FROM t2 WHERE t2.t2_int = t1.t1_int) as cnt from t1
----
logical_plan
01)Projection: t1.t1_id, CASE WHEN __scalar_sq_1.__always_true IS NULL THEN Int64(0) ELSE __scalar_sq_1.count(*) END AS cnt
02)--Left Join: t1.t1_int = __scalar_sq_1.t2_int
03)----TableScan: t1 projection=[t1_id, t1_int]
04)----SubqueryAlias: __scalar_sq_1
05)------Projection: count(Int64(1)) AS count(*), t2.t2_int, Boolean(true) AS __always_true
06)--------Aggregate: groupBy=[[t2.t2_int]], aggr=[[count(Int64(1))]]
07)----------TableScan: t2 projection=[t2_int]

query II rowsort
SELECT t1_id, (SELECT count(*) FROM t2 WHERE t2.t2_int = t1.t1_int) as cnt from t1
----
11 1
22 0
33 3
44 0

#correlated_scalar_subquery_count_agg_with_alias
query TT
explain SELECT t1_id, (SELECT count(*) as _cnt FROM t2 WHERE t2.t2_int = t1.t1_int) as cnt from t1
----
logical_plan
01)Projection: t1.t1_id, CASE WHEN __scalar_sq_1.__always_true IS NULL THEN Int64(0) ELSE __scalar_sq_1._cnt END AS cnt
02)--Left Join: t1.t1_int = __scalar_sq_1.t2_int
03)----TableScan: t1 projection=[t1_id, t1_int]
04)----SubqueryAlias: __scalar_sq_1
05)------Projection: count(Int64(1)) AS _cnt, t2.t2_int, Boolean(true) AS __always_true
06)--------Aggregate: groupBy=[[t2.t2_int]], aggr=[[count(Int64(1))]]
07)----------TableScan: t2 projection=[t2_int]

query II rowsort
SELECT t1_id, (SELECT count(*) as _cnt FROM t2 WHERE t2.t2_int = t1.t1_int) as cnt from t1
----
11 1
22 0
33 3
44 0

#correlated_scalar_subquery_count_agg_complex_expr
query TT
explain SELECT t1_id, (SELECT count(*) + 2 as _cnt FROM t2 WHERE t2.t2_int = t1.t1_int) from t1
----
logical_plan
01)Projection: t1.t1_id, CASE WHEN __scalar_sq_1.__always_true IS NULL THEN Int64(2) ELSE __scalar_sq_1._cnt END AS _cnt
02)--Left Join: t1.t1_int = __scalar_sq_1.t2_int
03)----TableScan: t1 projection=[t1_id, t1_int]
04)----SubqueryAlias: __scalar_sq_1
05)------Projection: count(Int64(1)) + Int64(2) AS _cnt, t2.t2_int, Boolean(true) AS __always_true
06)--------Aggregate: groupBy=[[t2.t2_int]], aggr=[[count(Int64(1))]]
07)----------TableScan: t2 projection=[t2_int]

query II rowsort
SELECT t1_id, (SELECT count(*) + 2 as _cnt FROM t2 WHERE t2.t2_int = t1.t1_int) from t1
----
11 3
22 2
33 5
44 2

#correlated_scalar_subquery_count_agg_where_clause
query TT
explain select t1.t1_int from t1 where (select count(*) from t2 where t1.t1_id = t2.t2_id) < t1.t1_int
----
logical_plan
01)Projection: t1.t1_int
02)--Filter: CASE WHEN __scalar_sq_1.__always_true IS NULL THEN Int64(0) ELSE __scalar_sq_1.count(*) END < CAST(t1.t1_int AS Int64)
03)----Projection: t1.t1_int, __scalar_sq_1.count(*), __scalar_sq_1.__always_true
04)------Left Join: t1.t1_id = __scalar_sq_1.t2_id
05)--------TableScan: t1 projection=[t1_id, t1_int]
06)--------SubqueryAlias: __scalar_sq_1
07)----------Projection: count(Int64(1)) AS count(*), t2.t2_id, Boolean(true) AS __always_true
08)------------Aggregate: groupBy=[[t2.t2_id]], aggr=[[count(Int64(1))]]
09)--------------TableScan: t2 projection=[t2_id]

query I rowsort
select t1.t1_int from t1 where (select count(*) from t2 where t1.t1_id = t2.t2_id) < t1.t1_int
----
2
3
4

#correlated_scalar_subquery_count_agg_with_having
#the having condition is kept as the normal filter condition, no need to pull up
query TT
explain SELECT t1_id, (SELECT count(*) + 2 as cnt_plus_2 FROM t2 WHERE t2.t2_int = t1.t1_int having count(*) >1) from t1
----
logical_plan
01)Projection: t1.t1_id, __scalar_sq_1.cnt_plus_2 AS cnt_plus_2
02)--Left Join: t1.t1_int = __scalar_sq_1.t2_int
03)----TableScan: t1 projection=[t1_id, t1_int]
04)----SubqueryAlias: __scalar_sq_1
05)------Projection: count(Int64(1)) AS count(*) + Int64(2) AS cnt_plus_2, t2.t2_int
06)--------Filter: count(Int64(1)) > Int64(1)
07)----------Aggregate: groupBy=[[t2.t2_int]], aggr=[[count(Int64(1))]]
08)------------TableScan: t2 projection=[t2_int]

query II rowsort
SELECT t1_id, (SELECT count(*) + 2 as cnt_plus_2 FROM t2 WHERE t2.t2_int = t1.t1_int having count(*) >1) from t1
----
11 NULL
22 NULL
33 5
44 NULL

#correlated_scalar_subquery_count_agg_with_pull_up_having
#the having condition need to pull up and evaluated after the left out join
query TT
explain SELECT t1_id, (SELECT count(*) + 2 as cnt_plus_2 FROM t2 WHERE t2.t2_int = t1.t1_int having count(*) = 0) from t1
----
logical_plan
01)Projection: t1.t1_id, CASE WHEN __scalar_sq_1.__always_true IS NULL THEN Int64(2) WHEN __scalar_sq_1.count(Int64(1)) != Int64(0) THEN Int64(NULL) ELSE __scalar_sq_1.cnt_plus_2 END AS cnt_plus_2
02)--Left Join: t1.t1_int = __scalar_sq_1.t2_int
03)----TableScan: t1 projection=[t1_id, t1_int]
04)----SubqueryAlias: __scalar_sq_1
05)------Projection: count(Int64(1)) + Int64(2) AS cnt_plus_2, t2.t2_int, count(Int64(1)), Boolean(true) AS __always_true
06)--------Aggregate: groupBy=[[t2.t2_int]], aggr=[[count(Int64(1))]]
07)----------TableScan: t2 projection=[t2_int]

query II rowsort
SELECT t1_id, (SELECT count(*) + 2 as cnt_plus_2 FROM t2 WHERE t2.t2_int = t1.t1_int having count(*) = 0) from t1
----
11 NULL
22 2
33 NULL
44 2

#correlated_scalar_subquery_count_agg_in_having
query TT
explain select t1.t1_int from t1 group by t1.t1_int having (select count(*) from t2 where t1.t1_int = t2.t2_int) = 0
----
logical_plan
01)Projection: t1.t1_int
02)--Filter: CASE WHEN __scalar_sq_1.__always_true IS NULL THEN Int64(0) ELSE __scalar_sq_1.count(*) END = Int64(0)
03)----Projection: t1.t1_int, __scalar_sq_1.count(*), __scalar_sq_1.__always_true
04)------Left Join: t1.t1_int = __scalar_sq_1.t2_int
05)--------Aggregate: groupBy=[[t1.t1_int]], aggr=[[]]
06)----------TableScan: t1 projection=[t1_int]
07)--------SubqueryAlias: __scalar_sq_1
08)----------Projection: count(Int64(1)) AS count(*), t2.t2_int, Boolean(true) AS __always_true
09)------------Aggregate: groupBy=[[t2.t2_int]], aggr=[[count(Int64(1))]]
10)--------------TableScan: t2 projection=[t2_int]

query I rowsort
select t1.t1_int from t1 group by t1.t1_int having (select count(*) from t2 where t1.t1_int = t2.t2_int) = 0
----
2
4

#correlated_scalar_subquery_count_agg_in_nested_projection
query TT
explain select t1.t1_int from t1 where (select cnt from (select count(*) as cnt, sum(t2_int) from t2 where t1.t1_int = t2.t2_int)) = 0
----
logical_plan
01)Projection: t1.t1_int
02)--Filter: CASE WHEN __scalar_sq_1.__always_true IS NULL THEN Int64(0) ELSE __scalar_sq_1.cnt END = Int64(0)
03)----Projection: t1.t1_int, __scalar_sq_1.cnt, __scalar_sq_1.__always_true
04)------Left Join: t1.t1_int = __scalar_sq_1.t2_int
05)--------TableScan: t1 projection=[t1_int]
06)--------SubqueryAlias: __scalar_sq_1
07)----------Projection: count(Int64(1)) AS cnt, t2.t2_int, Boolean(true) AS __always_true
08)------------Aggregate: groupBy=[[t2.t2_int]], aggr=[[count(Int64(1))]]
09)--------------TableScan: t2 projection=[t2_int]


query I rowsort
select t1.t1_int from t1 where (select cnt from (select count(*) as cnt, sum(t2_int) from t2 where t1.t1_int = t2.t2_int)) = 0
----
2
4

#correlated_scalar_subquery_count_agg_in_nested_subquery
#pull up the deeply nested having condition
query TT
explain
select t1.t1_int from t1 where (
    select cnt_plus_one + 1 as cnt_plus_two from (
        select cnt + 1 as cnt_plus_one from (
            select count(*) as cnt, sum(t2_int) s from t2 where t1.t1_int = t2.t2_int having cnt = 0
        )
    )
) = 2
----
logical_plan
01)Projection: t1.t1_int
02)--Filter: CASE WHEN __scalar_sq_1.__always_true IS NULL THEN Int64(2) WHEN __scalar_sq_1.count(Int64(1)) != Int64(0) THEN Int64(NULL) ELSE __scalar_sq_1.cnt_plus_two END = Int64(2)
03)----Projection: t1.t1_int, __scalar_sq_1.cnt_plus_two, __scalar_sq_1.count(Int64(1)), __scalar_sq_1.__always_true
04)------Left Join: t1.t1_int = __scalar_sq_1.t2_int
05)--------TableScan: t1 projection=[t1_int]
06)--------SubqueryAlias: __scalar_sq_1
07)----------Projection: count(Int64(1)) + Int64(1) + Int64(1) AS cnt_plus_two, t2.t2_int, count(Int64(1)), Boolean(true) AS __always_true
08)------------Aggregate: groupBy=[[t2.t2_int]], aggr=[[count(Int64(1))]]
09)--------------TableScan: t2 projection=[t2_int]

query I rowsort
select t1.t1_int from t1 where (
    select cnt_plus_one + 1 as cnt_plus_two from (
        select cnt + 1 as cnt_plus_one from (
            select count(*) as cnt, sum(t2_int) s from t2 where t1.t1_int = t2.t2_int having cnt = 0
        )
    )
) = 2
----
2
4

#correlated_scalar_subquery_count_agg_in_case_when
query TT
explain
select t1.t1_int from t1 where
       (select case when count(*) = 1 then null else count(*) end as cnt from t2 where t2.t2_int = t1.t1_int) = 0
----
logical_plan
01)Projection: t1.t1_int
02)--Filter: CASE WHEN __scalar_sq_1.__always_true IS NULL THEN Int64(0) ELSE __scalar_sq_1.cnt END = Int64(0)
03)----Projection: t1.t1_int, __scalar_sq_1.cnt, __scalar_sq_1.__always_true
04)------Left Join: t1.t1_int = __scalar_sq_1.t2_int
05)--------TableScan: t1 projection=[t1_int]
06)--------SubqueryAlias: __scalar_sq_1
07)----------Projection: CASE WHEN count(Int64(1)) = Int64(1) THEN Int64(NULL) ELSE count(Int64(1)) END AS cnt, t2.t2_int, Boolean(true) AS __always_true
08)------------Aggregate: groupBy=[[t2.t2_int]], aggr=[[count(Int64(1))]]
09)--------------TableScan: t2 projection=[t2_int]


query I rowsort
select t1.t1_int from t1 where
       (select case when count(*) = 1 then null else count(*) end as cnt from t2 where t2.t2_int = t1.t1_int) = 0
----
2
4

query B rowsort
select t1_int > (select avg(t1_int) from t1) from t1
----
false
false
true
true

query IT rowsort
SELECT t1_id, (SELECT case when max(t2.t2_id) > 1 then 'a' else 'b' end FROM t2 WHERE t2.t2_int = t1.t1_int) x from t1
----
11 a
22 b
33 a
44 b

query IB rowsort
SELECT t1_id, (SELECT max(t2.t2_id) is null FROM t2 WHERE t2.t2_int = t1.t1_int) x from t1
----
11 false
22 true
33 false
44 true

query TT
explain SELECT t1_id, (SELECT max(t2.t2_id) is null FROM t2 WHERE t2.t2_int = t1.t1_int) x from t1
----
logical_plan
01)Projection: t1.t1_id, __scalar_sq_1.__always_true IS NULL OR __scalar_sq_1.__always_true IS NOT NULL AND __scalar_sq_1.max(t2.t2_id) IS NULL AS x
02)--Left Join: t1.t1_int = __scalar_sq_1.t2_int
03)----TableScan: t1 projection=[t1_id, t1_int]
04)----SubqueryAlias: __scalar_sq_1
05)------Projection: max(t2.t2_id) IS NULL, t2.t2_int, Boolean(true) AS __always_true
06)--------Aggregate: groupBy=[[t2.t2_int]], aggr=[[max(t2.t2_id)]]
07)----------TableScan: t2 projection=[t2_id, t2_int]

query TT
explain SELECT t1_id, (SELECT max(t2.t2_id) FROM t2 WHERE t2.t2_int = t1.t1_int) x from t1
----
logical_plan
01)Projection: t1.t1_id, __scalar_sq_1.max(t2.t2_id) AS x
02)--Left Join: t1.t1_int = __scalar_sq_1.t2_int
03)----TableScan: t1 projection=[t1_id, t1_int]
04)----SubqueryAlias: __scalar_sq_1
05)------Projection: max(t2.t2_id), t2.t2_int
06)--------Aggregate: groupBy=[[t2.t2_int]], aggr=[[max(t2.t2_id)]]
07)----------TableScan: t2 projection=[t2_id, t2_int]

# in_subquery_to_join_with_correlated_outer_filter_disjunction
query TT
explain select t1.t1_id,
       t1.t1_name,
       t1.t1_int
from t1
where t1.t1_id > 40 or t1.t1_id in (select t2.t2_id from t2 where t1.t1_int > 0)
----
logical_plan
01)Projection: t1.t1_id, t1.t1_name, t1.t1_int
02)--Filter: t1.t1_id > Int32(40) OR __correlated_sq_1.mark
03)----LeftMark Join: t1.t1_id = __correlated_sq_1.t2_id Filter: t1.t1_int > Int32(0)
04)------TableScan: t1 projection=[t1_id, t1_name, t1_int]
05)------SubqueryAlias: __correlated_sq_1
06)--------TableScan: t2 projection=[t2_id]

query ITI rowsort
select t1.t1_id,
       t1.t1_name,
       t1.t1_int
from t1
where t1.t1_id > 40 or t1.t1_id in (select t2.t2_id from t2 where t1.t1_int > 0)
----
11 a 1
22 b 2
44 d 4

# not_in_subquery_to_join_with_correlated_outer_filter_disjunction
query TT
explain select t1.t1_id,
       t1.t1_name,
       t1.t1_int
from t1
where t1.t1_id = 11 or t1.t1_id + 12 not in (select t2.t2_id + 1 from t2 where t1.t1_int > 0)
----
logical_plan
01)Projection: t1.t1_id, t1.t1_name, t1.t1_int
02)--Filter: t1.t1_id = Int32(11) OR NOT __correlated_sq_1.mark
03)----LeftMark Join: CAST(t1.t1_id AS Int64) + Int64(12) = __correlated_sq_1.t2.t2_id + Int64(1) Filter: t1.t1_int > Int32(0)
04)------TableScan: t1 projection=[t1_id, t1_name, t1_int]
05)------SubqueryAlias: __correlated_sq_1
06)--------Projection: CAST(t2.t2_id AS Int64) + Int64(1)
07)----------TableScan: t2 projection=[t2_id]

query ITI rowsort
select t1.t1_id,
       t1.t1_name,
       t1.t1_int
from t1
where t1.t1_id = 11 or t1.t1_id + 12 not in (select t2.t2_id + 1 from t2 where t1.t1_int > 0)
----
11 a 1
22 b 2

# exists_subquery_to_join_with_correlated_outer_filter_disjunction
query TT
explain select t1.t1_id,
       t1.t1_name,
       t1.t1_int
from t1
where t1.t1_id > 40 or exists (select * from t2 where t1.t1_id = t2.t2_id)
----
logical_plan
01)Projection: t1.t1_id, t1.t1_name, t1.t1_int
02)--Filter: t1.t1_id > Int32(40) OR __correlated_sq_1.mark
03)----LeftMark Join: t1.t1_id = __correlated_sq_1.t2_id
04)------TableScan: t1 projection=[t1_id, t1_name, t1_int]
05)------SubqueryAlias: __correlated_sq_1
06)--------TableScan: t2 projection=[t2_id]

query ITI rowsort
select t1.t1_id,
       t1.t1_name,
       t1.t1_int
from t1
where t1.t1_id > 40 or exists (select * from t2 where t1.t1_id = t2.t2_id)
----
11 a 1
22 b 2
44 d 4

statement ok
set datafusion.explain.logical_plan_only = false;

# not_exists_subquery_to_join_with_correlated_outer_filter_disjunction
query TT
explain select t1.t1_id,
       t1.t1_name,
       t1.t1_int
from t1
where t1.t1_id > 40 or not exists (select * from t2 where t1.t1_id = t2.t2_id)
----
logical_plan
01)Projection: t1.t1_id, t1.t1_name, t1.t1_int
02)--Filter: t1.t1_id > Int32(40) OR NOT __correlated_sq_1.mark
03)----LeftMark Join: t1.t1_id = __correlated_sq_1.t2_id
04)------TableScan: t1 projection=[t1_id, t1_name, t1_int]
05)------SubqueryAlias: __correlated_sq_1
06)--------TableScan: t2 projection=[t2_id]
physical_plan
01)CoalesceBatchesExec: target_batch_size=2
02)--FilterExec: t1_id@0 > 40 OR NOT mark@3, projection=[t1_id@0, t1_name@1, t1_int@2]
03)----CoalesceBatchesExec: target_batch_size=2
04)------HashJoinExec: mode=CollectLeft, join_type=LeftMark, on=[(t1_id@0, t2_id@0)]
05)--------DataSourceExec: partitions=1, partition_sizes=[1]
06)--------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
07)----------DataSourceExec: partitions=1, partition_sizes=[1]

statement ok
set datafusion.explain.logical_plan_only = true;

query ITI rowsort
select t1.t1_id,
       t1.t1_name,
       t1.t1_int
from t1
where t1.t1_id > 40 or not exists (select * from t2 where t1.t1_id = t2.t2_id)
----
33 c 3
44 d 4

# in_subquery_to_join_with_correlated_outer_filter_and_or
query TT
explain select t1.t1_id,
       t1.t1_name,
       t1.t1_int
from t1
where t1.t1_id in (select t3.t3_id from t3) and (t1.t1_id > 40 or t1.t1_id in (select t2.t2_id from t2 where t1.t1_int > 0))
----
logical_plan
01)Projection: t1.t1_id, t1.t1_name, t1.t1_int
02)--Filter: t1.t1_id > Int32(40) OR __correlated_sq_2.mark
03)----LeftMark Join: t1.t1_id = __correlated_sq_2.t2_id Filter: t1.t1_int > Int32(0)
04)------LeftSemi Join: t1.t1_id = __correlated_sq_1.t3_id
05)--------TableScan: t1 projection=[t1_id, t1_name, t1_int]
06)--------SubqueryAlias: __correlated_sq_1
07)----------TableScan: t3 projection=[t3_id]
08)------SubqueryAlias: __correlated_sq_2
09)--------TableScan: t2 projection=[t2_id]

query ITI rowsort
select t1.t1_id,
       t1.t1_name,
       t1.t1_int
from t1
where t1.t1_id in (select t3.t3_id from t3) and (t1.t1_id > 40 or t1.t1_id in (select t2.t2_id from t2 where t1.t1_int > 0))
----
11 a 1
22 b 2
44 d 4

# Handle duplicate values in exists query
query ITI rowsort
select t1.t1_id,
       t1.t1_name,
       t1.t1_int
from t1
where t1.t1_id > 40 or exists (select * from t2 cross join t3 where t1.t1_id = t2.t2_id)
----
11 a 1
22 b 2
44 d 4

# Nested subqueries
query ITI rowsort
select t1.t1_id,
       t1.t1_name,
       t1.t1_int
from t1
where exists (
    select * from t2 where t1.t1_id = t2.t2_id OR exists (
        select * from t3 where t2.t2_id = t3.t3_id
    )
)
----
11 a 1
22 b 2
33 c 3
44 d 4

# issue: https://github.com/apache/datafusion/issues/7027
query TTTT rowsort
SELECT * FROM
    (VALUES ('catan-prod1-daily', 'success')) as jobs(cron_job_name, status)
  JOIN
    (VALUES ('catan-prod1-daily', 'high')) as severity(cron_job_name, level)
  ON (severity.cron_job_name = jobs.cron_job_name);
----
catan-prod1-daily success catan-prod1-daily high

##correlated_scalar_subquery_sum_agg_bug
#query TT
#explain
#select t1.t1_int from t1 where
#    (select sum(t2_int) is null from t2 where t1.t1_id = t2.t2_id)
#----
#logical_plan
#Projection: t1.t1_int
#--Inner Join: t1.t1_id = __scalar_sq_1.t2_id
#----TableScan: t1 projection=[t1_id, t1_int]
#----SubqueryAlias: __scalar_sq_1
#------Projection: t2.t2_id
#--------Filter: SUM(t2.t2_int) IS NULL
#----------Aggregate: groupBy=[[t2.t2_id]],  aggr=[[SUM(t2.t2_int)]]
#------------TableScan: t2 projection=[t2_id, t2_int]

#query I rowsort
#select t1.t1_int from t1 where
#    (select sum(t2_int) is null from t2 where t1.t1_id = t2.t2_id)
#----
#2
#3
#4

statement ok
create table t(a bigint);

# Result of query below shouldn't depend on
# number of optimization passes
# See issue: https://github.com/apache/datafusion/issues/8296
statement ok
set datafusion.optimizer.max_passes = 1;

query TT
explain select a/2, a/2 + 1 from t
----
logical_plan
01)Projection: __common_expr_1 AS t.a / Int64(2), __common_expr_1 AS t.a / Int64(2) + Int64(1)
02)--Projection: t.a / Int64(2) AS __common_expr_1
03)----TableScan: t projection=[a]

statement ok
set datafusion.optimizer.max_passes = 3;

query TT
explain select a/2, a/2 + 1 from t
----
logical_plan
01)Projection: __common_expr_1 AS t.a / Int64(2), __common_expr_1 AS t.a / Int64(2) + Int64(1)
02)--Projection: t.a / Int64(2) AS __common_expr_1
03)----TableScan: t projection=[a]

###
## Ensure that operators are rewritten in subqueries
###

statement ok
create table foo(x int) as values (1);

# Show input data
query ?
select struct(1, 'b')
----
{c0: 1, c1: b}

query T
select (select struct(1, 'b')['c1']);
----
b

query T
select 'foo' || (select struct(1, 'b')['c1']);
----
foob

query I
SELECT  * FROM (VALUES (1), (2))
WHERE column1  IN (SELECT struct(1, 'b')['c0']);
----
1

# also add an expression so the subquery is the output expr
query I
SELECT  * FROM (VALUES (1), (2))
WHERE 1+2 = 3 AND column1  IN (SELECT struct(1, 'b')['c0']);
----
1

query I
SELECT  * FROM foo
WHERE EXISTS (SELECT * FROM (values (1)) WHERE column1 = foo.x AND struct(1, 'b')['c0'] = 1);
----
1

# also add an expression so the subquery is the output expr
query I
SELECT  * FROM foo
WHERE 1+2 = 3 AND EXISTS (SELECT * FROM (values (1)) WHERE column1 = foo.x AND struct(1, 'b')['c0'] = 1);
----
1

statement ok
drop table foo;


# Test for window alias in subquery

# Setup source table
statement ok
CREATE TABLE source_table (
    column1 TEXT,
    column2 TIMESTAMP,
    column3 FLOAT
);

statement ok
INSERT INTO source_table VALUES
    ('item1', TIMESTAMP '1970-01-01 00:00:01', 50.0),
    ('item2', TIMESTAMP '1970-01-01 00:00:02', 30.0),
    ('item1', TIMESTAMP '1970-01-01 00:00:03', 25.0);

# Execute the query
query TPR
WITH SubQuery AS (
    SELECT
        a.column1,
        a.column2 AS ts_column,
        a.column3,
        SUM(a.column3) OVER (
            PARTITION BY a.column1
            ORDER BY a.column2 RANGE BETWEEN INTERVAL '10 minutes' PRECEDING AND CURRENT ROW
        ) AS moving_sum
    FROM source_table a
)
SELECT
    column1,
    ts_column,
    moving_sum
FROM SubQuery
WHERE moving_sum > 60;
----
item1 1970-01-01T00:00:03 75

statement ok
drop table source_table;

statement count 0
drop table t1;

statement count 0
drop table t2;

statement count 0
drop table t3;

# test count wildcard
statement count 0
create table t1(a int) as values (1);

statement count 0
create table t2(b int) as values (1);

query I
SELECT a FROM t1 WHERE EXISTS (SELECT count(*) FROM t2)
----
1

query TT
explain SELECT a FROM t1 WHERE EXISTS (SELECT count(*) FROM t2)
----
logical_plan
01)LeftSemi Join: 
02)--TableScan: t1 projection=[a]
03)--SubqueryAlias: __correlated_sq_1
04)----Projection: 
05)------Aggregate: groupBy=[[]], aggr=[[count(Int64(1))]]
06)--------TableScan: t2 projection=[]

statement count 0
drop table t1;

statement count 0
drop table t2;


# test exists_subquery_wildcard
statement count 0
create table person(id int, last_name int, state int);

query TT
explain SELECT id FROM person p WHERE EXISTS 
    (SELECT * FROM person WHERE last_name = p.last_name AND state = p.state)
----
logical_plan
01)Projection: p.id
02)--LeftSemi Join: p.last_name = __correlated_sq_1.last_name, p.state = __correlated_sq_1.state
03)----SubqueryAlias: p
04)------TableScan: person projection=[id, last_name, state]
05)----SubqueryAlias: __correlated_sq_1
06)------TableScan: person projection=[last_name, state]

statement count 0
drop table person;
