# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

##########
## Prepare Statement Tests
##########

statement ok
create table person (id int, first_name varchar, last_name varchar, age int, state varchar, salary double, birthday timestamp, "😀" int) as values (1, 'jane', 'smith', 20, 'MA', 100000.45, '2000-11-12T00:00:00'::timestamp, 99);

query ITTITRPI rowsort
select * from person;
----
1 jane smith 20 MA 100000.45 2000-11-12T00:00:00 99

# Error due to syntax and semantic violation

# Syntax error: no name specified after the keyword prepare
statement error DataFusion error: SQL error: ParserError
PREPARE AS SELECT id, age  FROM person WHERE age = $foo;

# param following a non-number, $foo, not supported
statement error Invalid placeholder, not a number: \$foo
PREPARE my_plan(INT) AS SELECT id, age  FROM person WHERE age = $foo;

# not specify table hence cannot specify columns
statement error Schema error: No field named id
PREPARE my_plan(INT) AS SELECT id + $1;

# not specify data types for all params
statement error Prepare specifies 1 data types but query has 2 parameters
PREPARE my_plan(INT) AS SELECT 1 + $1 + $2;

# sepecify too many data types for params
statement error Prepare specifies 2 data types but query has 1 parameters
PREPARE my_plan(INT, INT) AS SELECT 1 + $1;

# cannot use IS param
statement error SQL error: ParserError
PREPARE my_plan(INT) AS SELECT id, age  FROM person WHERE age is $1;

# #######################
# Test prepare and execute statements

# execute a non-existing plan
statement error Prepared statement \'my_plan\' does not exist
EXECUTE my_plan('Foo', 'Bar');

# deallocate a non-existing plan
statement error Prepared statement \'my_plan\' does not exist
DEALLOCATE my_plan;

# Allow prepare without specifying data types
statement ok
PREPARE my_plan AS SELECT $1;

query T
EXECUTE my_plan('Foo');
----
Foo

statement ok
DEALLOCATE my_plan

# Allow prepare col LIKE $1
statement ok
PREPARE my_plan AS SELECT * FROM person WHERE first_name LIKE $1;

query ITTITRPI rowsort
EXECUTE my_plan('j%');
----
1 jane smith 20 MA 100000.45 2000-11-12T00:00:00 99

statement ok
DEALLOCATE my_plan

# Check for missing parameters
statement ok
PREPARE my_plan AS SELECT * FROM person WHERE id < $1;

statement error No value found for placeholder with id \$1
EXECUTE my_plan

statement ok
DEALLOCATE my_plan

statement ok
PREPARE my_plan(STRING, STRING) AS SELECT * FROM (VALUES(1, $1), (2, $2)) AS t (num, letter);

query IT
EXECUTE my_plan('Foo', 'Bar');
----
1 Foo
2 Bar

# duplicate prepare statement
statement error Prepared statement \'my_plan\' already exists
PREPARE my_plan(STRING, STRING) AS SELECT * FROM (VALUES(1, $1), (2, $2)) AS t (num, letter);

# deallocate a plan
statement ok
DEALLOCATE my_plan;

# can't EXECUTE a deallocated plan
statement error Prepared statement \'my_plan\' does not exist
EXECUTE my_plan('Foo', 'Bar');

# re-prepare a deallocated plan
statement ok
PREPARE my_plan(STRING, STRING) AS SELECT * FROM (VALUES(1, $1), (2, $2)) AS t (num, letter);

query IT
EXECUTE my_plan('Foo', 'Bar');
----
1 Foo
2 Bar

# deallocate with the PREPARE keyword
statement ok
DEALLOCATE PREPARE my_plan;

statement error Prepare specifies 1 data types but query has 0 parameters
PREPARE my_plan(INT) AS SELECT id, age  FROM person WHERE age = 10;

# prepare statement has no params
statement ok
PREPARE my_plan2 AS SELECT id, age FROM person WHERE age = 20;

query II
EXECUTE my_plan2;
----
1 20

statement ok
DEALLOCATE my_plan2;

statement ok
PREPARE my_plan3(INT) AS SELECT $1;

query I
EXECUTE my_plan3(10);
----
10

statement ok
DEALLOCATE my_plan3;

statement ok
PREPARE my_plan4(INT) AS SELECT 1 + $1;

query I
EXECUTE my_plan4(10);
----
11

statement ok
DEALLOCATE my_plan4;

statement ok
PREPARE my_plan5(INT, DOUBLE) AS SELECT 1 + $1 + $2;

query R
EXECUTE my_plan5(10, 20.5);
----
31.5

statement ok
DEALLOCATE my_plan5;

statement ok
PREPARE my_plan6(INT) AS SELECT id, age FROM person WHERE age = $1;

query II
EXECUTE my_plan6(20);
----
1 20

# EXECUTE param is a different type but compatible
query II
EXECUTE my_plan6('20');
----
1 20

query II
EXECUTE my_plan6(20.0);
----
1 20

# invalid execute param
statement error Cast error: Cannot cast string 'foo' to value of Int32 type
EXECUTE my_plan6('foo');

# TODO: support non-literal expressions
statement error Unsupported parameter type
EXECUTE my_plan6(10 + 20);

statement ok
DEALLOCATE my_plan6;

statement ok
PREPARE my_plan7(INT, STRING, DOUBLE, INT, DOUBLE, STRING)
    AS
SELECT id, age, $6 FROM person WHERE age IN ($1, $4) AND salary > $3 and salary < $5 OR first_name < $2;

query IIT
EXECUTE my_plan7(10, 'jane', 99999.45, 20, 200000.45, 'foo');
----
1 20 foo

statement ok
DEALLOCATE my_plan7;

statement ok
PREPARE my_plan8(INT, DOUBLE, DOUBLE, DOUBLE)
    AS
SELECT id, SUM(age) FROM person WHERE salary > $2 GROUP BY id
    HAVING sum(age) < $1 AND SUM(age) > 10 OR SUM(age) in ($3, $4);

query II
EXECUTE my_plan8(100000, 99999.45, 100000.45, 200000.45);
----
1 20

statement ok
DEALLOCATE my_plan8;

statement ok
PREPARE my_plan9(STRING, STRING) AS SELECT * FROM (VALUES(1, $1), (2, $2)) AS t (num, letter);

query IT
EXECUTE my_plan9('Foo', 'Bar');
----
1 Foo
2 Bar

statement ok
DEALLOCATE my_plan9;

# Test issue: https://github.com/apache/datafusion/issues/12294
# prepare argument is in the LIMIT clause
statement ok
CREATE TABLE test(id INT, run_id TEXT) AS VALUES(1, 'foo'), (1, 'foo'), (3, 'bar');

statement ok
PREPARE get_N_rand_ints_from_last_run(INT) AS
SELECT id
FROM
    "test"
WHERE run_id = 'foo'
ORDER BY random()
LIMIT $1

query I
EXECUTE get_N_rand_ints_from_last_run(1);
----
1

query I
EXECUTE get_N_rand_ints_from_last_run(2);
----
1
1

statement ok
DEALLOCATE get_N_rand_ints_from_last_run;

statement ok
DROP TABLE test;

statement ok
SET datafusion.explain.logical_plan_only=true;

# OptimizeProjections rule works with PREPARE and pushes down the `id` projection to TableScan
query TT
EXPLAIN PREPARE my_plan(INT) AS SELECT id + $1 FROM person;
----
logical_plan
01)Prepare: "my_plan" [Int32]
02)--Projection: person.id + $1
03)----TableScan: person projection=[id]

# test creating logical plan for EXECUTE statements
query TT
EXPLAIN EXECUTE my_plan;
----
logical_plan Execute: my_plan params=[]

query TT
EXPLAIN EXECUTE my_plan(10*2 + 1, 'Foo');
----
logical_plan Execute: my_plan params=[Int64(21), Utf8("Foo")]

query error DataFusion error: Schema error: No field named a\.
EXPLAIN EXECUTE my_plan(a);

statement ok
SET datafusion.explain.logical_plan_only=false;

statement ok
DROP TABLE person;

statement ok
SET datafusion.explain.logical_plan_only=true;

statement count 0
PREPARE my_plan(STRING, STRING) AS SELECT * FROM (VALUES(1, $1), (2, $2)) AS t (num, letter);

statement count 5
explain PREPARE my_plan(STRING, STRING) AS SELECT * FROM (VALUES(1, $1), (2, $2)) AS t (num, letter);

query IT
EXECUTE my_plan('a', 'b');
----
1 a
2 b
