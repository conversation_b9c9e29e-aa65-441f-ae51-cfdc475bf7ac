# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# Test generate_series table function
query I
SELECT * FROM generate_series(6)
----
0
1
2
3
4
5
6



query I rowsort
SELECT * FROM generate_series(1, 5)
----
1
2
3
4
5

query I rowsort
SELECT * FROM generate_series(1, 1)
----
1

query I rowsort
SELECT * FROM generate_series(3, 6)
----
3
4
5
6

# #generated_data > batch_size
query I
SELECT count(v1) FROM generate_series(-66666,66666) t1(v1)
----
133333




query I rowsort
SELECT SUM(v1) FROM generate_series(1, 5) t1(v1)
----
15

query I
SELECT * FROM generate_series(6, -1, -2)
----
6
4
2
0

query I
SELECT * FROM generate_series(6, 66, 666)
----
6



# Test generate_series with WHERE clause
query I rowsort
SELECT * FROM generate_series(1, 10) t1(v1) WHERE v1 % 2 = 0
----
10
2
4
6
8

# Test generate_series with ORDER BY
query I
SELECT * FROM generate_series(1, 5) t1(v1) ORDER BY v1 DESC
----
5
4
3
2
1

# Test generate_series with LIMIT
query I rowsort
SELECT * FROM generate_series(1, 100) t1(v1) LIMIT 5
----
1
2
3
4
5

# Test generate_series in subquery
query I rowsort
SELECT v1 + 10 FROM (SELECT * FROM generate_series(1, 3) t1(v1))
----
11
12
13

# Test generate_series with JOIN
query II rowsort
SELECT a.v1, b.v1
FROM generate_series(1, 3) a(v1)
JOIN generate_series(2, 4) b(v1)
ON a.v1 = b.v1 - 1
----
1 2
2 3
3 4

#
# Test generate_series with null arguments
#

query I
SELECT * FROM generate_series(NULL, 5)
----

query I
SELECT * FROM generate_series(1, NULL)
----

query I
SELECT * FROM generate_series(NULL, NULL)
----

query I
SELECT * FROM generate_series(1, 5, NULL)
----


query TT
EXPLAIN SELECT * FROM generate_series(1, 5)
----
logical_plan TableScan: tmp_table projection=[value]
physical_plan LazyMemoryExec: partitions=1, batch_generators=[generate_series: start=1, end=5, batch_size=8192]

#
# Test generate_series with invalid arguments
#

query error DataFusion error: Error during planning: start is bigger than end, but increment is positive: cannot generate infinite series
SELECT * FROM generate_series(5, 1)

query error DataFusion error: Error during planning: start is smaller than end, but increment is negative: cannot generate infinite series
SELECT * FROM generate_series(-6, 6, -1)

query error DataFusion error: Error during planning: step cannot be zero
SELECT * FROM generate_series(-6, 6, 0)

query error DataFusion error: Error during planning: start is bigger than end, but increment is positive: cannot generate infinite series
SELECT * FROM generate_series(6, -6, 1)


statement error DataFusion error: Error during planning: generate_series function requires 1 to 3 arguments
SELECT * FROM generate_series(1, 2, 3, 4)


statement error DataFusion error: Error during planning: First argument must be an integer literal
SELECT * FROM generate_series('foo', 'bar')

# UDF and UDTF `generate_series` can be used simultaneously
query ? rowsort
SELECT generate_series(1, t1.end) FROM generate_series(3, 5) as t1(end)
----
[1, 2, 3, 4, 5]
[1, 2, 3, 4]
[1, 2, 3]

# Test range table function
query I
SELECT * FROM range(6)
----
0
1
2
3
4
5



query I rowsort
SELECT * FROM range(1, 5)
----
1
2
3
4

query I rowsort
SELECT * FROM range(1, 1)
----

query I rowsort
SELECT * FROM range(3, 6)
----
3
4
5

# #generated_data > batch_size
query I
SELECT count(v1) FROM range(-66666,66666) t1(v1)
----
133332

query I rowsort
SELECT SUM(v1) FROM range(1, 5) t1(v1)
----
10

query I
SELECT * FROM range(6, -1, -2)
----
6
4
2
0

query I
SELECT * FROM range(6, 66, 666)
----
6



#
# Test range with null arguments
#

query I
SELECT * FROM range(NULL, 5)
----

query I
SELECT * FROM range(1, NULL)
----

query I
SELECT * FROM range(NULL, NULL)
----

query I
SELECT * FROM range(1, 5, NULL)
----


query TT
EXPLAIN SELECT * FROM range(1, 5)
----
logical_plan TableScan: tmp_table projection=[value]
physical_plan LazyMemoryExec: partitions=1, batch_generators=[range: start=1, end=5, batch_size=8192]

#
# Test range with invalid arguments
#

query error DataFusion error: Error during planning: start is bigger than end, but increment is positive: cannot generate infinite series
SELECT * FROM range(5, 1)

query error DataFusion error: Error during planning: start is smaller than end, but increment is negative: cannot generate infinite series
SELECT * FROM range(-6, 6, -1)

query error DataFusion error: Error during planning: step cannot be zero
SELECT * FROM range(-6, 6, 0)

query error DataFusion error: Error during planning: start is bigger than end, but increment is positive: cannot generate infinite series
SELECT * FROM range(6, -6, 1)


statement error DataFusion error: Error during planning: range function requires 1 to 3 arguments
SELECT * FROM range(1, 2, 3, 4)


statement error DataFusion error: Error during planning: First argument must be an integer literal
SELECT * FROM range('foo', 'bar')

# UDF and UDTF `range` can be used simultaneously
query ? rowsort
SELECT range(1, t1.end) FROM range(3, 5) as t1(end)
----
[1, 2, 3]
[1, 2]
