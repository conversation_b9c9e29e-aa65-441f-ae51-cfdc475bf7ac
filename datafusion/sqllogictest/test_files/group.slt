# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

statement ok
CREATE EXTERNAL TABLE aggregate_test_100 (
  c1  VARCHAR NOT NULL,
  c2  TINYINT NOT NULL,
  c3  SMALLINT NOT NULL,
  c4  SMALLINT,
  c5  INT,
  c6  BIGINT NOT NULL,
  c7  SMALLINT NOT NULL,
  c8  INT NOT NULL,
  c9  BIGINT UNSIGNED NOT NULL,
  c10 VARCHAR NOT NULL,
  c11 FLOAT NOT NULL,
  c12 DOUBLE NOT NULL,
  c13 VARCHAR NOT NULL
)
STORED AS CSV
LOCATION '../../testing/data/csv/aggregate_test_100.csv'
OPTIONS ('format.has_header' 'true');

statement ok
CREATE external table aggregate_simple(c1 real, c2 double, c3 boolean) STORED as CSV LOCATION '../core/tests/data/aggregate_simple.csv' OPTIONS ('format.has_header' 'true');


# csv_query_group_by_int_min_max
query IRR rowsort
SELECT c2, MIN(c12), MAX(c12) FROM aggregate_test_100 GROUP BY c2
----
1 0.05636955102 0.996540038759
2 0.163011105157 0.991517828651
3 0.047343434291 0.929388350248
4 0.021825780392 0.923787797819
5 0.014793053078 0.97235803965

# csv_query_group_by_float32
query IR
SELECT COUNT(*) as cnt, c1 FROM aggregate_simple GROUP BY c1 ORDER BY cnt DESC
----
5 0.00005
4 0.00004
3 0.00003
2 0.00002
1 0.00001

# csv_query_group_by_float64
query IR
SELECT COUNT(*) as cnt, c2 FROM aggregate_simple GROUP BY c2 ORDER BY cnt DESC
----
5 0.000000000005
4 0.000000000004
3 0.000000000003
2 0.000000000002
1 0.000000000001

# csv_query_group_by_boolean
query IB
SELECT COUNT(*) as cnt, c3 FROM aggregate_simple GROUP BY c3 ORDER BY cnt DESC
----
9 true
6 false

# csv_query_group_by_two_columns
query TII rowsort
SELECT c1, c2, MIN(c3) FROM aggregate_test_100 GROUP BY c1, c2
----
a 1 -85
a 2 -48
a 3 -72
a 4 -101
a 5 -101
b 1 12
b 2 -60
b 3 -101
b 4 -117
b 5 -82
c 1 -24
c 2 -117
c 3 -2
c 4 -90
c 5 -94
d 1 -99
d 2 93
d 3 -76
d 4 5
d 5 -59
e 1 36
e 2 -61
e 3 -95
e 4 -56
e 5 -86

# csv_query_group_by_and_having
query TI rowsort
SELECT c1, MIN(c3) AS m FROM aggregate_test_100 GROUP BY c1 HAVING m < -100 AND MAX(c3) > 70
----
a -101
c -117

# csv_query_group_by_and_having_and_where
query TI
SELECT c1, MIN(c3) AS m
FROM aggregate_test_100
WHERE c1 IN ('a', 'b')
GROUP BY c1
HAVING m < -100 AND MAX(c3) > 70
----
a -101

# csv_query_group_by_substr
query T rowsort
SELECT substr(c1, 1, 1) c1 FROM aggregate_test_100 GROUP BY substr(c1, 1, 1)
----
a
b
c
d
e

# csv_query_group_by_avg
query TR rowsort
SELECT c1, avg(c12) FROM aggregate_test_100 GROUP BY c1
----
a 0.487545174661
b 0.410407092638
c 0.660045653644
d 0.488553793875
e 0.486006692713

# csv_query_group_by_with_aliases
query TR rowsort
SELECT c1 AS c12, avg(c12) AS c1 FROM aggregate_test_100 GROUP BY c1
----
a 0.487545174661
b 0.410407092638
c 0.660045653644
d 0.488553793875
e 0.486006692713

# csv_query_group_by_int_count
query TI rowsort
SELECT c1, count(c12) FROM aggregate_test_100 GROUP BY c1
----
a 21
b 19
c 21
d 18
e 21

# csv_query_group_with_aliased_aggregate
query TI rowsort
SELECT c1, count(c12) AS count FROM aggregate_test_100 GROUP BY c1
----
a 21
b 19
c 21
d 18
e 21

# csv_query_group_by_string_min_max
query TRR rowsort
SELECT c1, MIN(c12), MAX(c12) FROM aggregate_test_100 GROUP BY c1
----
a 0.021825780392 0.980019341044
b 0.04893135682 0.918581397074
c 0.049492446547 0.991517828651
d 0.061029375346 0.974836050902
e 0.014793053078 0.996540038759


# Create a table containing null values

statement ok
create table null_data(c1 int) as values
  (0),
  (3),
  (NULL),
  (1),
  (3);

# query_group_on_null
query II rowsort
SELECT COUNT(*), c1 FROM null_data GROUP BY c1
----
1 0
1 1
1 NULL
2 3


# Create a table containing multiple column with null values

statement ok
create table multi_null_data(c1 int, c2 string) as values
  (0, NULL),
  (0, NULL),
  (3, 'foo'),
  (NULL, NULL),
  (NULL, 'bar'),
  (3, 'foo'),
  (0, NULL),
  (NULL, 'bar'),
  (3, 'foo');

# query_group_on_null_multi_col

query IIT rowsort
SELECT COUNT(*), c1, c2 FROM multi_null_data GROUP BY c1, c2
----
1 NULL NULL
2 NULL bar
3 0 NULL
3 3 foo

query IIT rowsort
SELECT COUNT(*), c1, c2 FROM multi_null_data GROUP BY c2, c1
----
1 NULL NULL
2 NULL bar
3 0 NULL
3 3 foo


# Create a date table

statement ok
create table date_data(date date, cnt int) as values
  (100, 1),
  (100, 2),
  (100, 3),
  (101, 3),
  (101, 3),
  (101, 3);

# csv_group_by_date
query I rowsort
SELECT SUM(cnt) FROM date_data GROUP BY date
----
6
9

# Create time tables with different precisions but the same logical values

statement ok
create table time_data(ts bigint, cnt int) as values
  (5000000000000, 1),
  (5000000000000, 1),
  (5500000000000, 1),
  (5500000000000, 2),
  (5900000000000, 1),
  (5900000000000, 3);

statement ok
create table time64_nanos as
select arrow_cast(ts, 'Time64(Nanosecond)') as time, arrow_cast(cnt, 'Int64') as cnt
from time_data;

statement ok
create table time64_micros as
select arrow_cast(ts / 1000, 'Time64(Microsecond)') as time, arrow_cast(cnt, 'Int64') as cnt
from time_data;

statement ok
create table time32_millis as
select arrow_cast(arrow_cast(ts / 1000 / 1000, 'Int32'), 'Time32(Millisecond)') as time, cnt
from time_data;

statement ok
create table time32_s as
select arrow_cast(arrow_cast(ts / 1000 / 1000 / 1000, 'Int32'), 'Time32(Second)') as time, cnt
from time_data;

# csv_group_by_time32second
query I rowsort
SELECT SUM(cnt) FROM time32_s GROUP BY time
----
2
3
4

# csv_group_by_time32millisecond
query I rowsort
SELECT SUM(cnt) FROM time32_millis GROUP BY time
----
2
3
4

# csv_group_by_time64microsecond
query I rowsort
SELECT SUM(cnt) FROM time64_micros GROUP BY time
----
2
3
4

# csv_group_by_time64nanosecond
query I rowsort
SELECT SUM(cnt) FROM time64_nanos GROUP BY time
----
2
3
4


# TODO: group_by_date_trunc


# Create table with LargeUtf8 data

statement ok
create table utf8_data(str string, val bigint) as values
  ('A', 1),
  ('B', 2),
  ('A', 2),
  ('A', 4),
  ('C', 1),
  ('A', 1);

statement ok
create table largeutf8_data as
select arrow_cast(str, 'LargeUtf8') as str, val
from utf8_data;

# group_by_largeutf8
query TI rowsort
SELECT str, count(val) FROM largeutf8_data GROUP BY str
----
A 4
B 1
C 1


# TODO: group_by_dictionary

# csv_query_group_by_order_by_substr
query TR
SELECT substr(c1, 1, 1), avg(c12)
FROM aggregate_test_100
GROUP BY substr(c1, 1, 1)
ORDER BY substr(c1, 1, 1)
----
a 0.487545174661
b 0.410407092638
c 0.660045653644
d 0.488553793875
e 0.486006692713

# csv_query_group_by_order_by_substr_aliased_projection
query TR
SELECT substr(c1, 1, 1) as name, avg(c12) as average
FROM aggregate_test_100
GROUP BY substr(c1, 1, 1)
ORDER BY substr(c1, 1, 1)
----
a 0.487545174661
b 0.410407092638
c 0.660045653644
d 0.488553793875
e 0.486006692713

# csv_query_group_by_order_by_avg_group_by_substr
query TR
SELECT substr(c1, 1, 1) as name, avg(c12) as average
FROM aggregate_test_100
GROUP BY substr(c1, 1, 1)
ORDER BY avg(c12)
----
b 0.410407092638
e 0.486006692713
a 0.487545174661
d 0.488553793875
c 0.660045653644
