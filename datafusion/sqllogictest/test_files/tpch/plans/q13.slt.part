
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

query TT
explain select
    c_count,
    count(*) as custdist
from
    (
        select
            c_custkey,
            count(o_orderkey)
        from
            customer left outer join orders on
                        c_custkey = o_custkey
                    and o_comment not like '%special%requests%'
        group by
            c_custkey
    ) as c_orders (c_custkey, c_count)
group by
    c_count
order by
    custdist desc,
    c_count desc
limit 10;
----
logical_plan
01)Sort: custdist DESC NULLS FIRST, c_orders.c_count DESC NULLS FIRST, fetch=10
02)--Projection: c_orders.c_count, count(Int64(1)) AS count(*) AS custdist
03)----Aggregate: groupBy=[[c_orders.c_count]], aggr=[[count(Int64(1))]]
04)------SubqueryAlias: c_orders
05)--------Projection: count(orders.o_orderkey) AS c_count
06)----------Aggregate: groupBy=[[customer.c_custkey]], aggr=[[count(orders.o_orderkey)]]
07)------------Projection: customer.c_custkey, orders.o_orderkey
08)--------------Left Join: customer.c_custkey = orders.o_custkey
09)----------------TableScan: customer projection=[c_custkey]
10)----------------Projection: orders.o_orderkey, orders.o_custkey
11)------------------Filter: orders.o_comment NOT LIKE Utf8("%special%requests%")
12)--------------------TableScan: orders projection=[o_orderkey, o_custkey, o_comment], partial_filters=[orders.o_comment NOT LIKE Utf8("%special%requests%")]
physical_plan
01)SortPreservingMergeExec: [custdist@1 DESC, c_count@0 DESC], fetch=10
02)--SortExec: TopK(fetch=10), expr=[custdist@1 DESC, c_count@0 DESC], preserve_partitioning=[true]
03)----ProjectionExec: expr=[c_count@0 as c_count, count(Int64(1))@1 as custdist]
04)------AggregateExec: mode=FinalPartitioned, gby=[c_count@0 as c_count], aggr=[count(Int64(1))]
05)--------CoalesceBatchesExec: target_batch_size=8192
06)----------RepartitionExec: partitioning=Hash([c_count@0], 4), input_partitions=4
07)------------AggregateExec: mode=Partial, gby=[c_count@0 as c_count], aggr=[count(Int64(1))]
08)--------------ProjectionExec: expr=[count(orders.o_orderkey)@1 as c_count]
09)----------------AggregateExec: mode=SinglePartitioned, gby=[c_custkey@0 as c_custkey], aggr=[count(orders.o_orderkey)]
10)------------------CoalesceBatchesExec: target_batch_size=8192
11)--------------------HashJoinExec: mode=Partitioned, join_type=Left, on=[(c_custkey@0, o_custkey@1)], projection=[c_custkey@0, o_orderkey@1]
12)----------------------CoalesceBatchesExec: target_batch_size=8192
13)------------------------RepartitionExec: partitioning=Hash([c_custkey@0], 4), input_partitions=4
14)--------------------------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
15)----------------------------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/sqllogictest/test_files/tpch/data/customer.tbl]]}, projection=[c_custkey], file_type=csv, has_header=false
16)----------------------CoalesceBatchesExec: target_batch_size=8192
17)------------------------RepartitionExec: partitioning=Hash([o_custkey@1], 4), input_partitions=4
18)--------------------------CoalesceBatchesExec: target_batch_size=8192
19)----------------------------FilterExec: o_comment@2 NOT LIKE %special%requests%, projection=[o_orderkey@0, o_custkey@1]
20)------------------------------DataSourceExec: file_groups={4 groups: [[WORKSPACE_ROOT/datafusion/sqllogictest/test_files/tpch/data/orders.tbl:0..4223281], [WORKSPACE_ROOT/datafusion/sqllogictest/test_files/tpch/data/orders.tbl:4223281..8446562], [WORKSPACE_ROOT/datafusion/sqllogictest/test_files/tpch/data/orders.tbl:8446562..12669843], [WORKSPACE_ROOT/datafusion/sqllogictest/test_files/tpch/data/orders.tbl:12669843..16893122]]}, projection=[o_orderkey, o_custkey, o_comment], file_type=csv, has_header=false
