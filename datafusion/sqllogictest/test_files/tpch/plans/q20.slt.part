
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

query TT
explain select
    s_name,
    s_address
from
    supplier,
    nation
where
        s_suppkey in (
        select
            ps_suppkey
        from
            partsupp
        where
                ps_partkey in (
                select
                    p_partkey
                from
                    part
                where
                        p_name like 'forest%'
            )
          and ps_availqty > (
            select
                    0.5 * sum(l_quantity)
            from
                lineitem
            where
                    l_partkey = ps_partkey
              and l_suppkey = ps_suppkey
              and l_shipdate >= date '1994-01-01'
              and l_shipdate < date '1994-01-01' + interval '1' year
        )
    )
  and s_nationkey = n_nationkey
  and n_name = 'CANADA'
order by
    s_name;
----
logical_plan
01)Sort: supplier.s_name ASC NULLS LAST
02)--Projection: supplier.s_name, supplier.s_address
03)----LeftSemi Join: supplier.s_suppkey = __correlated_sq_2.ps_suppkey
04)------Projection: supplier.s_suppkey, supplier.s_name, supplier.s_address
05)--------Inner Join: supplier.s_nationkey = nation.n_nationkey
06)----------TableScan: supplier projection=[s_suppkey, s_name, s_address, s_nationkey]
07)----------Projection: nation.n_nationkey
08)------------Filter: nation.n_name = Utf8("CANADA")
09)--------------TableScan: nation projection=[n_nationkey, n_name], partial_filters=[nation.n_name = Utf8("CANADA")]
10)------SubqueryAlias: __correlated_sq_2
11)--------Projection: partsupp.ps_suppkey
12)----------Inner Join: partsupp.ps_partkey = __scalar_sq_3.l_partkey, partsupp.ps_suppkey = __scalar_sq_3.l_suppkey Filter: CAST(partsupp.ps_availqty AS Float64) > __scalar_sq_3.Float64(0.5) * sum(lineitem.l_quantity)
13)------------LeftSemi Join: partsupp.ps_partkey = __correlated_sq_1.p_partkey
14)--------------TableScan: partsupp projection=[ps_partkey, ps_suppkey, ps_availqty]
15)--------------SubqueryAlias: __correlated_sq_1
16)----------------Projection: part.p_partkey
17)------------------Filter: part.p_name LIKE Utf8("forest%")
18)--------------------TableScan: part projection=[p_partkey, p_name], partial_filters=[part.p_name LIKE Utf8("forest%")]
19)------------SubqueryAlias: __scalar_sq_3
20)--------------Projection: Float64(0.5) * CAST(sum(lineitem.l_quantity) AS Float64), lineitem.l_partkey, lineitem.l_suppkey
21)----------------Aggregate: groupBy=[[lineitem.l_partkey, lineitem.l_suppkey]], aggr=[[sum(lineitem.l_quantity)]]
22)------------------Projection: lineitem.l_partkey, lineitem.l_suppkey, lineitem.l_quantity
23)--------------------Filter: lineitem.l_shipdate >= Date32("1994-01-01") AND lineitem.l_shipdate < Date32("1995-01-01")
24)----------------------TableScan: lineitem projection=[l_partkey, l_suppkey, l_quantity, l_shipdate], partial_filters=[lineitem.l_shipdate >= Date32("1994-01-01"), lineitem.l_shipdate < Date32("1995-01-01")]
physical_plan
01)SortPreservingMergeExec: [s_name@0 ASC NULLS LAST]
02)--SortExec: expr=[s_name@0 ASC NULLS LAST], preserve_partitioning=[true]
03)----CoalesceBatchesExec: target_batch_size=8192
04)------HashJoinExec: mode=Partitioned, join_type=LeftSemi, on=[(s_suppkey@0, ps_suppkey@0)], projection=[s_name@1, s_address@2]
05)--------CoalesceBatchesExec: target_batch_size=8192
06)----------RepartitionExec: partitioning=Hash([s_suppkey@0], 4), input_partitions=4
07)------------CoalesceBatchesExec: target_batch_size=8192
08)--------------HashJoinExec: mode=Partitioned, join_type=Inner, on=[(s_nationkey@3, n_nationkey@0)], projection=[s_suppkey@0, s_name@1, s_address@2]
09)----------------CoalesceBatchesExec: target_batch_size=8192
10)------------------RepartitionExec: partitioning=Hash([s_nationkey@3], 4), input_partitions=4
11)--------------------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
12)----------------------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/sqllogictest/test_files/tpch/data/supplier.tbl]]}, projection=[s_suppkey, s_name, s_address, s_nationkey], file_type=csv, has_header=false
13)----------------CoalesceBatchesExec: target_batch_size=8192
14)------------------RepartitionExec: partitioning=Hash([n_nationkey@0], 4), input_partitions=4
15)--------------------CoalesceBatchesExec: target_batch_size=8192
16)----------------------FilterExec: n_name@1 = CANADA, projection=[n_nationkey@0]
17)------------------------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
18)--------------------------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/sqllogictest/test_files/tpch/data/nation.tbl]]}, projection=[n_nationkey, n_name], file_type=csv, has_header=false
19)--------CoalesceBatchesExec: target_batch_size=8192
20)----------RepartitionExec: partitioning=Hash([ps_suppkey@0], 4), input_partitions=4
21)------------CoalesceBatchesExec: target_batch_size=8192
22)--------------HashJoinExec: mode=Partitioned, join_type=Inner, on=[(ps_partkey@0, l_partkey@1), (ps_suppkey@1, l_suppkey@2)], filter=CAST(ps_availqty@0 AS Float64) > Float64(0.5) * sum(lineitem.l_quantity)@1, projection=[ps_suppkey@1]
23)----------------CoalesceBatchesExec: target_batch_size=8192
24)------------------RepartitionExec: partitioning=Hash([ps_partkey@0, ps_suppkey@1], 4), input_partitions=4
25)--------------------CoalesceBatchesExec: target_batch_size=8192
26)----------------------HashJoinExec: mode=Partitioned, join_type=LeftSemi, on=[(ps_partkey@0, p_partkey@0)]
27)------------------------CoalesceBatchesExec: target_batch_size=8192
28)--------------------------RepartitionExec: partitioning=Hash([ps_partkey@0], 4), input_partitions=4
29)----------------------------DataSourceExec: file_groups={4 groups: [[WORKSPACE_ROOT/datafusion/sqllogictest/test_files/tpch/data/partsupp.tbl:0..2932049], [WORKSPACE_ROOT/datafusion/sqllogictest/test_files/tpch/data/partsupp.tbl:2932049..5864098], [WORKSPACE_ROOT/datafusion/sqllogictest/test_files/tpch/data/partsupp.tbl:5864098..8796147], [WORKSPACE_ROOT/datafusion/sqllogictest/test_files/tpch/data/partsupp.tbl:8796147..11728193]]}, projection=[ps_partkey, ps_suppkey, ps_availqty], file_type=csv, has_header=false
30)------------------------CoalesceBatchesExec: target_batch_size=8192
31)--------------------------RepartitionExec: partitioning=Hash([p_partkey@0], 4), input_partitions=4
32)----------------------------CoalesceBatchesExec: target_batch_size=8192
33)------------------------------FilterExec: p_name@1 LIKE forest%, projection=[p_partkey@0]
34)--------------------------------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
35)----------------------------------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/sqllogictest/test_files/tpch/data/part.tbl]]}, projection=[p_partkey, p_name], file_type=csv, has_header=false
36)----------------ProjectionExec: expr=[0.5 * CAST(sum(lineitem.l_quantity)@2 AS Float64) as Float64(0.5) * sum(lineitem.l_quantity), l_partkey@0 as l_partkey, l_suppkey@1 as l_suppkey]
37)------------------AggregateExec: mode=FinalPartitioned, gby=[l_partkey@0 as l_partkey, l_suppkey@1 as l_suppkey], aggr=[sum(lineitem.l_quantity)]
38)--------------------CoalesceBatchesExec: target_batch_size=8192
39)----------------------RepartitionExec: partitioning=Hash([l_partkey@0, l_suppkey@1], 4), input_partitions=4
40)------------------------AggregateExec: mode=Partial, gby=[l_partkey@0 as l_partkey, l_suppkey@1 as l_suppkey], aggr=[sum(lineitem.l_quantity)]
41)--------------------------CoalesceBatchesExec: target_batch_size=8192
42)----------------------------FilterExec: l_shipdate@3 >= 1994-01-01 AND l_shipdate@3 < 1995-01-01, projection=[l_partkey@0, l_suppkey@1, l_quantity@2]
43)------------------------------DataSourceExec: file_groups={4 groups: [[WORKSPACE_ROOT/datafusion/sqllogictest/test_files/tpch/data/lineitem.tbl:0..18561749], [WORKSPACE_ROOT/datafusion/sqllogictest/test_files/tpch/data/lineitem.tbl:18561749..37123498], [WORKSPACE_ROOT/datafusion/sqllogictest/test_files/tpch/data/lineitem.tbl:37123498..55685247], [WORKSPACE_ROOT/datafusion/sqllogictest/test_files/tpch/data/lineitem.tbl:55685247..74246996]]}, projection=[l_partkey, l_suppkey, l_quantity, l_shipdate], file_type=csv, has_header=false
