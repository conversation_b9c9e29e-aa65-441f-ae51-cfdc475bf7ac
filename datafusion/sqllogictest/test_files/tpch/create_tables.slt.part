

# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# Some modification to original tpch:
# 1. Add `_rev` field in each table to make csv reader compatible with generated tbl format
# 2. Add `limit xx` to some queries to avoid making slt files too large.

statement ok
CREATE EXTERNAL TABLE IF NOT EXISTS supplier (
        s_suppkey  BIGINT,
        s_name VARCHAR,
        s_address VARCHAR,
        s_nationkey BIGINT,
        s_phone VARCHAR,
        s_acctbal DECIMAL(15, 2),
        s_comment VARCHAR,
        s_rev VARCHA<PERSON>,
) STORED AS CSV LOCATION 'test_files/tpch/data/supplier.tbl' OPTIONS ('format.delimiter' '|', 'format.has_header' 'false');

statement ok
CREATE EXTERNAL TABLE IF NOT EXISTS part (
        p_partkey BIGINT,
        p_name VARCHAR,
        p_mfgr VARCHAR,
        p_brand VARCHAR,
        p_type VARCHAR,
        p_size INTEGER,
        p_container VARCHAR,
        p_retailprice DECIMAL(15, 2),
        p_comment VARCHAR,
        p_rev VARCHAR,
) STORED AS CSV LOCATION 'test_files/tpch/data/part.tbl' OPTIONS ('format.delimiter' '|', 'format.has_header' 'false');


statement ok
CREATE EXTERNAL TABLE IF NOT EXISTS partsupp (
        ps_partkey BIGINT,
        ps_suppkey BIGINT,
        ps_availqty INTEGER,
        ps_supplycost DECIMAL(15, 2),
        ps_comment VARCHAR,
        ps_rev VARCHAR,
) STORED AS CSV LOCATION 'test_files/tpch/data/partsupp.tbl' OPTIONS ('format.delimiter' '|', 'format.has_header' 'false');

statement ok
CREATE EXTERNAL TABLE IF NOT EXISTS customer (
        c_custkey BIGINT,
        c_name VARCHAR,
        c_address VARCHAR,
        c_nationkey BIGINT,
        c_phone VARCHAR,
        c_acctbal DECIMAL(15, 2),
        c_mktsegment VARCHAR,
        c_comment VARCHAR,
        c_rev VARCHAR,
) STORED AS CSV LOCATION 'test_files/tpch/data/customer.tbl' OPTIONS ('format.delimiter' '|', 'format.has_header' 'false');

statement ok
CREATE EXTERNAL TABLE IF NOT EXISTS orders (
        o_orderkey BIGINT,
        o_custkey BIGINT,
        o_orderstatus VARCHAR,
        o_totalprice DECIMAL(15, 2),
        o_orderdate DATE,
        o_orderpriority VARCHAR,
        o_clerk VARCHAR,
        o_shippriority INTEGER,
        o_comment VARCHAR,
        o_rev VARCHAR,
)  STORED AS CSV LOCATION 'test_files/tpch/data/orders.tbl' OPTIONS ('format.delimiter' '|', 'format.has_header' 'false');

statement ok
CREATE EXTERNAL TABLE IF NOT EXISTS lineitem (
        l_orderkey BIGINT,
        l_partkey BIGINT,
        l_suppkey BIGINT,
        l_linenumber INTEGER,
        l_quantity DECIMAL(15, 2),
        l_extendedprice DECIMAL(15, 2),
        l_discount DECIMAL(15, 2),
        l_tax DECIMAL(15, 2),
        l_returnflag VARCHAR,
        l_linestatus VARCHAR,
        l_shipdate DATE,
        l_commitdate DATE,
        l_receiptdate DATE,
        l_shipinstruct VARCHAR,
        l_shipmode VARCHAR,
        l_comment VARCHAR,
        l_rev VARCHAR,
) STORED AS CSV LOCATION 'test_files/tpch/data/lineitem.tbl' OPTIONS ('format.delimiter' '|', 'format.has_header' 'false');

statement ok
CREATE EXTERNAL TABLE IF NOT EXISTS nation (
        n_nationkey BIGINT,
        n_name VARCHAR,
        n_regionkey BIGINT,
        n_comment VARCHAR,
        n_rev VARCHAR,
) STORED AS CSV LOCATION 'test_files/tpch/data/nation.tbl' OPTIONS ('format.delimiter' '|', 'format.has_header' 'false');

statement ok
CREATE EXTERNAL TABLE IF NOT EXISTS region (
        r_regionkey BIGINT,
        r_name VARCHAR,
        r_comment VARCHAR,
        r_rev VARCHAR,
) STORED AS CSV LOCATION 'test_files/tpch/data/region.tbl' OPTIONS ('format.delimiter' '|', 'format.has_header' 'false');
