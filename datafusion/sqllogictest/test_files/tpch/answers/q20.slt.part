
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

query TT
select
    s_name,
    s_address
from
    supplier,
    nation
where
        s_suppkey in (
        select
            ps_suppkey
        from
            partsupp
        where
                ps_partkey in (
                select
                    p_partkey
                from
                    part
                where
                        p_name like 'forest%'
            )
          and ps_availqty > (
            select
                    0.5 * sum(l_quantity)
            from
                lineitem
            where
                    l_partkey = ps_partkey
              and l_suppkey = ps_suppkey
              and l_shipdate >= date '1994-01-01'
              and l_shipdate < date '1994-01-01' + interval '1' year
        )
    )
  and s_nationkey = n_nationkey
  and n_name = 'CANADA'
order by
    s_name;
----
Supplier#000000157 ,mEGorBfVIm
Supplier#000000197 YC2Acon6kjY3zj3Fbxs2k4Vdf7X0cd2F
Supplier#000000287 7a9SP7qW5Yku5PvSg
Supplier#000000378 FfbhyCxWvcPrO8ltp9
Supplier#000000530 0qwCMwobKY OcmLyfRXlagA8ukENJv,
Supplier#000000555 TfB,a5bfl3Ah 3Z 74GqnNs6zKVGM
Supplier#000000557 jj0wUYh9K3fG5Jhdhrkuy ,4
Supplier#000000729 pqck2ppy758TQpZCUAjPvlU55K3QjfL7Bi
Supplier#000000935 ij98czM 2KzWe7dDTOxB8sq0UfCdvrX
