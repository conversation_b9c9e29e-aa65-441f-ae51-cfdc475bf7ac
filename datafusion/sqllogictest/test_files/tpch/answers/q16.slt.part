
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

query TTII
select
    p_brand,
    p_type,
    p_size,
    count(distinct ps_suppkey) as supplier_cnt
from
    partsupp,
    part
where
        p_partkey = ps_partkey
  and p_brand <> 'Brand#45'
  and p_type not like 'MEDIUM POLISHED%'
  and p_size in (49, 14, 23, 45, 19, 3, 36, 9)
  and ps_suppkey not in (
    select
        s_suppkey
    from
        supplier
    where
            s_comment like '%Customer%Complaints%'
)
group by
    p_brand,
    p_type,
    p_size
order by
    supplier_cnt desc,
    p_brand,
    p_type,
    p_size
limit 10;
----
Brand#14 SMALL ANODIZED NICKEL 45 12
Brand#22 SMALL BURNISHED BRASS 19 12
Brand#25 PROMO POLISHED COPPER 14 12
Brand#35 LARGE ANODIZED STEEL 45 12
Brand#35 PROMO BRUSHED COPPER 9 12
Brand#51 ECONOMY ANODIZED STEEL 9 12
Brand#53 LARGE BRUSHED NICKEL 45 12
Brand#11 ECONOMY POLISHED COPPER 14 8
Brand#11 LARGE PLATED STEEL 23 8
Brand#11 PROMO POLISHED STEEL 23 8
