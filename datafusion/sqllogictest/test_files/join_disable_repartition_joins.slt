# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

##########
## Join Tests
##########

# turn off repartition_joins
statement ok
set datafusion.optimizer.repartition_joins = false;

include ./join.slt.part

statement ok
CREATE EXTERNAL TABLE annotated_data (
  a0 INTEGER,
  a INTEGER,
  b INTEGER,
  c INTEGER,
  d INTEGER
)
STORED AS CSV
WITH ORDER (a ASC, b ASC, c ASC)
LOCATION '../core/tests/data/window_2.csv'
OPTIONS ('format.has_header' 'true');

query TT
EXPLAIN SELECT t2.a
 FROM annotated_data as t1
 INNER JOIN annotated_data as t2
 ON t1.c = t2.c ORDER BY t2.a
 LIMIT 5
----
logical_plan
01)Sort: t2.a ASC NULLS LAST, fetch=5
02)--Projection: t2.a
03)----Inner Join: t1.c = t2.c
04)------SubqueryAlias: t1
05)--------TableScan: annotated_data projection=[c]
06)------SubqueryAlias: t2
07)--------TableScan: annotated_data projection=[a, c]
physical_plan
01)SortPreservingMergeExec: [a@0 ASC NULLS LAST], fetch=5
02)--CoalesceBatchesExec: target_batch_size=8192, fetch=5
03)----HashJoinExec: mode=CollectLeft, join_type=Inner, on=[(c@0, c@1)], projection=[a@1]
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[c], file_type=csv, has_header=true
05)------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
06)--------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[a, c], output_ordering=[a@0 ASC NULLS LAST], file_type=csv, has_header=true

# preserve_inner_join
query IIII nosort
SELECT t1.a, t1.b, t1.c, t2.a as a2
 FROM annotated_data as t1
 INNER JOIN annotated_data as t2
 ON t1.d = t2.d ORDER BY a2, t2.b
 LIMIT 5
----
0 0 0 0
0 0 2 0
0 0 3 0
0 0 6 0
0 0 20 0

query TT
EXPLAIN SELECT t2.a as a2, t2.b
    FROM annotated_data as t1
    RIGHT SEMI JOIN annotated_data as t2
    ON t1.d = t2.d AND t1.c = t2.c
    WHERE t2.d = 3
    ORDER BY a2, t2.b
LIMIT 10
----
logical_plan
01)Sort: a2 ASC NULLS LAST, t2.b ASC NULLS LAST, fetch=10
02)--Projection: t2.a AS a2, t2.b
03)----RightSemi Join: t1.d = t2.d, t1.c = t2.c
04)------SubqueryAlias: t1
05)--------TableScan: annotated_data projection=[c, d]
06)------SubqueryAlias: t2
07)--------Filter: annotated_data.d = Int32(3)
08)----------TableScan: annotated_data projection=[a, b, c, d], partial_filters=[annotated_data.d = Int32(3)]
physical_plan
01)SortPreservingMergeExec: [a2@0 ASC NULLS LAST, b@1 ASC NULLS LAST], fetch=10
02)--ProjectionExec: expr=[a@0 as a2, b@1 as b]
03)----CoalesceBatchesExec: target_batch_size=8192, fetch=10
04)------HashJoinExec: mode=CollectLeft, join_type=RightSemi, on=[(d@1, d@3), (c@0, c@2)], projection=[a@0, b@1]
05)--------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[c, d], file_type=csv, has_header=true
06)--------CoalesceBatchesExec: target_batch_size=8192
07)----------FilterExec: d@3 = 3
08)------------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
09)--------------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[a, b, c, d], output_ordering=[a@0 ASC NULLS LAST, b@1 ASC NULLS LAST, c@2 ASC NULLS LAST], file_type=csv, has_header=true

# preserve_right_semi_join
query II nosort
SELECT t2.a as a2, t2.b
    FROM annotated_data as t1
    RIGHT SEMI JOIN annotated_data as t2
    ON t1.d = t2.d AND t1.c = t2.c
    WHERE t2.d = 3
    ORDER BY a2, t2.b
LIMIT 10
----
0 0
0 0
0 0
0 1
0 1
0 1
0 1
0 1
1 2
1 2

# turn on repartition_joins
statement ok
set datafusion.optimizer.repartition_joins = true;
