# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.


# Verify the information schema does not exit by default
statement error DataFusion error: Error during planning: table 'datafusion.information_schema.tables' not found
SELECT * from information_schema.tables

statement error DataFusion error: Error during planning: SHOW \[VARIABLE\] is not supported unless information_schema is enabled
show all

# Turn it on

# expect that the queries now work
statement ok
set datafusion.catalog.information_schema = true;

# Verify the information schema now does exist and is empty
query TTTT rowsort
SELECT * from information_schema.tables;
----
datafusion information_schema columns VIEW
datafusion information_schema df_settings VIEW
datafusion information_schema parameters VIEW
datafusion information_schema routines VIEW
datafusion information_schema schemata VIEW
datafusion information_schema tables VIEW
datafusion information_schema views VIEW

############
# Create multiple catalogs
###########
statement ok
create database my_catalog;

statement ok
create schema my_catalog.my_schema;

statement ok
set datafusion.catalog.default_catalog = my_catalog;

statement ok
set datafusion.catalog.default_schema = my_schema;

statement ok
create table t1 as values(1);

statement ok
create table t2 as values(1);

statement ok
create database my_other_catalog;

statement ok
create schema my_other_catalog.my_other_schema;

statement ok
set datafusion.catalog.default_catalog = my_other_catalog;

statement ok
set datafusion.catalog.default_schema = my_other_schema;

statement ok
create table t3 as values(1);

query TTTT rowsort
SELECT * from information_schema.tables;
----
datafusion information_schema columns VIEW
datafusion information_schema df_settings VIEW
datafusion information_schema parameters VIEW
datafusion information_schema routines VIEW
datafusion information_schema schemata VIEW
datafusion information_schema tables VIEW
datafusion information_schema views VIEW
my_catalog information_schema columns VIEW
my_catalog information_schema df_settings VIEW
my_catalog information_schema parameters VIEW
my_catalog information_schema routines VIEW
my_catalog information_schema schemata VIEW
my_catalog information_schema tables VIEW
my_catalog information_schema views VIEW
my_catalog my_schema t1 BASE TABLE
my_catalog my_schema t2 BASE TABLE
my_other_catalog information_schema columns VIEW
my_other_catalog information_schema df_settings VIEW
my_other_catalog information_schema parameters VIEW
my_other_catalog information_schema routines VIEW
my_other_catalog information_schema schemata VIEW
my_other_catalog information_schema tables VIEW
my_other_catalog information_schema views VIEW
my_other_catalog my_other_schema t3 BASE TABLE

# Cleanup

statement ok
drop table t3

statement ok
set datafusion.catalog.default_catalog = my_catalog;

statement ok
set datafusion.catalog.default_schema = my_schema;

statement ok
drop table t1

statement ok
drop table t2
