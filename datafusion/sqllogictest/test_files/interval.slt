# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# Use `interval` SQL literal syntax
# the types should be the same: https://github.com/apache/datafusion/issues/5801
query TT
select
  arrow_typeof(interval '5 months'),
  arrow_typeof(interval '5 days 3 nanoseconds')
----
Interval(MonthDayNano) Interval(MonthDayNano)


query ?
select interval '5' years
----
60 mons

# check all different kinds of intervals
query ?
select interval '5' year
----
60 mons

query ?
select interval '5' month
----
5 mons

query ?
select interval '5' months
----
5 mons

query ?
select interval '5' week
----
35 days


query ?
select interval '5' day
----
5 days

query ?
select interval '5' hour
----
5 hours

## This seems wrong (5 mons)
query ?
select interval '5' hours
----
5 hours

query ?
select interval '5' minute
----
5 mins

query ?
select interval '5' second
----
5.********* secs

query ?
select interval '5' millisecond
----
0.********* secs

query ?
select interval '5' milliseconds
----
0.********* secs

query ?
select interval '5' microsecond
----
0.********* secs

query ?
select interval '5' microseconds
----
0.********* secs

query ?
select interval '5' nanosecond
----
0.000000005 secs

query ?
select interval '5' nanoseconds
----
0.000000005 secs

query ?
select interval '5 YEAR'
----
60 mons

query ?
select interval '5 MONTH'
----
5 mons

query ?
select interval '5 WEEK'
----
35 days

query ?
select interval '5 DAY'
----
5 days

query ?
select interval '5 HOUR'
----
5 hours

query ?
select interval '5 HOURS'
----
5 hours

query ?
select interval '5 MINUTE'
----
5 mins

query ?
select interval '5 SECOND'
----
5.********* secs

query ?
select interval '5 SECONDS'
----
5.********* secs

query ?
select interval '5 MILLISECOND'
----
0.********* secs

query ?
select interval '5 MILLISECONDS'
----
0.********* secs

query ?
select interval '5 MICROSECOND'
----
0.********* secs

query ?
select interval '5 MICROSECONDS'
----
0.********* secs

query ?
select interval '5 NANOSECOND'
----
0.000000005 secs

query ?
select interval '5 NANOSECONDS'
----
0.000000005 secs

query ?
select interval '5 YEAR 5 MONTH 5 DAY 5 HOUR 5 MINUTE 5 SECOND 5 MILLISECOND 5 MICROSECOND 5 NANOSECOND'
----
65 mons 5 days 5 hours 5 mins 5.005005005 secs

# Interval mega nested literal addition
query ?
select interval '1 year' + interval '1 month' + interval '1 day' + interval '1 hour' + interval '1 minute' + interval '1 second' + interval '1 millisecond' + interval '1 microsecond' + interval '1 nanosecond'
----
13 mons 1 days 1 hours 1 mins 1.001001001 secs

# Interval with string literal subtraction
query ?
select interval '1 month' - interval '1 day';
----
1 mons -1 days

# Interval with nested string literal subtraction
query ?
select interval '1 month' - interval '1 day' - interval '1 hour';
----
1 mons -1 days -1 hours

# Interval mega nested string literal subtraction
query ?
select interval '1 year' - interval '1 month' - interval '1 day' - interval '1 hour' - interval '1 minute' - interval '1 second' - interval '1 millisecond' - interval '1 microsecond' - interval '1 nanosecond'
----
11 mons -1 days -1 hours -1 mins -1.001001001 secs

# Interval with nested literal negation
query ?
select -interval '1 month' + interval '1 day' + interval '1 hour';
----
-1 mons 1 days 1 hours

# Interval mega nested literal negation
query ?
select -interval '1 year' - interval '1 month' - interval '1 day' - interval '1 hour' - interval '1 minute' - interval '1 second' - interval '1 millisecond' - interval '1 microsecond' - interval '1 nanosecond'
----
-13 mons -1 days -1 hours -1 mins -1.001001001 secs

# Interval string literal + date
query D
select interval 1 month + interval 1 day + '2012-01-01'::date;
----
2012-02-02

# Interval nested string literal + date
query D
select interval 1 year +  interval 1 month + interval 1 day + '2012-01-01'::date
----
2013-02-02

# Interval nested string literal subtraction + date
query D
select interval 1 year - interval 1 month + interval 1 day + '2012-01-01'::date
----
2012-12-02

# Use interval SQL type
query TT
select
  arrow_typeof('5 months'::interval),
  arrow_typeof('5 days 3 nanoseconds'::interval)
----
Interval(MonthDayNano) Interval(MonthDayNano)

# cast with explicit cast syntax
query TT
select
  arrow_typeof(cast ('5 months' as interval)),
  arrow_typeof(cast ('5 days 3 nanoseconds' as interval))
----
Interval(MonthDayNano) Interval(MonthDayNano)


statement ok
create table t (i interval) as values (interval '5 days 3 nanoseconds');

query ?T rowsort
select
  i,
  arrow_typeof(i)
from t;
----
5 days 0.********* secs Interval(MonthDayNano)


statement ok
drop table t;

# Create tables with interval values
statement ok
create table t (i interval) as values ('5 days 3 nanoseconds'::interval);

statement ok
insert into t values ('6 days 7 nanoseconds'::interval)

query ? rowsort
select -i from t order by 1;
----
-5 days -0.********* secs
-6 days -0.********* secs

query ?T rowsort
select
  i,
  arrow_typeof(i)
from t;
----
5 days 0.********* secs Interval(MonthDayNano)
6 days 0.********* secs Interval(MonthDayNano)

statement ok
drop table t;


##### Tests for interval arithmetic

statement ok
create table t(i interval, d date, ts timestamp)
as
values
  ('1 month',  '1980-01-01', '2000-01-01T00:00:00'),
  ('1 day',    '1990-10-01', '2000-01-01T12:11:10'),
  ('1 minute', '1980-01-02', '2000-02-01T00:00:00')
;

### date / timestamp (scalar) + interval (scalar)
query D
select '1980-01-01'::date + interval '1 day'
----
1980-01-02


query P
select '1980-01-01'::timestamp + interval '1 day'
----
1980-01-02T00:00:00


### date / timestamp (scalar) - interval (scalar)
query D
select '1980-01-01'::date - interval '1 day'
----
1979-12-31


query P
select '1980-01-01'::timestamp - interval '1 day'
----
1979-12-31T00:00:00


### date / timestamp (array) + interval (scalar)
query D
select d + interval '1 day' from t;
----
1980-01-02
1990-10-02
1980-01-03

query P
select ts + interval '1 day' from t;
----
2000-01-02T00:00:00
2000-01-02T12:11:10
2000-02-02T00:00:00

### date / timestamp (array) - interval (scalar)
query D
select d - interval '1 day' from t;
----
1979-12-31
1990-09-30
1980-01-01

query P
select ts - interval '1 day' from t;
----
1999-12-31T00:00:00
1999-12-31T12:11:10
2000-01-31T00:00:00

### date / timestamp (scalar) + interval (array)
query D
select '1980-01-01'::date + i from t;
----
1980-02-01
1980-01-02
1980-01-01

query P
select '1980-01-01T12:00:00'::timestamp + i from t;
----
1980-02-01T12:00:00
1980-01-02T12:00:00
1980-01-01T12:01:00


query D
select '1980-01-01'::date - i from t;
----
1979-12-01
1979-12-31
1980-01-01

query P
select '1980-01-01T12:00:00'::timestamp - i from t;
----
1979-12-01T12:00:00
1979-12-31T12:00:00
1980-01-01T11:59:00

### date / timestamp (array) + interval (array)
query D
select d + i from t;
----
1980-02-01
1990-10-02
1980-01-02

query P
select ts + i from t;
----
2000-02-01T00:00:00
2000-01-02T12:11:10
2000-02-01T00:01:00


### date / timestamp (array) - interval (array)
query D
select d - i from t;
----
1979-12-01
1990-09-30
1980-01-02

query P
select ts - i from t;
----
1999-12-01T00:00:00
1999-12-31T12:11:10
2000-01-31T23:59:00


# Now reverse the argument order
# interval (scalar) + date / timestamp (scalar)
query D
select '1 month'::interval + '1980-01-01'::date;
----
1980-02-01

query P
select '1 month'::interval + '1980-01-01T12:00:00'::timestamp;
----
1980-02-01T12:00:00

query error DataFusion error: Error during planning: Cannot coerce arithmetic expression Interval\(MonthDayNano\) \- Date32 to valid types
select '1 month'::interval - '1980-01-01'::date;

query error DataFusion error: Error during planning: Cannot coerce arithmetic expression Interval\(MonthDayNano\) \- Timestamp\(Nanosecond, None\) to valid types
select '1 month'::interval - '1980-01-01T12:00:00'::timestamp;

# interval (array) + date / timestamp (array)
query D
select i + d from t;
----
1980-02-01
1990-10-02
1980-01-02

query P
select i + ts from t;
----
2000-02-01T00:00:00
2000-01-02T12:11:10
2000-02-01T00:01:00

# expected error interval (array) - date / timestamp (array)
query error DataFusion error: Error during planning: Cannot coerce arithmetic expression Interval\(MonthDayNano\) \- Date32 to valid types
select i - d from t;

query error DataFusion error: Error during planning: Cannot coerce arithmetic expression Interval\(MonthDayNano\) \- Timestamp\(Nanosecond, None\) to valid types
select i - ts from t;

# interval unit abreiviation and plurals
query ?
select interval '1s'
----
1.********* secs

query ?
select '1s'::interval
----
1.********* secs

query ?
select interval'1sec'
----
1.********* secs

query ?
select interval '1ms'
----
0.001000000 secs

query ?
select interval '1 y' + interval '1 year'
----
24 mons

# interval (scalar) + date / timestamp (array)
query D
select '1 month'::interval + d from t;
----
1980-02-01
1990-11-01
1980-02-02

query P
select '1 month'::interval + ts from t;
----
2000-02-01T00:00:00
2000-02-01T12:11:10
2000-03-01T00:00:00

# trailing extra unit, this matches PostgreSQL
query ?
select interval '5 day 1' hour
----
5 days 1 hours

# trailing extra unit, this matches PostgreSQL
query ?
select interval '5 day 0' hour
----
5 days

# This is interpreted as "0 hours" with PostgreSQL, should be fixed with
query error DataFusion error: Arrow error: Parser error: Invalid input syntax for type interval: "5 day HOUR"
SELECT interval '5 day' hour

# expected error interval (scalar) - date / timestamp (array)
query error DataFusion error: Error during planning: Cannot coerce arithmetic expression Interval\(MonthDayNano\) \- Date32 to valid types
select '1 month'::interval - d from t;

query error DataFusion error: Error during planning: Cannot coerce arithmetic expression Interval\(MonthDayNano\) \- Timestamp\(Nanosecond, None\) to valid types
select '1 month'::interval - ts from t;

# interval + date
query D
select interval '1 month' + '2012-01-01'::date;
----
2012-02-01

# is (not) distinct from
query BBBBBB
select
    i is distinct from null,
    i is distinct from (interval '1 month'),
    i is distinct from i,
    i is not distinct from null,
    i is not distinct from (interval '1 day'),
    i is not distinct from i
from t;
----
true false false false false true
true true false false true true
true true false false false true

### interval (array) cmp interval (array)
query BBBBBB
select i = i, i != i, i < i, i <= i, i > i, i >= i from t;
----
true false false true false true
true false false true false true
true false false true false true

### interval (array) cmp interval (scalar)
query BBBBBB
select
    (interval '1 day') = i,
    (interval '1 day') != i,
    i < (interval '1 day'),
    i <= (interval '1 day'),
    i > (interval '1 day'),
    i >= (interval '1 day')
from t;
----
false true false false true true
true false false true false true
false true true true false false

### interval (scalar) cmp interval (scalar)
query BBBBBB
select
    (interval '1 day') = (interval '1 day'),
    (interval '1 month') != (interval '1 day'),
    (interval '1 minute') < (interval '1 day'),
    (interval '1 hour') <= (interval '1 day'),
    (interval '1 year') > (interval '1 day'),
    (interval '1 day') >= (interval '1 day');
----
true true true true true true

statement ok
drop table t
