# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

statement ok
create table t(a int, b string) as values (1, 'a'), (2, NULL), (NULL, 'c');

# test between simplification
query TT
explain select a from t where a BETWEEN 3 and 3
----
logical_plan
01)Filter: t.a = Int32(3)
02)--TableScan: t projection=[a]
physical_plan
01)CoalesceBatchesExec: target_batch_size=8192
02)--FilterExec: a@0 = 3
03)----DataSourceExec: partitions=1, partition_sizes=[1]

# test regex exprs
query TT
explain select b from t where b ~ '.*'
----
logical_plan
01)Filter: t.b IS NOT NULL
02)--TableScan: t projection=[b]
physical_plan
01)CoalesceBatchesExec: target_batch_size=8192
02)--FilterExec: b@0 IS NOT NULL
03)----DataSourceExec: partitions=1, partition_sizes=[1]

query TT
explain select b from t where b !~ '.*'
----
logical_plan
01)Filter: t.b = Utf8("")
02)--TableScan: t projection=[b]
physical_plan
01)CoalesceBatchesExec: target_batch_size=8192
02)--FilterExec: b@0 = 
03)----DataSourceExec: partitions=1, partition_sizes=[1]

query T
select b from t where b ~ '.*'
----
a
c

query T
select b from t where b !~ '.*'
----

query TT
explain select * from t where a = a;
----
logical_plan
01)Filter: t.a IS NOT NULL OR Boolean(NULL)
02)--TableScan: t projection=[a, b]
physical_plan
01)CoalesceBatchesExec: target_batch_size=8192
02)--FilterExec: a@0 IS NOT NULL OR NULL
03)----DataSourceExec: partitions=1, partition_sizes=[1]

statement ok
drop table t;

# test decimal precision
query B
SELECT a * 1.000::DECIMAL(4,3) > 1.2::decimal(2,1) FROM VALUES (1) AS t(a);
----
false

query B
SELECT 1.000::DECIMAL(4,3) * a > 1.2::decimal(2,1) FROM VALUES (1) AS t(a);
----
false

query B
SELECT NULL::DECIMAL(4,3) * a > 1.2::decimal(2,1) FROM VALUES (1) AS t(a);
----
NULL

query B
SELECT a * NULL::DECIMAL(4,3) > 1.2::decimal(2,1) FROM VALUES (1) AS t(a);
----
NULL

query B
SELECT a / 1.000::DECIMAL(4,3) > 1.2::decimal(2,1) FROM VALUES (1) AS t(a);
----
false

query B
SELECT a / NULL::DECIMAL(4,3) > 1.2::decimal(2,1) FROM VALUES (1) AS t(a);
----
NULL
