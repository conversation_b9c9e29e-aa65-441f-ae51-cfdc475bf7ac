# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# Create a source where there is multiple orderings.
statement ok
CREATE EXTERNAL TABLE multiple_ordered_table (
  a0 INTEGER,
  a INTEGER,
  b INTEGER,
  c INTEGER,
  d INTEGER
)
STORED AS CSV
WITH ORDER (a ASC, b ASC)
WITH ORDER (c ASC)
LOCATION '../core/tests/data/window_2.csv'
OPTIONS ('format.has_header' 'true');

# test for substitute CAST scenario
query TT
EXPLAIN
SELECT 
    CAST(a AS BIGINT) AS a_big,
    b
FROM multiple_ordered_table
ORDER BY a_big ASC, b ASC;
----
logical_plan
01)Sort: a_big ASC NULLS LAST, multiple_ordered_table.b ASC NULLS LAST
02)--Projection: CAST(multiple_ordered_table.a AS Int64) AS a_big, multiple_ordered_table.b
03)----TableScan: multiple_ordered_table projection=[a, b]
physical_plan
01)SortPreservingMergeExec: [a_big@0 ASC NULLS LAST, b@1 ASC NULLS LAST]
02)--ProjectionExec: expr=[CAST(a@0 AS Int64) as a_big, b@1 as b]
03)----RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[a, b], output_ordering=[a@0 ASC NULLS LAST, b@1 ASC NULLS LAST], file_type=csv, has_header=true

query TT
EXPLAIN
SELECT a, CAST(a AS BIGINT) AS a_big, b
FROM multiple_ordered_table
ORDER BY a ASC, b ASC;
----
logical_plan
01)Sort: multiple_ordered_table.a ASC NULLS LAST, multiple_ordered_table.b ASC NULLS LAST
02)--Projection: multiple_ordered_table.a, CAST(multiple_ordered_table.a AS Int64) AS a_big, multiple_ordered_table.b
03)----TableScan: multiple_ordered_table projection=[a, b]
physical_plan
01)SortPreservingMergeExec: [a@0 ASC NULLS LAST, b@2 ASC NULLS LAST]
02)--ProjectionExec: expr=[a@0 as a, CAST(a@0 AS Int64) as a_big, b@1 as b]
03)----RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[a, b], output_ordering=[a@0 ASC NULLS LAST, b@1 ASC NULLS LAST], file_type=csv, has_header=true

# Cast to larger types as well as preserving ordering
# doesn't invalidate lexicographical ordering.
# Hence '[CAST(a AS BIGINT) AS a_big ASC, b ASC]'
# is valid for the given ordering: '[a ASC, b ASC]'.
# See discussion for rationale: https://github.com/apache/datafusion/issues/8838#issue-2077714891
query TT
EXPLAIN
SELECT a, CAST(a AS BIGINT) AS a_big, b
FROM multiple_ordered_table
ORDER BY a_big ASC, b ASC;
----
logical_plan
01)Sort: a_big ASC NULLS LAST, multiple_ordered_table.b ASC NULLS LAST
02)--Projection: multiple_ordered_table.a, CAST(multiple_ordered_table.a AS Int64) AS a_big, multiple_ordered_table.b
03)----TableScan: multiple_ordered_table projection=[a, b]
physical_plan
01)SortPreservingMergeExec: [a_big@1 ASC NULLS LAST, b@2 ASC NULLS LAST]
02)--ProjectionExec: expr=[a@0 as a, CAST(a@0 AS Int64) as a_big, b@1 as b]
03)----RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[a, b], output_ordering=[a@0 ASC NULLS LAST, b@1 ASC NULLS LAST], file_type=csv, has_header=true

# test for common rename
query TT
EXPLAIN
SELECT a, a AS a_big, b
FROM multiple_ordered_table
ORDER BY a_big ASC, b ASC;
----
logical_plan
01)Sort: a_big ASC NULLS LAST, multiple_ordered_table.b ASC NULLS LAST
02)--Projection: multiple_ordered_table.a, multiple_ordered_table.a AS a_big, multiple_ordered_table.b
03)----TableScan: multiple_ordered_table projection=[a, b]
physical_plan
01)ProjectionExec: expr=[a@0 as a, a@0 as a_big, b@1 as b]
02)--DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[a, b], output_ordering=[a@0 ASC NULLS LAST, b@1 ASC NULLS LAST], file_type=csv, has_header=true

query TT
EXPLAIN
SELECT a, a AS a_big, b
FROM multiple_ordered_table
ORDER BY a ASC, b ASC;
----
logical_plan
01)Sort: multiple_ordered_table.a ASC NULLS LAST, multiple_ordered_table.b ASC NULLS LAST
02)--Projection: multiple_ordered_table.a, multiple_ordered_table.a AS a_big, multiple_ordered_table.b
03)----TableScan: multiple_ordered_table projection=[a, b]
physical_plan
01)ProjectionExec: expr=[a@0 as a, a@0 as a_big, b@1 as b]
02)--DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[a, b], output_ordering=[a@0 ASC NULLS LAST, b@1 ASC NULLS LAST], file_type=csv, has_header=true


# test for cast Utf8
# (must actually sort as the sort order for a number cast to utf8 is different than for int)
# See discussion: https://github.com/apache/datafusion/pull/9127#discussion_r1492336709
query TT
EXPLAIN
SELECT 
    CAST(a AS STRING) AS a_str,
    b
FROM multiple_ordered_table
ORDER BY a_str ASC, b ASC;
----
logical_plan
01)Sort: a_str ASC NULLS LAST, multiple_ordered_table.b ASC NULLS LAST
02)--Projection: CAST(multiple_ordered_table.a AS Utf8) AS a_str, multiple_ordered_table.b
03)----TableScan: multiple_ordered_table projection=[a, b]
physical_plan
01)SortPreservingMergeExec: [a_str@0 ASC NULLS LAST, b@1 ASC NULLS LAST]
02)--SortExec: expr=[a_str@0 ASC NULLS LAST, b@1 ASC NULLS LAST], preserve_partitioning=[true]
03)----ProjectionExec: expr=[CAST(a@0 AS Utf8) as a_str, b@1 as b]
04)------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
05)--------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[a, b], output_ordering=[a@0 ASC NULLS LAST, b@1 ASC NULLS LAST], file_type=csv, has_header=true

# We cannot determine a+b is ordered from the
# invariant [a ASC, b ASC] is satisfied. Hence
# we should see a SortExec with a+b ASC in the plan.
query TT
EXPLAIN
SELECT a, b
FROM multiple_ordered_table
ORDER BY a + b ASC;
----
logical_plan
01)Sort: multiple_ordered_table.a + multiple_ordered_table.b ASC NULLS LAST
02)--TableScan: multiple_ordered_table projection=[a, b]
physical_plan
01)SortExec: expr=[a@0 + b@1 ASC NULLS LAST], preserve_partitioning=[false]
02)--DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[a, b], output_ordering=[a@0 ASC NULLS LAST, b@1 ASC NULLS LAST], file_type=csv, has_header=true

# With similar reasoning above. It is not guaranteed sum_expr is ordered
# Hence we should see a SortExec with sum_expr ASC in the plan.
query TT
EXPLAIN
SELECT CAST(a+b AS BIGINT) sum_expr, a, b
FROM multiple_ordered_table
ORDER BY sum_expr ASC;
----
logical_plan
01)Sort: sum_expr ASC NULLS LAST
02)--Projection: CAST(multiple_ordered_table.a + multiple_ordered_table.b AS Int64) AS sum_expr, multiple_ordered_table.a, multiple_ordered_table.b
03)----TableScan: multiple_ordered_table projection=[a, b]
physical_plan
01)SortPreservingMergeExec: [sum_expr@0 ASC NULLS LAST]
02)--SortExec: expr=[sum_expr@0 ASC NULLS LAST], preserve_partitioning=[true]
03)----ProjectionExec: expr=[CAST(a@0 + b@1 AS Int64) as sum_expr, a@0 as a, b@1 as b]
04)------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
05)--------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[a, b], output_ordering=[a@0 ASC NULLS LAST, b@1 ASC NULLS LAST], file_type=csv, has_header=true
