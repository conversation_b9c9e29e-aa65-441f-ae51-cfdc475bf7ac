# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

statement ok
CREATE EXTERNAL TABLE test_table (
  c1  VARCHAR NOT NULL,
  c2  TINYINT NOT NULL,
  c3  SMALLINT NOT NULL,
  c4  SMALLINT,
  c5  INT,
  c6  BIGINT NOT NULL,
  c7  SMALLINT NOT NULL,
  c8  INT NOT NULL,
  c9  INT UNSIGNED NOT NULL,
  c10 BIGINT UNSIGNED NOT NULL,
  c11 FLOAT NOT NULL,
  c12 DOUBLE NOT NULL,
  c13 VARCHAR NOT NULL
)
STORED AS CSV
LOCATION '../../testing/data/csv/aggregate_test_100.csv'
OPTIONS ('format.has_header' 'true');

statement ok
SET datafusion.execution.target_partitions = 1;

statement ok
SET datafusion.explain.logical_plan_only = true;

query TT
EXPLAIN
SELECT c1, 99999, c5 + c8, 'test', count(1)
FROM test_table t
GROUP BY 1, 2, 3, 4
----
logical_plan
01)Projection: t.c1, Int64(99999), t.c5 + t.c8, Utf8("test"), count(Int64(1))
02)--Aggregate: groupBy=[[t.c1, t.c5 + t.c8]], aggr=[[count(Int64(1))]]
03)----SubqueryAlias: t
04)------TableScan: test_table projection=[c1, c5, c8]

query TT
EXPLAIN
SELECT 123, 456, 789, count(1), avg(c12)
FROM test_table t
group by 1, 2, 3
----
logical_plan
01)Projection: Int64(123), Int64(456), Int64(789), count(Int64(1)), avg(t.c12)
02)--Aggregate: groupBy=[[]], aggr=[[count(Int64(1)), avg(t.c12)]]
03)----SubqueryAlias: t
04)------TableScan: test_table projection=[c12]

query TT
EXPLAIN 
SELECT to_date('2023-05-04') as dt, extract(day from now()) < 1000 as today_filter, count(1)
FROM test_table t
GROUP BY 1, 2
----
logical_plan
01)Projection: Date32("2023-05-04") AS dt, Boolean(true) AS today_filter, count(Int64(1))
02)--Aggregate: groupBy=[[]], aggr=[[count(Int64(1))]]
03)----SubqueryAlias: t
04)------TableScan: test_table projection=[]

query TT
EXPLAIN 
SELECT 
    not (
        cast(
            extract(month from now()) AS INT
        )
        between 50 and 60
    ), count(1)
FROM test_table t
GROUP BY 1
----
logical_plan
01)Projection: Boolean(true) AS NOT date_part(Utf8("MONTH"),now()) BETWEEN Int64(50) AND Int64(60), count(Int64(1))
02)--Aggregate: groupBy=[[]], aggr=[[count(Int64(1))]]
03)----SubqueryAlias: t
04)------TableScan: test_table projection=[]

query TT
EXPLAIN 
SELECT 123
FROM test_table t
GROUP BY 1
----
logical_plan
01)Aggregate: groupBy=[[Int64(123)]], aggr=[[]]
02)--SubqueryAlias: t
03)----TableScan: test_table projection=[]

query TT
EXPLAIN 
SELECT random()
FROM test_table t
GROUP BY 1
----
logical_plan
01)Aggregate: groupBy=[[random()]], aggr=[[]]
02)--SubqueryAlias: t
03)----TableScan: test_table projection=[]
