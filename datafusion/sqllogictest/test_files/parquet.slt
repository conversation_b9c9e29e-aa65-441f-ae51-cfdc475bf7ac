# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# TESTS FOR PARQUET FILES

# Set 2 partitions for deterministic output plans
statement ok
set datafusion.execution.target_partitions = 2;

# Create a table as a data source
statement ok
CREATE TABLE src_table (
  int_col INT,
  string_col TEXT,
  bigint_col BIGINT,
  date_col DATE
) AS VALUES
(1, 'aaa', 100, 1),
(2, 'bbb', 200, 2),
(3, 'ccc', 300, 3),
(4, 'ddd', 400, 4),
(5, 'eee', 500, 5),
(6, 'fff', 600, 6),
(7, 'ggg', 700, 7),
(8, 'hhh', 800, 8),
(9, 'iii', 900, 9);

# Setup 2 files, i.e., as many as there are partitions:

# File 1:
query I
COPY (SELECT * FROM src_table LIMIT 3)
TO 'test_files/scratch/parquet/test_table/0.parquet'
STORED AS PARQUET;
----
3

# File 2:
query I
COPY (SELECT * FROM src_table WHERE int_col > 3 LIMIT 3)
TO 'test_files/scratch/parquet/test_table/1.parquet'
STORED AS PARQUET;
----
3

# Create a table from generated parquet files, without ordering:
statement ok
CREATE EXTERNAL TABLE test_table (
  int_col INT,
  string_col TEXT,
  bigint_col BIGINT,
  date_col DATE
)
STORED AS PARQUET
LOCATION 'test_files/scratch/parquet/test_table';

# Basic query:
query ITID
SELECT * FROM test_table ORDER BY int_col;
----
1 aaa 100 1970-01-02
2 bbb 200 1970-01-03
3 ccc 300 1970-01-04
4 ddd 400 1970-01-05
5 eee 500 1970-01-06
6 fff 600 1970-01-07

# Check output plan, expect no "output_ordering" clause in the physical_plan -> ParquetExec:
query TT
EXPLAIN SELECT int_col, string_col
FROM test_table
ORDER BY string_col, int_col;
----
logical_plan
01)Sort: test_table.string_col ASC NULLS LAST, test_table.int_col ASC NULLS LAST
02)--TableScan: test_table projection=[int_col, string_col]
physical_plan
01)SortPreservingMergeExec: [string_col@1 ASC NULLS LAST, int_col@0 ASC NULLS LAST]
02)--SortExec: expr=[string_col@1 ASC NULLS LAST, int_col@0 ASC NULLS LAST], preserve_partitioning=[true]
03)----DataSourceExec: file_groups={2 groups: [[WORKSPACE_ROOT/datafusion/sqllogictest/test_files/scratch/parquet/test_table/0.parquet], [WORKSPACE_ROOT/datafusion/sqllogictest/test_files/scratch/parquet/test_table/1.parquet]]}, projection=[int_col, string_col], file_type=parquet

# Tear down test_table:
statement ok
DROP TABLE test_table;

# Create test_table again, but with ordering:
statement ok
CREATE EXTERNAL TABLE test_table (
  int_col INT,
  string_col TEXT,
  bigint_col BIGINT,
  date_col DATE
)
STORED AS PARQUET
WITH ORDER (string_col ASC NULLS LAST, int_col ASC NULLS LAST)
LOCATION 'test_files/scratch/parquet/test_table';

# Check output plan, expect an "output_ordering" clause in the physical_plan -> ParquetExec:
query TT
EXPLAIN SELECT int_col, string_col
FROM test_table
ORDER BY string_col, int_col;
----
logical_plan
01)Sort: test_table.string_col ASC NULLS LAST, test_table.int_col ASC NULLS LAST
02)--TableScan: test_table projection=[int_col, string_col]
physical_plan
01)SortPreservingMergeExec: [string_col@1 ASC NULLS LAST, int_col@0 ASC NULLS LAST]
02)--DataSourceExec: file_groups={2 groups: [[WORKSPACE_ROOT/datafusion/sqllogictest/test_files/scratch/parquet/test_table/0.parquet], [WORKSPACE_ROOT/datafusion/sqllogictest/test_files/scratch/parquet/test_table/1.parquet]]}, projection=[int_col, string_col], output_ordering=[string_col@1 ASC NULLS LAST, int_col@0 ASC NULLS LAST], file_type=parquet

# Add another file to the directory underlying test_table
query I
COPY (SELECT * FROM src_table WHERE int_col > 6 LIMIT 3)
TO 'test_files/scratch/parquet/test_table/2.parquet'
STORED AS PARQUET;
----
3

# Check output plan again, expect no "output_ordering" clause in the physical_plan -> ParquetExec,
# due to there being more files than partitions:
query TT
EXPLAIN SELECT int_col, string_col
FROM test_table
ORDER BY string_col, int_col;
----
logical_plan
01)Sort: test_table.string_col ASC NULLS LAST, test_table.int_col ASC NULLS LAST
02)--TableScan: test_table projection=[int_col, string_col]
physical_plan
01)SortPreservingMergeExec: [string_col@1 ASC NULLS LAST, int_col@0 ASC NULLS LAST]
02)--SortExec: expr=[string_col@1 ASC NULLS LAST, int_col@0 ASC NULLS LAST], preserve_partitioning=[true]
03)----DataSourceExec: file_groups={2 groups: [[WORKSPACE_ROOT/datafusion/sqllogictest/test_files/scratch/parquet/test_table/0.parquet, WORKSPACE_ROOT/datafusion/sqllogictest/test_files/scratch/parquet/test_table/1.parquet], [WORKSPACE_ROOT/datafusion/sqllogictest/test_files/scratch/parquet/test_table/2.parquet]]}, projection=[int_col, string_col], file_type=parquet


# Perform queries using MIN and MAX
query I
SELECT max(int_col) FROM test_table;
----
9

query T
SELECT min(string_col) FROM test_table;
----
aaa

query I
SELECT max(bigint_col) FROM test_table;
----
900

query D
SELECT min(date_col) FROM test_table;
----
1970-01-02

# Clean up
statement ok
DROP TABLE test_table;

# Setup alltypes_plain table:
statement ok
CREATE EXTERNAL TABLE alltypes_plain (
  id INT NOT NULL,
  bool_col BOOLEAN NOT NULL,
  tinyint_col TINYINT NOT NULL,
  smallint_col SMALLINT NOT NULL,
  int_col INT NOT NULL,
  bigint_col BIGINT NOT NULL,
  float_col FLOAT NOT NULL,
  double_col DOUBLE NOT NULL,
  date_string_col BYTEA NOT NULL,
  string_col VARCHAR NOT NULL,
  timestamp_col TIMESTAMP NOT NULL,
)
STORED AS PARQUET
LOCATION '../../parquet-testing/data/alltypes_plain.parquet';

# Test a basic query with a CAST:
query IT
SELECT id, CAST(string_col AS varchar) FROM alltypes_plain
----
4 0
5 1
6 0
7 1
2 0
3 1
0 0
1 1

# Ensure that local files can not be read by default (a potential security issue)
# (url table is only supported when DynamicFileCatalog is enabled)
statement error DataFusion error: Error during planning: table 'datafusion.public.../../parquet-testing/data/alltypes_plain.parquet' not found
SELECT id, CAST(string_col AS varchar) FROM '../../parquet-testing/data/alltypes_plain.parquet';

# Clean up
statement ok
DROP TABLE alltypes_plain;

# Perform SELECT on table with fixed sized binary columns

statement ok
CREATE EXTERNAL TABLE test_binary
STORED AS PARQUET
LOCATION '../core/tests/data/test_binary.parquet';

# Check size of table:
query I
SELECT count(ids) FROM test_binary;
----
466

# Do the SELECT query:
query ?
SELECT ids FROM test_binary ORDER BY ids LIMIT 10;
----
008c7196f68089ab692e4739c5fd16b5
00a51a7bc5ff8eb1627f8f3dc959dce8
0166ce1d46129ad104fa4990c6057c91
03a4893f3285b422820b4cd74c9b9786
04999ac861e14682cd339eae2cc74359
04b86bf8f228739fde391f850636a77d
050fb9cf722a709eb94b70b3ee7dc342
052578a65e8e91b8526b182d40e846e8
05408e6a403e4296526006e20cc4a45a
0592e6fb7d7169b888a4029b53abb701

# Clean up
statement ok
DROP TABLE test_binary;

# Perform a query with a window function and timestamp data:

statement ok
CREATE EXTERNAL TABLE timestamp_with_tz
STORED AS PARQUET
LOCATION '../core/tests/data/timestamp_with_tz.parquet';

# Check size of table:
query I
SELECT COUNT(*) FROM timestamp_with_tz;
----
131072

# Ensure that output timestamp columns preserve the timezone from the input
# and output record count will match input file record count
query TPI
SELECT arrow_typeof(lag_timestamp),
       MIN(lag_timestamp),
       COUNT(1)
FROM (
  SELECT
    count,
    LAG(timestamp, 1) OVER (ORDER BY timestamp) AS lag_timestamp
  FROM timestamp_with_tz
) t
GROUP BY 1
----
Timestamp(Millisecond, Some("UTC")) 2014-08-27T14:00:00Z 131072

# Test config listing_table_ignore_subdirectory:

query I
COPY (SELECT * FROM src_table WHERE int_col > 6 LIMIT 3)
TO 'test_files/scratch/parquet/test_table/subdir/3.parquet'
STORED AS PARQUET;
----
3

statement ok
CREATE EXTERNAL TABLE listing_table
STORED AS PARQUET
LOCATION 'test_files/scratch/parquet/test_table/*.parquet';

statement ok
set datafusion.execution.listing_table_ignore_subdirectory = true;

# scan file: 0.parquet 1.parquet 2.parquet
query I
select count(*) from listing_table;
----
9

statement ok
set datafusion.execution.listing_table_ignore_subdirectory = false;

# scan file: 0.parquet 1.parquet 2.parquet 3.parquet
query I
select count(*) from listing_table;
----
12

# Clean up
statement ok
DROP TABLE timestamp_with_tz;

# Test a query from the single_nan data set:
statement ok
CREATE EXTERNAL TABLE single_nan
STORED AS PARQUET
LOCATION '../../parquet-testing/data/single_nan.parquet';

# Check table size:
query I
SELECT COUNT(*) FROM single_nan;
----
1

# Query for the single NULL:
query R
SELECT mycol FROM single_nan;
----
NULL

# Clean up
statement ok
DROP TABLE single_nan;

statement ok
CREATE EXTERNAL TABLE list_columns
STORED AS PARQUET
LOCATION '../../parquet-testing/data/list_columns.parquet';

query ??
SELECT int64_list, utf8_list FROM list_columns
----
[1, 2, 3] [abc, efg, hij]
[NULL, 1] NULL
[4] [efg, NULL, hij, xyz]

statement ok
DROP TABLE list_columns;

# Clean up
statement ok
DROP TABLE listing_table;

### Tests for binary_ar_string

# This scenario models the case where a column has been stored in parquet
# "binary" column (without a String logical type annotation)
# this is the case with the `hits_partitioned` ClickBench datasets
# see https://github.com/apache/datafusion/issues/12788

## Create a table with a binary column

query I
COPY (
  SELECT
    arrow_cast(string_col, 'Binary')      as binary_col,
    arrow_cast(string_col, 'LargeBinary') as largebinary_col,
    arrow_cast(string_col, 'BinaryView')  as binaryview_col
  FROM src_table
  )
TO 'test_files/scratch/parquet/binary_as_string.parquet'
STORED AS PARQUET;
----
9

# Test 1: Read table with default options
statement ok
CREATE EXTERNAL TABLE binary_as_string_default
STORED AS PARQUET LOCATION 'test_files/scratch/parquet/binary_as_string.parquet'

# NB the data is read and displayed as binary
query T?T?T?
select
  arrow_typeof(binary_col),      binary_col,
  arrow_typeof(largebinary_col), largebinary_col,
  arrow_typeof(binaryview_col),  binaryview_col
 FROM binary_as_string_default;
----
BinaryView 616161 BinaryView 616161 BinaryView 616161
BinaryView 626262 BinaryView 626262 BinaryView 626262
BinaryView 636363 BinaryView 636363 BinaryView 636363
BinaryView 646464 BinaryView 646464 BinaryView 646464
BinaryView 656565 BinaryView 656565 BinaryView 656565
BinaryView 666666 BinaryView 666666 BinaryView 666666
BinaryView 676767 BinaryView 676767 BinaryView 676767
BinaryView 686868 BinaryView 686868 BinaryView 686868
BinaryView 696969 BinaryView 696969 BinaryView 696969

# Run an explain plan to show the cast happens in the plan (a CAST is needed for the predicates)
query TT
EXPLAIN
  SELECT binary_col, largebinary_col, binaryview_col
  FROM binary_as_string_default
 WHERE
   binary_col LIKE '%a%' AND
   largebinary_col LIKE '%a%' AND
   binaryview_col LIKE '%a%';
----
logical_plan
01)Filter: CAST(binary_as_string_default.binary_col AS Utf8View) LIKE Utf8View("%a%") AND CAST(binary_as_string_default.largebinary_col AS Utf8View) LIKE Utf8View("%a%") AND CAST(binary_as_string_default.binaryview_col AS Utf8View) LIKE Utf8View("%a%")
02)--TableScan: binary_as_string_default projection=[binary_col, largebinary_col, binaryview_col], partial_filters=[CAST(binary_as_string_default.binary_col AS Utf8View) LIKE Utf8View("%a%"), CAST(binary_as_string_default.largebinary_col AS Utf8View) LIKE Utf8View("%a%"), CAST(binary_as_string_default.binaryview_col AS Utf8View) LIKE Utf8View("%a%")]
physical_plan
01)CoalesceBatchesExec: target_batch_size=8192
02)--FilterExec: CAST(binary_col@0 AS Utf8View) LIKE %a% AND CAST(largebinary_col@1 AS Utf8View) LIKE %a% AND CAST(binaryview_col@2 AS Utf8View) LIKE %a%
03)----RepartitionExec: partitioning=RoundRobinBatch(2), input_partitions=1
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/sqllogictest/test_files/scratch/parquet/binary_as_string.parquet]]}, projection=[binary_col, largebinary_col, binaryview_col], file_type=parquet, predicate=CAST(binary_col@0 AS Utf8View) LIKE %a% AND CAST(largebinary_col@1 AS Utf8View) LIKE %a% AND CAST(binaryview_col@2 AS Utf8View) LIKE %a%


statement ok
DROP TABLE binary_as_string_default;

## Test 2: Read table using the binary_as_string option

statement ok
CREATE EXTERNAL TABLE binary_as_string_option
STORED AS PARQUET LOCATION 'test_files/scratch/parquet/binary_as_string.parquet'
OPTIONS ('binary_as_string' 'true');

# NB the data is read and displayed as string
query TTTTTT
select
  arrow_typeof(binary_col),      binary_col,
  arrow_typeof(largebinary_col), largebinary_col,
  arrow_typeof(binaryview_col),  binaryview_col
 FROM binary_as_string_option;
----
Utf8View aaa Utf8View aaa Utf8View aaa
Utf8View bbb Utf8View bbb Utf8View bbb
Utf8View ccc Utf8View ccc Utf8View ccc
Utf8View ddd Utf8View ddd Utf8View ddd
Utf8View eee Utf8View eee Utf8View eee
Utf8View fff Utf8View fff Utf8View fff
Utf8View ggg Utf8View ggg Utf8View ggg
Utf8View hhh Utf8View hhh Utf8View hhh
Utf8View iii Utf8View iii Utf8View iii

# Run an explain plan to show the cast happens in the plan (there should be no casts)
query TT
EXPLAIN
  SELECT binary_col, largebinary_col, binaryview_col
  FROM binary_as_string_option
 WHERE
   binary_col LIKE '%a%' AND
   largebinary_col LIKE '%a%' AND
   binaryview_col LIKE '%a%';
----
logical_plan
01)Filter: binary_as_string_option.binary_col LIKE Utf8View("%a%") AND binary_as_string_option.largebinary_col LIKE Utf8View("%a%") AND binary_as_string_option.binaryview_col LIKE Utf8View("%a%")
02)--TableScan: binary_as_string_option projection=[binary_col, largebinary_col, binaryview_col], partial_filters=[binary_as_string_option.binary_col LIKE Utf8View("%a%"), binary_as_string_option.largebinary_col LIKE Utf8View("%a%"), binary_as_string_option.binaryview_col LIKE Utf8View("%a%")]
physical_plan
01)CoalesceBatchesExec: target_batch_size=8192
02)--FilterExec: binary_col@0 LIKE %a% AND largebinary_col@1 LIKE %a% AND binaryview_col@2 LIKE %a%
03)----RepartitionExec: partitioning=RoundRobinBatch(2), input_partitions=1
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/sqllogictest/test_files/scratch/parquet/binary_as_string.parquet]]}, projection=[binary_col, largebinary_col, binaryview_col], file_type=parquet, predicate=binary_col@0 LIKE %a% AND largebinary_col@1 LIKE %a% AND binaryview_col@2 LIKE %a%


statement ok
DROP TABLE binary_as_string_option;

## Test 3: Read table with binary_as_string option AND schema_force_view_types

statement ok
CREATE EXTERNAL TABLE binary_as_string_both
STORED AS PARQUET LOCATION 'test_files/scratch/parquet/binary_as_string.parquet'
OPTIONS (
  'binary_as_string' 'true',
  'schema_force_view_types' 'true'
);

# NB the data is read and displayed a StringView
query TTTTTT
select
  arrow_typeof(binary_col),      binary_col,
  arrow_typeof(largebinary_col), largebinary_col,
  arrow_typeof(binaryview_col),  binaryview_col
 FROM binary_as_string_both;
----
Utf8View aaa Utf8View aaa Utf8View aaa
Utf8View bbb Utf8View bbb Utf8View bbb
Utf8View ccc Utf8View ccc Utf8View ccc
Utf8View ddd Utf8View ddd Utf8View ddd
Utf8View eee Utf8View eee Utf8View eee
Utf8View fff Utf8View fff Utf8View fff
Utf8View ggg Utf8View ggg Utf8View ggg
Utf8View hhh Utf8View hhh Utf8View hhh
Utf8View iii Utf8View iii Utf8View iii

# Run an explain plan to show the cast happens in the plan (there should be no casts)
query TT
EXPLAIN
  SELECT binary_col, largebinary_col, binaryview_col
  FROM binary_as_string_both
 WHERE
   binary_col LIKE '%a%' AND
   largebinary_col LIKE '%a%' AND
   binaryview_col LIKE '%a%';
----
logical_plan
01)Filter: binary_as_string_both.binary_col LIKE Utf8View("%a%") AND binary_as_string_both.largebinary_col LIKE Utf8View("%a%") AND binary_as_string_both.binaryview_col LIKE Utf8View("%a%")
02)--TableScan: binary_as_string_both projection=[binary_col, largebinary_col, binaryview_col], partial_filters=[binary_as_string_both.binary_col LIKE Utf8View("%a%"), binary_as_string_both.largebinary_col LIKE Utf8View("%a%"), binary_as_string_both.binaryview_col LIKE Utf8View("%a%")]
physical_plan
01)CoalesceBatchesExec: target_batch_size=8192
02)--FilterExec: binary_col@0 LIKE %a% AND largebinary_col@1 LIKE %a% AND binaryview_col@2 LIKE %a%
03)----RepartitionExec: partitioning=RoundRobinBatch(2), input_partitions=1
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/sqllogictest/test_files/scratch/parquet/binary_as_string.parquet]]}, projection=[binary_col, largebinary_col, binaryview_col], file_type=parquet, predicate=binary_col@0 LIKE %a% AND largebinary_col@1 LIKE %a% AND binaryview_col@2 LIKE %a%


statement ok
drop table binary_as_string_both;

# Read a parquet file with binary data in a FixedSizeBinary column

# by default, the data is read as binary
statement ok
CREATE EXTERNAL TABLE test_non_utf8_binary
STORED AS PARQUET LOCATION '../core/tests/data/test_binary.parquet';

query T?
SELECT arrow_typeof(ids), ids FROM test_non_utf8_binary LIMIT 3;
----
FixedSizeBinary(16) 008c7196f68089ab692e4739c5fd16b5
FixedSizeBinary(16) 00a51a7bc5ff8eb1627f8f3dc959dce8
FixedSizeBinary(16) 0166ce1d46129ad104fa4990c6057c91

statement ok
DROP TABLE test_non_utf8_binary;


# even with the binary_as_string option set, the data is read as binary
statement ok
CREATE EXTERNAL TABLE test_non_utf8_binary
STORED AS PARQUET LOCATION '../core/tests/data/test_binary.parquet'
OPTIONS ('binary_as_string' 'true');

query T?
SELECT arrow_typeof(ids), ids FROM test_non_utf8_binary LIMIT 3
----
FixedSizeBinary(16) 008c7196f68089ab692e4739c5fd16b5
FixedSizeBinary(16) 00a51a7bc5ff8eb1627f8f3dc959dce8
FixedSizeBinary(16) 0166ce1d46129ad104fa4990c6057c91

statement ok
DROP TABLE test_non_utf8_binary;


## Tests for https://github.com/apache/datafusion/issues/13186
statement ok
create table cpu (time timestamp, usage_idle float, usage_user float, cpu int);

statement ok
insert into cpu values ('1970-01-01 00:00:00', 1.0, 2.0, 3);

# must put it into a parquet file to get statistics
statement ok
copy (select * from cpu) to 'test_files/scratch/parquet/cpu.parquet';

# Run queries against parquet files
statement ok
create external table cpu_parquet
stored as parquet
location 'test_files/scratch/parquet/cpu.parquet';

# Double filtering
#
# Expect 1 row for both queries
query PI
select time, rn
from (
  select time, row_number() OVER (ORDER BY usage_idle, time) as rn
  from cpu
  where cpu = 3
) where rn > 0;
----
1970-01-01T00:00:00 1

query PI
select time, rn
from (
  select time, row_number() OVER (ORDER BY usage_idle, time) as rn
  from cpu_parquet
  where cpu = 3
) where rn > 0;
----
1970-01-01T00:00:00 1


# Clean up
statement ok
drop table cpu;

statement ok
drop table cpu_parquet;

# Test for parquet predicate pruning with `starts_with` function
query I
copy (values ('foo'), ('bar'), ('baz')) TO 'test_files/scratch/parquet/foo.parquet'
----
3

statement ok
create external table foo
stored as parquet
location 'test_files/scratch/parquet/foo.parquet';


# Expect that the pruning predicate contain a comparison on the min/max value of `column1):
# column1_min@0 <= g AND f <= column1_max@1`
# (the starts_with function is not supported in the parquet predicate pruning but DataFusion rewrites
# it to a like which is then handled by the PruningPredicate)
query TT
explain select * from foo where starts_with(column1, 'f');
----
logical_plan
01)Filter: foo.column1 LIKE Utf8View("f%")
02)--TableScan: foo projection=[column1], partial_filters=[foo.column1 LIKE Utf8View("f%")]
physical_plan
01)CoalesceBatchesExec: target_batch_size=8192
02)--FilterExec: column1@0 LIKE f%
03)----RepartitionExec: partitioning=RoundRobinBatch(2), input_partitions=1
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/sqllogictest/test_files/scratch/parquet/foo.parquet]]}, projection=[column1], file_type=parquet, predicate=column1@0 LIKE f%, pruning_predicate=column1_null_count@2 != row_count@3 AND column1_min@0 <= g AND f <= column1_max@1, required_guarantees=[]

statement ok
drop table foo


statement ok
set datafusion.execution.parquet.coerce_int96 = ms;

statement ok
CREATE EXTERNAL TABLE int96_from_spark
STORED AS PARQUET
LOCATION '../../parquet-testing/data/int96_from_spark.parquet';

# Print schema
query TTT
describe int96_from_spark;
----
a Timestamp(Millisecond, None) YES

statement ok
set datafusion.execution.parquet.coerce_int96 = ns;
