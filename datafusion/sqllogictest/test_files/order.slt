# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.


statement ok
CREATE EXTERNAL TABLE alltypes_plain STORED AS PARQUET LOCATION '../../parquet-testing/data/alltypes_plain.parquet';

statement ok
CREATE EXTERNAL TABLE aggregate_test_100 (
  c1  VARCHAR NOT NULL,
  c2  TINYINT NOT NULL,
  c3  SMALLINT NOT NULL,
  c4  SMALLINT,
  c5  INT,
  c6  BIGINT NOT NULL,
  c7  SMALLINT NOT NULL,
  c8  INT NOT NULL,
  c9  BIGINT UNSIGNED NOT NULL,
  c10 VARCHAR NOT NULL,
  c11 FLOAT NOT NULL,
  c12 DOUBLE NOT NULL,
  c13 VARCHAR NOT NULL
)
STORED AS CSV
LOCATION '../../testing/data/csv/aggregate_test_100.csv'
OPTIONS ('format.has_header' 'true');

# test_sort_unprojected_col
query I
SELECT id FROM alltypes_plain ORDER BY int_col, double_col
----
4
6
2
0
5
7
3
1


# test_order_by_agg_expr
query R
SELECT MIN(c12) FROM aggregate_test_100 ORDER BY MIN(c12)
----
0.014793053078

# test_nulls_first_asc

query IT
SELECT * FROM (VALUES (1, 'one'), (2, 'two'), (null, 'three')) AS t (num,letter) ORDER BY num
----
1 one
2 two
NULL three

# test_nulls_first_desc()

query IT
SELECT * FROM (VALUES (1, 'one'), (2, 'two'), (null, 'three')) AS t (num,letter) ORDER BY num DESC
----
NULL three
2 two
1 one

# test_specific_nulls_last_desc

query IT
SELECT * FROM (VALUES (1, 'one'), (2, 'two'), (null, 'three')) AS t (num,letter) ORDER BY num DESC NULLS LAST
----
2 two
1 one
NULL three

# test_specific_nulls_first_asc
query IT
SELECT * FROM (VALUES (1, 'one'), (2, 'two'), (null, 'three')) AS t (num,letter) ORDER BY num ASC NULLS FIRST
----
NULL three
1 one
2 two

# sort

statement ok
CREATE EXTERNAL TABLE test (c1 int, c2 bigint, c3 boolean)
STORED AS CSV LOCATION '../core/tests/data/partitioned_csv'
OPTIONS('format.has_header' 'false');

# Demonstrate types
query TTT
SELECT arrow_typeof(c1), arrow_typeof(c2), arrow_typeof(c3) FROM test LIMIT 1;
----
Int32 Int64 Boolean

query II
SELECT c1, c2 FROM test ORDER BY c1 DESC, c2 ASC
----
3 0
3 1
3 2
3 3
3 4
3 5
3 6
3 7
3 8
3 9
3 10
2 0
2 1
2 2
2 3
2 4
2 5
2 6
2 7
2 8
2 9
2 10
1 0
1 1
1 2
1 3
1 4
1 5
1 6
1 7
1 8
1 9
1 10
0 0
0 1
0 2
0 3
0 4
0 5
0 6
0 7
0 8
0 9
0 10

# eliminate duplicated sorted expr
query TT
explain SELECT c1, c2 FROM aggregate_test_100 ORDER BY c2, c3, c2
----
logical_plan
01)Projection: aggregate_test_100.c1, aggregate_test_100.c2
02)--Sort: aggregate_test_100.c2 ASC NULLS LAST, aggregate_test_100.c3 ASC NULLS LAST
03)----TableScan: aggregate_test_100 projection=[c1, c2, c3]
physical_plan
01)ProjectionExec: expr=[c1@0 as c1, c2@1 as c2]
02)--SortExec: expr=[c2@1 ASC NULLS LAST, c3@2 ASC NULLS LAST], preserve_partitioning=[false]
03)----DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/testing/data/csv/aggregate_test_100.csv]]}, projection=[c1, c2, c3], file_type=csv, has_header=true

query II
SELECT c2, c3 FROM aggregate_test_100 ORDER BY c2, c3, c2
----
1 -99
1 -98
1 -85
1 -72
1 -56
1 -25
1 -24
1 -8
1 -5
1 12
1 29
1 36
1 38
1 41
1 54
1 57
1 70
1 71
1 83
1 103
1 120
1 125
2 -117
2 -107
2 -106
2 -61
2 -60
2 -60
2 -48
2 -43
2 -29
2 1
2 29
2 31
2 45
2 49
2 52
2 52
2 63
2 68
2 93
2 97
2 113
2 122
3 -101
3 -95
3 -76
3 -72
3 -12
3 -2
3 13
3 13
3 14
3 17
3 17
3 22
3 71
3 73
3 77
3 97
3 104
3 112
3 123
4 -117
4 -111
4 -101
4 -90
4 -79
4 -59
4 -56
4 -54
4 -53
4 -38
4 3
4 5
4 17
4 30
4 47
4 55
4 65
4 73
4 74
4 96
4 97
4 102
4 123
5 -101
5 -94
5 -86
5 -82
5 -59
5 -44
5 -40
5 -31
5 -5
5 36
5 62
5 64
5 68
5 118


# sort_empty
# The predicate on this query purposely generates no results

statement ok
SELECT c1, c2 FROM test WHERE c1 > 100000 ORDER BY c1 DESC, c2 ASC

#####
# Sorting and Grouping
#####
statement ok
create table foo as values (1, 2), (3, 4), (5, 6);

query II rowsort
select * from foo
----
1 2
3 4
5 6

query I
select column1 from foo order by column2;
----
1
3
5

query I
select column1 from foo order by column1 + column2;
----
1
3
5

query I
select column1 from foo order by column1 + column2;
----
1
3
5

query I rowsort
select column1 + column2 from foo group by column1, column2;
----
11
3
7

query I
select column1 + column2 from foo group by column1, column2 ORDER BY column2 desc;
----
11
7
3

# Test issue: https://github.com/apache/datafusion/issues/11549
query I
select column1 from foo order by log(column2);
----
1
3
5

# Test issue: https://github.com/apache/datafusion/issues/13157
query I
select column1 from foo order by column2 % 2, column2;
----
1
3
5

# Cleanup
statement ok
drop table foo;


#####
# Tests for https://github.com/apache/datafusion/issues/4854
# Ordering / grouping by the same column
#####
statement ok
create or replace table t as select column1 as value, column2 as time from (select * from (values
  (1, timestamp '2022-01-01 00:00:30'),
  (2, timestamp '2022-01-01 01:00:10'),
  (3, timestamp '2022-01-02 00:00:20')
) as sq) as sq


query IP rowsort
select
  sum(value) AS "value",
  date_trunc('hour',time) AS "time"
FROM t
GROUP BY time;
----
1 2022-01-01T00:00:00
2 2022-01-01T01:00:00
3 2022-01-02T00:00:00

# should work fine
query IP
select
  sum(value) AS "value",
  date_trunc('minute',time) AS "time"
FROM t
GROUP BY time
ORDER BY time;
----
1 2022-01-01T00:00:00
2 2022-01-01T01:00:00
3 2022-01-02T00:00:00

# Tests for https://github.com/apache/datafusion/issues/14459
query PI
select
  date_trunc('minute',time) AS "time",
  sum(value) + sum(value)
FROM t
GROUP BY time
ORDER BY sum(value) + sum(value);
----
2022-01-01T00:00:00 2
2022-01-01T01:00:00 4
2022-01-02T00:00:00 6

## SORT BY  is not supported
statement error DataFusion error: This feature is not implemented: SORT BY
select * from t SORT BY time;


# distinct on a column not in the select list should not work
statement error DataFusion error: Error during planning: For SELECT DISTINCT, ORDER BY expressions t\.time must appear in select list
SELECT DISTINCT value FROM t ORDER BY time;

# distinct on an expression of a column not in the select list should not work
statement error DataFusion error: Error during planning: For SELECT DISTINCT, ORDER BY expressions t\.time must appear in select list
SELECT DISTINCT date_trunc('hour', time)  FROM t ORDER BY time;

# distinct on a column that is in the select list but aliasted should work
query P
SELECT DISTINCT time as "first_seen" FROM t ORDER BY "first_seen";
----
2022-01-01T00:00:30
2022-01-01T01:00:10
2022-01-02T00:00:20

# distinct on a column that is in the select list, but aliased (though
# the reference is to original expr) should work
query P
SELECT DISTINCT time as "first_seen" FROM t ORDER BY time;
----
2022-01-01T00:00:30
2022-01-01T01:00:10
2022-01-02T00:00:20

# distinct on a column that is in the select list, but aliased (though
# the reference is its ordinal position) should work
query P
SELECT DISTINCT time as "first_seen" FROM t ORDER BY 1;
----
2022-01-01T00:00:30
2022-01-01T01:00:10
2022-01-02T00:00:20

## Cleanup
statement ok
drop table t;

# Create a table having 3 columns which are ordering equivalent by the source. In the next step,
# we will expect to observe the removed SortExec by propagating the orders across projection.
statement ok
CREATE EXTERNAL TABLE multiple_ordered_table (
  a0 INTEGER,
  a INTEGER,
  b INTEGER,
  c INTEGER,
  d INTEGER
)
STORED AS CSV
WITH ORDER (a ASC)
WITH ORDER (b ASC)
WITH ORDER (c ASC)
LOCATION '../core/tests/data/window_2.csv'
OPTIONS ('format.has_header' 'true');

query TT
EXPLAIN SELECT (b+a+c) AS result
FROM multiple_ordered_table
ORDER BY result;
----
logical_plan
01)Sort: result ASC NULLS LAST
02)--Projection: multiple_ordered_table.b + multiple_ordered_table.a + multiple_ordered_table.c AS result
03)----TableScan: multiple_ordered_table projection=[a, b, c]
physical_plan
01)SortPreservingMergeExec: [result@0 ASC NULLS LAST]
02)--ProjectionExec: expr=[b@1 + a@0 + c@2 as result]
03)----RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[a, b, c], output_orderings=[[a@0 ASC NULLS LAST], [b@1 ASC NULLS LAST], [c@2 ASC NULLS LAST]], file_type=csv, has_header=true

statement ok
drop table multiple_ordered_table;

# Create tables having some ordered columns. In the next step, we will expect to observe that scalar
# functions, such as mathematical functions like atan(), ceil(), sqrt(), or date_time functions
# like date_bin() and date_trunc(), will maintain the order of its argument columns.
statement ok
CREATE EXTERNAL TABLE csv_with_timestamps (
  name VARCHAR,
  ts TIMESTAMP
)
STORED AS CSV
WITH ORDER (ts ASC NULLS LAST)
LOCATION '../core/tests/data/timestamps.csv'
OPTIONS('format.has_header' 'false');

query TT
EXPLAIN SELECT DATE_BIN(INTERVAL '15 minutes', ts, TIMESTAMP '2022-08-03 14:40:00Z') as db15
FROM csv_with_timestamps
ORDER BY db15;
----
logical_plan
01)Sort: db15 ASC NULLS LAST
02)--Projection: date_bin(IntervalMonthDayNano("IntervalMonthDayNano { months: 0, days: 0, nanoseconds: 900000000000 }"), csv_with_timestamps.ts, TimestampNanosecond(1659537600000000000, None)) AS db15
03)----TableScan: csv_with_timestamps projection=[ts]
physical_plan
01)SortPreservingMergeExec: [db15@0 ASC NULLS LAST]
02)--ProjectionExec: expr=[date_bin(IntervalMonthDayNano { months: 0, days: 0, nanoseconds: 900000000000 }, ts@0, 1659537600000000000) as db15]
03)----RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/timestamps.csv]]}, projection=[ts], output_ordering=[ts@0 ASC NULLS LAST], file_type=csv, has_header=false

query TT
EXPLAIN SELECT DATE_TRUNC('DAY', ts) as dt_day
FROM csv_with_timestamps
ORDER BY dt_day;
----
logical_plan
01)Sort: dt_day ASC NULLS LAST
02)--Projection: date_trunc(Utf8("DAY"), csv_with_timestamps.ts) AS dt_day
03)----TableScan: csv_with_timestamps projection=[ts]
physical_plan
01)SortPreservingMergeExec: [dt_day@0 ASC NULLS LAST]
02)--ProjectionExec: expr=[date_trunc(DAY, ts@0) as dt_day]
03)----RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/timestamps.csv]]}, projection=[ts], output_ordering=[ts@0 ASC NULLS LAST], file_type=csv, has_header=false

statement ok
drop table csv_with_timestamps;

statement ok
drop table aggregate_test_100;

statement ok
CREATE EXTERNAL TABLE aggregate_test_100 (
  c1  VARCHAR NOT NULL,
  c2  TINYINT NOT NULL,
  c3  SMALLINT NOT NULL,
  c4  SMALLINT,
  c5  INT,
  c6  BIGINT NOT NULL,
  c7  SMALLINT NOT NULL,
  c8  INT NOT NULL,
  c9  BIGINT UNSIGNED NOT NULL,
  c10 VARCHAR NOT NULL,
  c11 FLOAT NOT NULL,
  c12 DOUBLE NOT NULL,
  c13 VARCHAR NOT NULL
)
STORED AS CSV
WITH ORDER(c11)
WITH ORDER(c12 DESC NULLS LAST)
LOCATION '../../testing/data/csv/aggregate_test_100.csv'
OPTIONS ('format.has_header' 'true');

query TT
EXPLAIN SELECT ATAN(c11) as atan_c11
FROM aggregate_test_100
ORDER BY atan_c11;
----
logical_plan
01)Sort: atan_c11 ASC NULLS LAST
02)--Projection: atan(aggregate_test_100.c11) AS atan_c11
03)----TableScan: aggregate_test_100 projection=[c11]
physical_plan
01)SortPreservingMergeExec: [atan_c11@0 ASC NULLS LAST]
02)--ProjectionExec: expr=[atan(c11@0) as atan_c11]
03)----RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/testing/data/csv/aggregate_test_100.csv]]}, projection=[c11], output_ordering=[c11@0 ASC NULLS LAST], file_type=csv, has_header=true

query TT
EXPLAIN SELECT CEIL(c11) as ceil_c11
FROM aggregate_test_100
ORDER BY ceil_c11;
----
logical_plan
01)Sort: ceil_c11 ASC NULLS LAST
02)--Projection: ceil(aggregate_test_100.c11) AS ceil_c11
03)----TableScan: aggregate_test_100 projection=[c11]
physical_plan
01)SortPreservingMergeExec: [ceil_c11@0 ASC NULLS LAST]
02)--ProjectionExec: expr=[ceil(c11@0) as ceil_c11]
03)----RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/testing/data/csv/aggregate_test_100.csv]]}, projection=[c11], output_ordering=[c11@0 ASC NULLS LAST], file_type=csv, has_header=true

query TT
  EXPLAIN SELECT LOG(c12, c11) as log_c11_base_c12
  FROM aggregate_test_100
  ORDER BY log_c11_base_c12;
----
logical_plan
01)Sort: log_c11_base_c12 ASC NULLS LAST
02)--Projection: log(aggregate_test_100.c12, CAST(aggregate_test_100.c11 AS Float64)) AS log_c11_base_c12
03)----TableScan: aggregate_test_100 projection=[c11, c12]
physical_plan
01)SortPreservingMergeExec: [log_c11_base_c12@0 ASC NULLS LAST]
02)--ProjectionExec: expr=[log(c12@1, CAST(c11@0 AS Float64)) as log_c11_base_c12]
03)----RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/testing/data/csv/aggregate_test_100.csv]]}, projection=[c11, c12], output_orderings=[[c11@0 ASC NULLS LAST], [c12@1 DESC NULLS LAST]], file_type=csv, has_header=true

query TT
EXPLAIN SELECT LOG(c11, c12) as log_c12_base_c11
FROM aggregate_test_100
ORDER BY log_c12_base_c11 DESC NULLS LAST;
----
logical_plan
01)Sort: log_c12_base_c11 DESC NULLS LAST
02)--Projection: log(CAST(aggregate_test_100.c11 AS Float64), aggregate_test_100.c12) AS log_c12_base_c11
03)----TableScan: aggregate_test_100 projection=[c11, c12]
physical_plan
01)SortPreservingMergeExec: [log_c12_base_c11@0 DESC NULLS LAST]
02)--ProjectionExec: expr=[log(CAST(c11@0 AS Float64), c12@1) as log_c12_base_c11]
03)----RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/testing/data/csv/aggregate_test_100.csv]]}, projection=[c11, c12], output_orderings=[[c11@0 ASC NULLS LAST], [c12@1 DESC NULLS LAST]], file_type=csv, has_header=true

statement ok
drop table aggregate_test_100;


# Sort with lots of repetition values
# Test sorting a parquet file with 2 million records that has lots of values that are repeated
statement ok
CREATE EXTERNAL TABLE repeat_much STORED AS PARQUET LOCATION 'data/repeat_much.snappy.parquet';

query I
SELECT a FROM repeat_much ORDER BY a LIMIT 20;
----
2450962
2450962
2450962
2450962
2450962
2450962
2450962
2450962
2450962
2450962
2450962
2450962
2450962
2450962
2450962
2450962
2450962
2450962
2450962
2450962


# Create external table with optional pre-known sort order
# ORDER BY query should be able to take advantage of the pre-existing order and drop SortExec
# in the physical plan
statement ok
set datafusion.catalog.information_schema = true;

statement ok
CREATE EXTERNAL TABLE IF NOT EXISTS orders (
        o_orderkey BIGINT,
        o_custkey BIGINT,
        o_orderstatus VARCHAR,
        o_totalprice DECIMAL(15, 2),
        o_orderdate DATE,
        o_orderpriority VARCHAR,
        o_clerk VARCHAR,
        o_shippriority INTEGER,
        o_comment VARCHAR,
) STORED AS CSV  WITH ORDER (o_orderkey ASC) LOCATION '../core/tests/tpch-csv/orders.csv'
OPTIONS ('format.delimiter' ',', 'format.has_header' 'true');

query TT
EXPLAIN SELECT o_orderkey, o_orderstatus FROM orders ORDER BY o_orderkey ASC
----
logical_plan
01)Sort: orders.o_orderkey ASC NULLS LAST
02)--TableScan: orders projection=[o_orderkey, o_orderstatus]
physical_plan DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/tpch-csv/orders.csv]]}, projection=[o_orderkey, o_orderstatus], output_ordering=[o_orderkey@0 ASC NULLS LAST], file_type=csv, has_header=true


# Create external table with DDL ordered columns that are missing
# When columns are missing the query is expected to fail
query error DataFusion error: Error during planning: Column a is not in schema
CREATE EXTERNAL TABLE dt (a_id integer, a_str string, a_bool boolean) STORED AS CSV WITH ORDER (a ASC) LOCATION 'file://path/to/table';

# Sort with duplicate sort expressions
# Table is sorted multiple times on the same column name and should not fail
statement ok
CREATE TABLE t1 (
  id INT,
  name TEXT
) AS VALUES
(2, 'a'),
(4, 'b'),
(9, 'c'),
(3, 'd'),
(4, 'e');


query IT
SELECT * FROM t1 ORDER BY id DESC, id, name, id ASC;
----
9 c
4 b
4 e
3 d
2 a

query TT
EXPLAIN SELECT * FROM t1 ORDER BY id DESC, id, name, id ASC;
----
logical_plan
01)Sort: t1.id DESC NULLS FIRST, t1.name ASC NULLS LAST
02)--TableScan: t1 projection=[id, name]
physical_plan
01)SortExec: expr=[id@0 DESC, name@1 ASC NULLS LAST], preserve_partitioning=[false]
02)--DataSourceExec: partitions=1, partition_sizes=[1]

query IT
SELECT * FROM t1 ORDER BY id ASC, id, name, id DESC;
----
2 a
3 d
4 b
4 e
9 c

query TT
EXPLAIN SELECT * FROM t1 ORDER BY id ASC, id, name, id DESC;
----
logical_plan
01)Sort: t1.id ASC NULLS LAST, t1.name ASC NULLS LAST
02)--TableScan: t1 projection=[id, name]
physical_plan
01)SortExec: expr=[id@0 ASC NULLS LAST, name@1 ASC NULLS LAST], preserve_partitioning=[false]
02)--DataSourceExec: partitions=1, partition_sizes=[1]


# Minimal reproduction of issue 5970
# https://github.com/apache/datafusion/issues/5970
statement ok
set datafusion.execution.target_partitions = 2;

statement ok
set datafusion.optimizer.repartition_sorts = true;


query II
WITH
    m0(t) AS (
        VALUES (0), (1), (2)),
    m1(t) AS (
        VALUES (0), (1)),
    u AS (
        SELECT 0 as m, t FROM m0 GROUP BY 1, 2),
    v AS (
        SELECT 1 as m, t FROM m1 GROUP BY 1, 2)
SELECT * FROM u
UNION ALL
SELECT * FROM v
ORDER BY 1, 2;
----
0 0
0 1
0 2
1 0
1 1

query TT
EXPLAIN
WITH
    m0(t) AS (
        VALUES (0), (1), (2)),
    m1(t) AS (
        VALUES (0), (1)),
    u AS (
        SELECT 0 as m, t FROM m0 GROUP BY 1, 2),
    v AS (
        SELECT 1 as m, t FROM m1 GROUP BY 1, 2)
SELECT * FROM u
UNION ALL
SELECT * FROM v
ORDER BY 1, 2;
----
logical_plan
01)Sort: m ASC NULLS LAST, t ASC NULLS LAST
02)--Union
03)----SubqueryAlias: u
04)------Projection: Int64(0) AS m, m0.t
05)--------Aggregate: groupBy=[[m0.t]], aggr=[[]]
06)----------SubqueryAlias: m0
07)------------Projection: column1 AS t
08)--------------Values: (Int64(0)), (Int64(1)), (Int64(2))
09)----SubqueryAlias: v
10)------Projection: Int64(1) AS m, m1.t
11)--------Aggregate: groupBy=[[m1.t]], aggr=[[]]
12)----------SubqueryAlias: m1
13)------------Projection: column1 AS t
14)--------------Values: (Int64(0)), (Int64(1))
physical_plan
01)SortPreservingMergeExec: [m@0 ASC NULLS LAST, t@1 ASC NULLS LAST]
02)--SortExec: expr=[m@0 ASC NULLS LAST, t@1 ASC NULLS LAST], preserve_partitioning=[true]
03)----InterleaveExec
04)------ProjectionExec: expr=[0 as m, t@0 as t]
05)--------AggregateExec: mode=FinalPartitioned, gby=[t@0 as t], aggr=[]
06)----------CoalesceBatchesExec: target_batch_size=8192
07)------------RepartitionExec: partitioning=Hash([t@0], 2), input_partitions=2
08)--------------RepartitionExec: partitioning=RoundRobinBatch(2), input_partitions=1
09)----------------AggregateExec: mode=Partial, gby=[t@0 as t], aggr=[]
10)------------------ProjectionExec: expr=[column1@0 as t]
11)--------------------DataSourceExec: partitions=1, partition_sizes=[1]
12)------ProjectionExec: expr=[1 as m, t@0 as t]
13)--------AggregateExec: mode=FinalPartitioned, gby=[t@0 as t], aggr=[]
14)----------CoalesceBatchesExec: target_batch_size=8192
15)------------RepartitionExec: partitioning=Hash([t@0], 2), input_partitions=2
16)--------------RepartitionExec: partitioning=RoundRobinBatch(2), input_partitions=1
17)----------------AggregateExec: mode=Partial, gby=[t@0 as t], aggr=[]
18)------------------ProjectionExec: expr=[column1@0 as t]
19)--------------------DataSourceExec: partitions=1, partition_sizes=[1]

#####
# Multi column sorting with lists
#####

statement ok
create table foo as values (2, [0]), (4, [1]), (2, [6]), (1, [2, 5]), (0, [3]), (3, [4]), (2, [2, 5]), (2, [7]);

query I?
select column1, column2 from foo ORDER BY column1, column2;
----
0 [3]
1 [2, 5]
2 [0]
2 [2, 5]
2 [6]
2 [7]
3 [4]
4 [1]

query I?
select column1, column2 from foo ORDER BY column1 desc, column2;
----
4 [1]
3 [4]
2 [0]
2 [2, 5]
2 [6]
2 [7]
1 [2, 5]
0 [3]

query I?
select column1, column2 from foo ORDER BY column1, column2 desc;
----
0 [3]
1 [2, 5]
2 [7]
2 [6]
2 [2, 5]
2 [0]
3 [4]
4 [1]

query I?
select column1, column2 from foo ORDER BY column1 desc, column2 desc;
----
4 [1]
3 [4]
2 [7]
2 [6]
2 [2, 5]
2 [0]
1 [2, 5]
0 [3]

query ?I
select column2, column1 from foo ORDER BY column2, column1;
----
[0] 2
[1] 4
[2, 5] 1
[2, 5] 2
[3] 0
[4] 3
[6] 2
[7] 2

query ?I
select column2, column1 from foo ORDER BY column2 desc, column1;
----
[7] 2
[6] 2
[4] 3
[3] 0
[2, 5] 1
[2, 5] 2
[1] 4
[0] 2

query ?I
select column2, column1 from foo ORDER BY column2, column1 desc;
----
[0] 2
[1] 4
[2, 5] 2
[2, 5] 1
[3] 0
[4] 3
[6] 2
[7] 2

query ?I
select column2, column1 from foo ORDER BY column2 desc, column1 desc;
----
[7] 2
[6] 2
[4] 3
[3] 0
[2, 5] 2
[2, 5] 1
[1] 4
[0] 2

# Test issue: https://github.com/apache/datafusion/issues/10013
# There is both a HAVING clause and an ORDER BY clause in the query.
query I
SELECT
   SUM(column1)
FROM foo
HAVING SUM(column1) > 0
ORDER BY SUM(column1)
----
16

# ORDER BY with a GROUP BY clause
query I
SELECT SUM(column1) 
  FROM foo 
GROUP BY column2 
ORDER BY SUM(column1) 
----
0
2
2
2
3
3
4

# ORDER BY with a GROUP BY clause and a HAVING clause
query I
SELECT 
  SUM(column1) 
FROM foo 
GROUP BY column2 
HAVING SUM(column1) < 3 
ORDER BY SUM(column1) 
----
0
2
2
2

# ORDER BY without a HAVING clause
query I
SELECT SUM(column1) FROM foo ORDER BY SUM(column1)
----
16

# Order by unprojected aggregate expressions is not supported
query error DataFusion error: This feature is not implemented: Physical plan does not support logical expression AggregateFunction
SELECT column2 FROM foo ORDER BY SUM(column1)

statement ok
create table ambiguity_test(a int, b int) as values (1,20), (2,10);

# Order-by expressions prioritize referencing columns from the select list.
query II
select a as b, b as c2 from ambiguity_test order by b
----
1 20
2 10

# Cleanup
statement ok
drop table foo;

statement ok
drop table ambiguity_test;

## reproducer for https://github.com/apache/datafusion/issues/12446
# Ensure union ordering calculations with constants can be optimized

statement ok
create table t(a0 int, a int, b int, c int) as values (1, 2, 3, 4), (5, 6, 7, 8);

# expect this query to run successfully, not error
query IIII
select * from (select c, a, NULL::int as a0, b from t order by a, c) t1
union all
select * from (select c, NULL::int as a, a0, b from t order by a0, c) t2
order by c, a, a0, b
limit 2;
----
4 2 NULL 3
4 NULL 1 3

query III
select * from (select c, a, NULL::int as a0 from t order by a, c) t1
union all
select * from (select c, NULL::int as a, a0 from t order by a0, c) t2
order by c, a, a0
limit 2;
----
4 2 NULL
4 NULL 1

# Casting from numeric to string types breaks the ordering
statement ok
CREATE EXTERNAL TABLE ordered_table (
  a0 INT,
  a INT,
  b INT,
  c INT,
  d INT
)
STORED AS CSV
WITH ORDER (c ASC)
LOCATION '../core/tests/data/window_2.csv'
OPTIONS ('format.has_header' 'true');

query T
SELECT CAST(c as VARCHAR) as c_str
FROM ordered_table
ORDER BY c_str
limit 5;
----
0
1
10
11
12

query TT
EXPLAIN SELECT CAST(c as VARCHAR) as c_str
FROM ordered_table
ORDER BY c_str
limit 5;
----
logical_plan
01)Sort: c_str ASC NULLS LAST, fetch=5
02)--Projection: CAST(ordered_table.c AS Utf8) AS c_str
03)----TableScan: ordered_table projection=[c]
physical_plan
01)SortPreservingMergeExec: [c_str@0 ASC NULLS LAST], fetch=5
02)--SortExec: TopK(fetch=5), expr=[c_str@0 ASC NULLS LAST], preserve_partitioning=[true]
03)----ProjectionExec: expr=[CAST(c@0 AS Utf8) as c_str]
04)------RepartitionExec: partitioning=RoundRobinBatch(2), input_partitions=1
05)--------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[c], output_ordering=[c@0 ASC NULLS LAST], file_type=csv, has_header=true


# Casting from numeric to numeric types preserves the ordering
query I
SELECT CAST(c as BIGINT) as c_bigint
FROM ordered_table
ORDER BY c_bigint
limit 5;
----
0
1
2
3
4

query TT
EXPLAIN SELECT CAST(c as BIGINT) as c_bigint
FROM ordered_table
ORDER BY c_bigint
limit 5;
----
logical_plan
01)Sort: c_bigint ASC NULLS LAST, fetch=5
02)--Projection: CAST(ordered_table.c AS Int64) AS c_bigint
03)----TableScan: ordered_table projection=[c]
physical_plan
01)SortPreservingMergeExec: [c_bigint@0 ASC NULLS LAST], fetch=5
02)--ProjectionExec: expr=[CAST(c@0 AS Int64) as c_bigint]
03)----RepartitionExec: partitioning=RoundRobinBatch(2), input_partitions=1
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[c], output_ordering=[c@0 ASC NULLS LAST], file_type=csv, has_header=true

statement ok
drop table ordered_table; 


# ABS(x) breaks the ordering if x's range contains both negative and positive values.
# Since x is defined as INT, its range is assumed to be from NEG_INF to INF.
statement ok
CREATE EXTERNAL TABLE ordered_table (
  a0 INT,
  a INT,
  b INT,
  c INT,
  d INT
)
STORED AS CSV
WITH ORDER (c ASC)
LOCATION '../core/tests/data/window_2.csv'
OPTIONS ('format.has_header' 'true');

query TT
EXPLAIN SELECT ABS(c) as abs_c
FROM ordered_table
ORDER BY abs_c
limit 5;
----
logical_plan
01)Sort: abs_c ASC NULLS LAST, fetch=5
02)--Projection: abs(ordered_table.c) AS abs_c
03)----TableScan: ordered_table projection=[c]
physical_plan
01)SortPreservingMergeExec: [abs_c@0 ASC NULLS LAST], fetch=5
02)--SortExec: TopK(fetch=5), expr=[abs_c@0 ASC NULLS LAST], preserve_partitioning=[true]
03)----ProjectionExec: expr=[abs(c@0) as abs_c]
04)------RepartitionExec: partitioning=RoundRobinBatch(2), input_partitions=1
05)--------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[c], output_ordering=[c@0 ASC NULLS LAST], file_type=csv, has_header=true

statement ok
drop table ordered_table; 

# ABS(x) preserves the ordering if x's range falls into positive values.
# Since x is defined as INT UNSIGNED, its range is assumed to be from 0 to INF.
statement ok
CREATE EXTERNAL TABLE ordered_table (
  a0 INT,
  a INT,
  b INT,
  c INT UNSIGNED,
  d INT
)
STORED AS CSV
WITH ORDER (c ASC)
LOCATION '../core/tests/data/window_2.csv'
OPTIONS ('format.has_header' 'true');

query TT
EXPLAIN SELECT ABS(c) as abs_c
FROM ordered_table
ORDER BY abs_c
limit 5;
----
logical_plan
01)Sort: abs_c ASC NULLS LAST, fetch=5
02)--Projection: abs(ordered_table.c) AS abs_c
03)----TableScan: ordered_table projection=[c]
physical_plan
01)SortPreservingMergeExec: [abs_c@0 ASC NULLS LAST], fetch=5
02)--ProjectionExec: expr=[abs(c@0) as abs_c]
03)----RepartitionExec: partitioning=RoundRobinBatch(2), input_partitions=1
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[c], output_ordering=[c@0 ASC NULLS LAST], file_type=csv, has_header=true

# Boolean to integer casts preserve the order.
statement ok
CREATE EXTERNAL TABLE annotated_data_finite (
  ts INTEGER,
  inc_col INTEGER,
  desc_col INTEGER,
)
STORED AS CSV
WITH ORDER (inc_col ASC)
WITH ORDER (desc_col DESC)
LOCATION '../core/tests/data/window_1.csv'
OPTIONS ('format.has_header' 'true');

query TT
EXPLAIN SELECT CAST((inc_col>desc_col) as integer) as c from annotated_data_finite order by c;
----
logical_plan
01)Sort: c ASC NULLS LAST
02)--Projection: CAST(annotated_data_finite.inc_col > annotated_data_finite.desc_col AS Int32) AS c
03)----TableScan: annotated_data_finite projection=[inc_col, desc_col]
physical_plan
01)SortPreservingMergeExec: [c@0 ASC NULLS LAST]
02)--ProjectionExec: expr=[CAST(inc_col@0 > desc_col@1 AS Int32) as c]
03)----RepartitionExec: partitioning=RoundRobinBatch(2), input_partitions=1
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_1.csv]]}, projection=[inc_col, desc_col], output_orderings=[[inc_col@0 ASC NULLS LAST], [desc_col@1 DESC]], file_type=csv, has_header=true

# Union a query with the actual data and one with a constant
query I
SELECT (SELECT c from ordered_table ORDER BY c LIMIT 1) UNION ALL (SELECT 23 as c from ordered_table ORDER BY c LIMIT 1) ORDER BY c;
----
0
23

# Do not increase partition number after fetch 1. As this will be unnecessary.
query TT
EXPLAIN SELECT a + b as sum1 FROM (SELECT a, b
  FROM ordered_table
  ORDER BY a ASC LIMIT 1
);
----
logical_plan
01)Projection: ordered_table.a + ordered_table.b AS sum1
02)--Sort: ordered_table.a ASC NULLS LAST, fetch=1
03)----TableScan: ordered_table projection=[a, b]
physical_plan
01)ProjectionExec: expr=[a@0 + b@1 as sum1]
02)--RepartitionExec: partitioning=RoundRobinBatch(2), input_partitions=1
03)----SortExec: TopK(fetch=1), expr=[a@0 ASC NULLS LAST], preserve_partitioning=[false]
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[a, b], file_type=csv, has_header=true

statement ok
set datafusion.execution.use_row_number_estimates_to_optimize_partitioning = true;

# Do not increase the number of partitions after fetch one, as this will be unnecessary.
query TT
EXPLAIN SELECT a + b as sum1 FROM (SELECT a, b
  FROM ordered_table
  ORDER BY a ASC LIMIT 1
);
----
logical_plan
01)Projection: ordered_table.a + ordered_table.b AS sum1
02)--Sort: ordered_table.a ASC NULLS LAST, fetch=1
03)----TableScan: ordered_table projection=[a, b]
physical_plan
01)ProjectionExec: expr=[a@0 + b@1 as sum1]
02)--SortExec: TopK(fetch=1), expr=[a@0 ASC NULLS LAST], preserve_partitioning=[false]
03)----DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[a, b], file_type=csv, has_header=true

statement ok
set datafusion.execution.use_row_number_estimates_to_optimize_partitioning = false;

# Here, we have multiple partitions after fetch one, since the row count estimate is not exact.
query TT
EXPLAIN SELECT a + b as sum1 FROM (SELECT a, b
  FROM ordered_table
  ORDER BY a ASC LIMIT 1
);
----
logical_plan
01)Projection: ordered_table.a + ordered_table.b AS sum1
02)--Sort: ordered_table.a ASC NULLS LAST, fetch=1
03)----TableScan: ordered_table projection=[a, b]
physical_plan
01)ProjectionExec: expr=[a@0 + b@1 as sum1]
02)--RepartitionExec: partitioning=RoundRobinBatch(2), input_partitions=1
03)----SortExec: TopK(fetch=1), expr=[a@0 ASC NULLS LAST], preserve_partitioning=[false]
04)------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[a, b], file_type=csv, has_header=true


# Test: inputs into union with different orderings
query TT
explain select * from (select b, c, a, NULL::int as a0, d from ordered_table order by a, c) t1
union all
select * from (select b, c, NULL::int as a, a0, d from ordered_table order by a0, c) t2
order by d, c, a, a0, b
limit 2;
----
logical_plan
01)Sort: d ASC NULLS LAST, c ASC NULLS LAST, a ASC NULLS LAST, a0 ASC NULLS LAST, b ASC NULLS LAST, fetch=2
02)--Union
03)----SubqueryAlias: t1
04)------Projection: ordered_table.b, ordered_table.c, ordered_table.a, Int32(NULL) AS a0, ordered_table.d
05)--------TableScan: ordered_table projection=[a, b, c, d]
06)----SubqueryAlias: t2
07)------Projection: ordered_table.b, ordered_table.c, Int32(NULL) AS a, ordered_table.a0, ordered_table.d
08)--------TableScan: ordered_table projection=[a0, b, c, d]
physical_plan
01)SortPreservingMergeExec: [d@4 ASC NULLS LAST, c@1 ASC NULLS LAST, a@2 ASC NULLS LAST, a0@3 ASC NULLS LAST, b@0 ASC NULLS LAST], fetch=2
02)--UnionExec
03)----SortExec: TopK(fetch=2), expr=[d@4 ASC NULLS LAST, c@1 ASC NULLS LAST, a@2 ASC NULLS LAST, b@0 ASC NULLS LAST], preserve_partitioning=[false]
04)------ProjectionExec: expr=[b@1 as b, c@2 as c, a@0 as a, NULL as a0, d@3 as d]
05)--------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[a, b, c, d], output_ordering=[c@2 ASC NULLS LAST], file_type=csv, has_header=true
06)----SortExec: TopK(fetch=2), expr=[d@4 ASC NULLS LAST, c@1 ASC NULLS LAST, a0@3 ASC NULLS LAST, b@0 ASC NULLS LAST], preserve_partitioning=[false]
07)------ProjectionExec: expr=[b@1 as b, c@2 as c, NULL as a, a0@0 as a0, d@3 as d]
08)--------DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/window_2.csv]]}, projection=[a0, b, c, d], output_ordering=[c@2 ASC NULLS LAST], file_type=csv, has_header=true

# Test: run the query from above
query IIIII
select * from (select b, c, a, NULL::int as a0, d from ordered_table order by a, c) t1
union all
select * from (select b, c, NULL::int as a, a0, d from ordered_table order by a0, c) t2
order by d, c, a, a0, b
limit 2;
----
0 0 0 NULL 0
0 0 NULL 1 0


statement ok
drop table ordered_table;

query TT
EXPLAIN SELECT
    CASE
        WHEN name = 'name1' THEN 0.0
        WHEN name = 'name2' THEN 0.5
    END AS a
FROM (
    SELECT 'name1' AS name
    UNION ALL
    SELECT 'name2'
)
ORDER BY a DESC;
----
logical_plan
01)Sort: a DESC NULLS FIRST
02)--Projection: CASE WHEN name = Utf8("name1") THEN Float64(0) WHEN name = Utf8("name2") THEN Float64(0.5) END AS a
03)----Union
04)------Projection: Utf8("name1") AS name
05)--------EmptyRelation
06)------Projection: Utf8("name2") AS name
07)--------EmptyRelation
physical_plan
01)SortPreservingMergeExec: [a@0 DESC]
02)--ProjectionExec: expr=[CASE WHEN name@0 = name1 THEN 0 WHEN name@0 = name2 THEN 0.5 END as a]
03)----UnionExec
04)------ProjectionExec: expr=[name1 as name]
05)--------PlaceholderRowExec
06)------ProjectionExec: expr=[name2 as name]
07)--------PlaceholderRowExec

query R
SELECT
    CASE
        WHEN name = 'name1' THEN 0.0
        WHEN name = 'name2' THEN 0.5
    END AS a
FROM (
    SELECT 'name1' AS name
    UNION ALL
    SELECT 'name2'
)
ORDER BY a DESC;
----
0.5
0

# Test that when a column has a unique non-null constraint (via PRIMARY KEY) and is ordered (via WITH ORDER),
# we can eliminate sorts on derived lexicographical orderings that start with that column.
statement ok
CREATE EXTERNAL TABLE table_with_ordered_pk (
  c1 INT,
  c2 INT,
  c3 INT,
  PRIMARY KEY (c1)
)
STORED AS CSV
WITH ORDER (c1 ASC)
OPTIONS ('format.has_header' 'true')
LOCATION '../core/tests/data/aggregate_agg_multi_order.csv';

query TT
EXPLAIN SELECT c1, c2 FROM table_with_ordered_pk ORDER BY c1, c2;
----
logical_plan
01)Sort: table_with_ordered_pk.c1 ASC NULLS LAST, table_with_ordered_pk.c2 ASC NULLS LAST
02)--TableScan: table_with_ordered_pk projection=[c1, c2]
physical_plan DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/aggregate_agg_multi_order.csv]]}, projection=[c1, c2], output_ordering=[c1@0 ASC NULLS LAST], constraints=[PrimaryKey([0])], file_type=csv, has_header=true

statement ok
drop table table_with_ordered_pk;

statement ok
set datafusion.execution.target_partitions = 1;

statement ok
set datafusion.explain.physical_plan_only = true;

# Aggregation operation also can introduce unique values
statement ok
CREATE EXTERNAL TABLE table_with_ordered_not_null (
  c1 INT NOT NULL,
  c2 INT,
  c3 INT,
)
STORED AS CSV
WITH ORDER (c1)
OPTIONS ('format.has_header' 'true')
LOCATION '../core/tests/data/aggregate_agg_multi_order.csv';

query TT
EXPLAIN SELECT c1, SUM(c2) as sum_c2 FROM table_with_ordered_not_null GROUP BY c1 ORDER BY c1, sum_c2;
----
physical_plan
01)ProjectionExec: expr=[c1@0 as c1, sum(table_with_ordered_not_null.c2)@1 as sum_c2]
02)--AggregateExec: mode=Single, gby=[c1@0 as c1], aggr=[sum(table_with_ordered_not_null.c2)], ordering_mode=Sorted
03)----DataSourceExec: file_groups={1 group: [[WORKSPACE_ROOT/datafusion/core/tests/data/aggregate_agg_multi_order.csv]]}, projection=[c1, c2], output_ordering=[c1@0 ASC NULLS LAST], file_type=csv, has_header=true

statement ok
drop table table_with_ordered_not_null;
