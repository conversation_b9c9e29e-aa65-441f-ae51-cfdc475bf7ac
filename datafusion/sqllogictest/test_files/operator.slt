# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# Validate arithmetic operator output type

statement ok
create table numeric_types as
SELECT
    arrow_cast(1, 'Int8') as int8,
    arrow_cast(1, 'Int16') as int16,
    arrow_cast(1, 'Int32') as int32,
    arrow_cast(1, 'Int64') as int64,
    arrow_cast(1, 'UInt8') as uint8,
    arrow_cast(1, 'UInt16') as uint16,
    arrow_cast(1, 'UInt32') as uint32,
    arrow_cast(1, 'UInt64') as uint64,
    arrow_cast(1.23, 'Float32') as float32,
    arrow_cast(1.23, 'Float64') as float64,
    arrow_cast(1.25, 'Decimal128(5, 2)') as decimal
;

############### Addition ###############

# Plus with the same operand type, expect the same output type
# except for decimal which is  promoted to the highest precision
query TTTTTTTTTTT
select
    arrow_typeof(int8 + int8),
    arrow_typeof(int16 + int16),
    arrow_typeof(int32 + int32),
    arrow_typeof(int64 + int64),
    arrow_typeof(uint8 + uint8),
    arrow_typeof(uint16 + uint16),
    arrow_typeof(uint32 + uint32),
    arrow_typeof(uint64 + uint64),
    arrow_typeof(float32 + float32),
    arrow_typeof(float64 + float64),
    arrow_typeof(decimal + decimal)
from numeric_types;
----
Int8 Int16 Int32 Int64 UInt8 UInt16 UInt32 UInt64 Float32 Float64 Decimal128(6, 2)

# Plus with literal integer
query TTTTTTTTTTT
select
    arrow_typeof(int8 + 2),
    arrow_typeof(int16 + 2),
    arrow_typeof(int32 + 2),
    arrow_typeof(int64 + 2),
    arrow_typeof(uint8 + 2),
    arrow_typeof(uint16 + 2),
    arrow_typeof(uint32 + 2),
    arrow_typeof(uint64 + 2),
    arrow_typeof(float32 + 2),
    arrow_typeof(float64 + 2),
    arrow_typeof(decimal + 2)
from numeric_types;
----
Int64 Int64 Int64 Int64 Int64 Int64 Int64 Decimal128(21, 0) Float32 Float64 Decimal128(23, 2)

# Plus with literal decimal
query TTTTTTTTTTT
select
    arrow_typeof(int8 + 2.0),
    arrow_typeof(int16 + 2.0),
    arrow_typeof(int32 + 2.0),
    arrow_typeof(int64 + 2.0),
    arrow_typeof(uint8 + 2.0),
    arrow_typeof(uint16 + 2.0),
    arrow_typeof(uint32 + 2.0),
    arrow_typeof(uint64 + 2.0),
    arrow_typeof(float32 + 2.0),
    arrow_typeof(float64 + 2.0),
    arrow_typeof(decimal + 2.0)
from numeric_types;
----
Float64 Float64 Float64 Float64 Float64 Float64 Float64 Float64 Float64 Float64 Float64

############### Subtraction ###############

# Minus with the same operand type, expect the same output type
# except for decimal which is promoted to the highest precision
query TTTTTTTTTTT
select
    arrow_typeof(int8 - int8),
    arrow_typeof(int16 - int16),
    arrow_typeof(int32 - int32),
    arrow_typeof(int64 - int64),
    arrow_typeof(uint8 - uint8),
    arrow_typeof(uint16 - uint16),
    arrow_typeof(uint32 - uint32),
    arrow_typeof(uint64 - uint64),
    arrow_typeof(float32 - float32),
    arrow_typeof(float64 - float64),
    arrow_typeof(decimal - decimal)
from numeric_types;
----
Int8 Int16 Int32 Int64 UInt8 UInt16 UInt32 UInt64 Float32 Float64 Decimal128(6, 2)

# Minus with literal integer
query TTTTTTTTTTT
select
    arrow_typeof(int8 - 2),
    arrow_typeof(int16 - 2),
    arrow_typeof(int32 - 2),
    arrow_typeof(int64 - 2),
    arrow_typeof(uint8 - 2),
    arrow_typeof(uint16 - 2),
    arrow_typeof(uint32 - 2),
    arrow_typeof(uint64 - 2),
    arrow_typeof(float32 - 2),
    arrow_typeof(float64 - 2),
    arrow_typeof(decimal - 2)
from numeric_types;
----
Int64 Int64 Int64 Int64 Int64 Int64 Int64 Decimal128(21, 0) Float32 Float64 Decimal128(23, 2)

# Minus with literal decimal
query TTTTTTTTTTT
select
    arrow_typeof(int8 - 2.0),
    arrow_typeof(int16 - 2.0),
    arrow_typeof(int32 - 2.0),
    arrow_typeof(int64 - 2.0),
    arrow_typeof(uint8 - 2.0),
    arrow_typeof(uint16 - 2.0),
    arrow_typeof(uint32 - 2.0),
    arrow_typeof(uint64 - 2.0),
    arrow_typeof(float32 - 2.0),
    arrow_typeof(float64 - 2.0),
    arrow_typeof(decimal - 2.0)
from numeric_types;
----
Float64 Float64 Float64 Float64 Float64 Float64 Float64 Float64 Float64 Float64 Float64

############### Multiplication ###############

# Multiply with the same operand type, expect the same output type
# except for decimal which is promoted to the highest precision
query TTTTTTTTTTT
select
    arrow_typeof(int8 * int8),
    arrow_typeof(int16 * int16),
    arrow_typeof(int32 * int32),
    arrow_typeof(int64 * int64),
    arrow_typeof(uint8 * uint8),
    arrow_typeof(uint16 * uint16),
    arrow_typeof(uint32 * uint32),
    arrow_typeof(uint64 * uint64),
    arrow_typeof(float32 * float32),
    arrow_typeof(float64 * float64),
    arrow_typeof(decimal * decimal)
from numeric_types;
----
Int8 Int16 Int32 Int64 UInt8 UInt16 UInt32 UInt64 Float32 Float64 Decimal128(11, 4)

# Multiply with literal integer
query TTTTTTTTTTT
select
    arrow_typeof(int8 * 2),
    arrow_typeof(int16 * 2),
    arrow_typeof(int32 * 2),
    arrow_typeof(int64 * 2),
    arrow_typeof(uint8 * 2),
    arrow_typeof(uint16 * 2),
    arrow_typeof(uint32 * 2),
    arrow_typeof(uint64 * 2),
    arrow_typeof(float32 * 2),
    arrow_typeof(float64 * 2),
    arrow_typeof(decimal * 2)
from numeric_types;
----
Int64 Int64 Int64 Int64 Int64 Int64 Int64 Decimal128(38, 0) Float32 Float64 Decimal128(26, 2)

# Multiply with literal decimal
query TTTTTTTTTTT
select
    arrow_typeof(int8 * 2.0),
    arrow_typeof(int16 * 2.0),
    arrow_typeof(int32 * 2.0),
    arrow_typeof(int64 * 2.0),
    arrow_typeof(uint8 * 2.0),
    arrow_typeof(uint16 * 2.0),
    arrow_typeof(uint32 * 2.0),
    arrow_typeof(uint64 * 2.0),
    arrow_typeof(float32 * 2.0),
    arrow_typeof(float64 * 2.0),
    arrow_typeof(decimal * 2.0)
from numeric_types;
----
Float64 Float64 Float64 Float64 Float64 Float64 Float64 Float64 Float64 Float64 Float64

############### Division ###############


# Divide with the same operand type, expect the same output type
# except for decimal which is promoted to the highest precision
query TTTTTTTTTTT
select
    arrow_typeof(int8 / int8),
    arrow_typeof(int16 / int16),
    arrow_typeof(int32 / int32),
    arrow_typeof(int64 / int64),
    arrow_typeof(uint8 / uint8),
    arrow_typeof(uint16 / uint16),
    arrow_typeof(uint32 / uint32),
    arrow_typeof(uint64 / uint64),
    arrow_typeof(float32 / float32),
    arrow_typeof(float64 / float64),
    arrow_typeof(decimal / decimal)
from numeric_types;
----
Int8 Int16 Int32 Int64 UInt8 UInt16 UInt32 UInt64 Float32 Float64 Decimal128(11, 6)

# Divide with literal integer
query TTTTTTTTTTT
select
    arrow_typeof(int8 / 2),
    arrow_typeof(int16 / 2),
    arrow_typeof(int32 / 2),
    arrow_typeof(int64 / 2),
    arrow_typeof(uint8 / 2),
    arrow_typeof(uint16 / 2),
    arrow_typeof(uint32 / 2),
    arrow_typeof(uint64 / 2),
    arrow_typeof(float32 / 2),
    arrow_typeof(float64 / 2),
    arrow_typeof(decimal / 2)
from numeric_types;
----
Int64 Int64 Int64 Int64 Int64 Int64 Int64 Decimal128(24, 4) Float32 Float64 Decimal128(9, 6)

# Divide with literal decimal
query TTTTTTTTTTT
select
    arrow_typeof(int8 / 2.0),
    arrow_typeof(int16 / 2.0),
    arrow_typeof(int32 / 2.0),
    arrow_typeof(int64 / 2.0),
    arrow_typeof(uint8 / 2.0),
    arrow_typeof(uint16 / 2.0),
    arrow_typeof(uint32 / 2.0),
    arrow_typeof(uint64 / 2.0),
    arrow_typeof(float32 / 2.0),
    arrow_typeof(float64 / 2.0),
    arrow_typeof(decimal / 2.0)
from numeric_types;
----
Float64 Float64 Float64 Float64 Float64 Float64 Float64 Float64 Float64 Float64 Float64

###############
# Test for comparison with constants uses efficient types
# Expect the physical plans to compare with constants of the same type
# should have no casts of the column to a different type

statement ok
set datafusion.explain.physical_plan_only = true;

############### Less Than ###############

## < positive integer (expect no casts)
query TT
EXPLAIN SELECT * FROM numeric_types
WHERE  int64 < 5 AND uint64 < 5 AND float64 < 5 AND decimal < 5;
----
physical_plan
01)CoalesceBatchesExec: target_batch_size=8192
02)--FilterExec: int64@3 < 5 AND uint64@7 < 5 AND float64@9 < 5 AND decimal@10 < Some(500),5,2
03)----DataSourceExec: partitions=1, partition_sizes=[1]

## < negative  integer (expect no casts)
query TT
EXPLAIN SELECT * FROM numeric_types
WHERE  int64 < -5 AND uint64 < -5 AND float64 < -5 AND decimal < -5;
----
physical_plan
01)CoalesceBatchesExec: target_batch_size=8192
02)--FilterExec: int64@3 < -5 AND CAST(uint64@7 AS Decimal128(20, 0)) < Some(-5),20,0 AND float64@9 < -5 AND decimal@10 < Some(-500),5,2
03)----DataSourceExec: partitions=1, partition_sizes=[1]

## < decimal (expect casts for integers to float)
query TT
EXPLAIN SELECT * FROM numeric_types
WHERE  int64 < 5.1 AND uint64 < 5.1 AND float64 < 5.1 AND decimal < 5.1;
----
physical_plan
01)CoalesceBatchesExec: target_batch_size=8192
02)--FilterExec: CAST(int64@3 AS Float64) < 5.1 AND CAST(uint64@7 AS Float64) < 5.1 AND float64@9 < 5.1 AND decimal@10 < Some(510),5,2
03)----DataSourceExec: partitions=1, partition_sizes=[1]

## < negative decimal (expect casts for integers to float)
query TT
EXPLAIN SELECT * FROM numeric_types
WHERE  int64 < -5.1 AND uint64 < -5.1 AND float64 < -5.1 AND decimal < -5.1;
----
physical_plan
01)CoalesceBatchesExec: target_batch_size=8192
02)--FilterExec: CAST(int64@3 AS Float64) < -5.1 AND CAST(uint64@7 AS Float64) < -5.1 AND float64@9 < -5.1 AND decimal@10 < Some(-510),5,2
03)----DataSourceExec: partitions=1, partition_sizes=[1]


############### Equality ###############

## = positive integer (expect no casts)
query TT
EXPLAIN SELECT * FROM numeric_types
WHERE  int64 = 5 AND uint64 = 5 AND float64 = 5 AND decimal = 5;
----
physical_plan
01)CoalesceBatchesExec: target_batch_size=8192
02)--FilterExec: int64@3 = 5 AND uint64@7 = 5 AND float64@9 = 5 AND decimal@10 = Some(500),5,2
03)----DataSourceExec: partitions=1, partition_sizes=[1]

## = negative  integer (expect no casts)
query TT
EXPLAIN SELECT * FROM numeric_types
WHERE  int64 = -5 AND uint64 = -5 AND float64 = -5 AND decimal = -5;
----
physical_plan
01)CoalesceBatchesExec: target_batch_size=8192
02)--FilterExec: int64@3 = -5 AND CAST(uint64@7 AS Decimal128(20, 0)) = Some(-5),20,0 AND float64@9 = -5 AND decimal@10 = Some(-500),5,2
03)----DataSourceExec: partitions=1, partition_sizes=[1]

## = decimal (expect casts for integers to float)
query TT
EXPLAIN SELECT * FROM numeric_types
WHERE  int64 = 5.1 AND uint64 = 5.1 AND float64 = 5.1 AND decimal = 5.1;
----
physical_plan
01)CoalesceBatchesExec: target_batch_size=8192
02)--FilterExec: CAST(int64@3 AS Float64) = 5.1 AND CAST(uint64@7 AS Float64) = 5.1 AND float64@9 = 5.1 AND decimal@10 = Some(510),5,2
03)----DataSourceExec: partitions=1, partition_sizes=[1]

## = negative decimal (expect casts for integers to float)
query TT
EXPLAIN SELECT * FROM numeric_types
WHERE  int64 = -5.1 AND uint64 = -5.1 AND float64 = -5.1 AND decimal = -5.1;
----
physical_plan
01)CoalesceBatchesExec: target_batch_size=8192
02)--FilterExec: CAST(int64@3 AS Float64) = -5.1 AND CAST(uint64@7 AS Float64) = -5.1 AND float64@9 = -5.1 AND decimal@10 = Some(-510),5,2
03)----DataSourceExec: partitions=1, partition_sizes=[1]


statement ok
set datafusion.explain.physical_plan_only = false;


statement ok
drop table numeric_types
