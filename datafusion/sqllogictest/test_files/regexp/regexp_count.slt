# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# Import common test data
include ./init_data.slt.part

# regexp_count tests from postgresql
# https://github.com/postgres/postgres/blob/56d23855c864b7384970724f3ad93fb0fc319e51/src/test/regress/sql/strings.sql#L226-L235

query I
SELECT regexp_count('123123123123123', '(12)3');
----
5

query I
SELECT regexp_count('123123123123', '123', 1);
----
4

query I
SELECT regexp_count('123123123123', '123', 3);
----
3

query I
SELECT regexp_count('123123123123', '123', 33);
----
0

query I
SELECT regexp_count('ABCABCABCABC', 'Abc', 1, '');
----
0

query I
SELECT regexp_count('ABCABCABCABC', 'Abc', 1, 'i');
----
4

statement error
External error: query failed: DataFusion error: Arrow error: Compute error: regexp_count() requires start to be 1 based
SELECT regexp_count('123123123123', '123', 0);

statement error
External error: query failed: DataFusion error: Arrow error: Compute error: regexp_count() requires start to be 1 based
SELECT regexp_count('123123123123', '123', -3);

statement error
External error: statement failed: DataFusion error: Arrow error: Compute error: regexp_count() does not support global flag
SELECT regexp_count('123123123123', '123', 1, 'g');

query I
SELECT regexp_count(str, '\w') from regexp_test_data;
----
0
3
3
3
3
3
4
4
10
6
4
7

query I
SELECT regexp_count(str, '\w{2}', start) from regexp_test_data;
----
0
1
1
1
1
0
2
1
4
1
2
3

query I
SELECT regexp_count(str, 'ab', 1, 'i') from regexp_test_data;
----
0
1
1
1
1
1
0
0
0
0
0
0


query I
SELECT regexp_count(str, pattern) from regexp_test_data;
----
0
1
1
0
0
0
0
1
1
1
1
1

query I
SELECT regexp_count(str, pattern, start) from regexp_test_data;
----
0
1
1
0
0
0
0
0
1
1
1
1

query I
SELECT regexp_count(str, pattern, start, flags) from regexp_test_data;
----
0
1
1
1
0
0
0
0
1
1
1
1

# test type coercion
query I
SELECT regexp_count(arrow_cast(str, 'Utf8'), arrow_cast(pattern, 'LargeUtf8'),  arrow_cast(start, 'Int32'), flags) from regexp_test_data;
----
0
1
1
1
0
0
0
0
1
1
1
1

# test string views

statement ok
CREATE TABLE t_stringview AS
SELECT arrow_cast(str, 'Utf8View') as str, arrow_cast(pattern, 'Utf8View') as pattern, arrow_cast(start, 'Int64') as start, arrow_cast(flags, 'Utf8View') as flags FROM regexp_test_data;

query I
SELECT regexp_count(str, '\w') from t_stringview;
----
0
3
3
3
3
3
4
4
10
6
4
7

query I
SELECT regexp_count(str, '\w{2}', start) from t_stringview;
----
0
1
1
1
1
0
2
1
4
1
2
3

query I
SELECT regexp_count(str, 'ab', 1, 'i') from t_stringview;
----
0
1
1
1
1
1
0
0
0
0
0
0


query I
SELECT regexp_count(str, pattern) from t_stringview;
----
0
1
1
0
0
0
0
1
1
1
1
1

query I
SELECT regexp_count(str, pattern, start) from t_stringview;
----
0
1
1
0
0
0
0
0
1
1
1
1

query I
SELECT regexp_count(str, pattern, start, flags) from t_stringview;
----
0
1
1
1
0
0
0
0
1
1
1
1

# test type coercion
query I
SELECT regexp_count(arrow_cast(str, 'Utf8'), arrow_cast(pattern, 'LargeUtf8'),  arrow_cast(start, 'Int32'), flags) from t_stringview;
----
0
1
1
1
0
0
0
0
1
1
1
1

# NULL tests

query I
SELECT regexp_count(NULL, NULL);
----
0

query I
SELECT regexp_count(NULL, 'a');
----
0

query I
SELECT regexp_count('a', NULL);
----
0

query I
SELECT regexp_count(NULL, NULL, NULL, NULL);
----
0

statement ok
CREATE TABLE empty_table (str varchar, pattern varchar, start int, flags varchar);

query I
SELECT regexp_count(str, pattern, start, flags) from empty_table;
----

statement ok
INSERT INTO empty_table VALUES ('a', NULL, 1, 'i'), (NULL, 'a', 1, 'i'), (NULL, NULL, 1, 'i'), (NULL, NULL, NULL, 'i');

query I
SELECT regexp_count(str, pattern, start, flags) from empty_table;
----
0
0
0
0

statement ok
drop table t_stringview;

statement ok
drop table empty_table;