# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

statement ok
set datafusion.catalog.information_schema = true;

statement ok
set datafusion.catalog.default_catalog = my_catalog;

statement ok
set datafusion.catalog.default_schema = my_schema;

###########
# Information schema columns
###########

statement ok
CREATE TABLE t1 (i int) as values(1);

# table t2 is created using rust code because it is not possible to set nullable columns with `arrow_cast` syntax

query TTTTITTTIIIIIIT rowsort
SELECT * from information_schema.columns;
----
my_catalog my_schema t1 i 0 NULL YES Int32 NULL NULL 32 2 NULL NULL NULL
my_catalog my_schema table_with_many_types binary_col 4 NULL NO Binary NULL ********** NULL NULL NULL NULL NULL
my_catalog my_schema table_with_many_types float64_col 1 NULL YES Float64 NULL NULL 24 2 NULL NULL NULL
my_catalog my_schema table_with_many_types int32_col 0 NULL NO Int32 NULL NULL 32 2 NULL NULL NULL
my_catalog my_schema table_with_many_types large_binary_col 5 NULL NO LargeBinary NULL 9223372036854775807 NULL NULL NULL NULL NULL
my_catalog my_schema table_with_many_types large_utf8_col 3 NULL NO LargeUtf8 NULL 9223372036854775807 NULL NULL NULL NULL NULL
my_catalog my_schema table_with_many_types timestamp_nanos 6 NULL NO Timestamp(Nanosecond, None) NULL NULL NULL NULL NULL NULL NULL
my_catalog my_schema table_with_many_types utf8_col 2 NULL YES Utf8 NULL ********** NULL NULL NULL NULL NULL

# Cleanup
statement ok
drop table t1

statement ok
drop table table_with_many_types
