# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

#############
## Array Expressions Tests
#############

### Tables

statement ok
CREATE TABLE values(
  a INT,
  b INT,
  c INT,
  d FLOAT,
  e VARCHAR,
  f VARCHAR
) AS VALUES
  (1,    1,    2,    1.1,  'Lorem',       'A'),
  (2,    3,    4,    2.2,  'ipsum',       ''),
  (3,    5,    6,    3.3,  'dolor',       'BB'),
  (4,    7,    8,    4.4,  'sit',          NULL),
  (NULL, 9,    10,   5.5,  'amet',        'CCC'),
  (5,    NULL, 12,   6.6,  ',',           'DD'),
  (6,    11,   NULL, 7.7,  'consectetur', 'E'),
  (7,    13,   14,   NULL, 'adipiscing',  'F'),
  (8,    15,   16,   8.8,   NULL,          '')
;

statement ok
CREATE TABLE values_without_nulls
AS VALUES
  (1,    1,    2,    1.1,  'Lorem',       'A'),
  (2,    3,    4,    2.2,  'ipsum',       ''),
  (3,    5,    6,    3.3,  'dolor',       'BB'),
  (4,    7,    8,    4.4,  'sit',          NULL),
  (5,    9,    10,   5.5,  'amet',        'CCC'),
  (6,    11,   12,   6.6,  ',',           'DD'),
  (7,    13,   14,   7.7,  'consectetur', 'E'),
  (8,    15,   16,   8.8,  'adipiscing',  'F'),
  (9,    17,   18,   9.9,  'elit',        '')
;

statement ok
CREATE TABLE arrays
AS VALUES
  (make_array(make_array(NULL, 2),make_array(3, NULL)), make_array(1.1, 2.2, 3.3), make_array('L', 'o', 'r', 'e', 'm')),
  (make_array(make_array(3, 4),make_array(5, 6)), make_array(NULL, 5.5, 6.6), make_array('i', 'p', NULL, 'u', 'm')),
  (make_array(make_array(5, 6),make_array(7, 8)), make_array(7.7, 8.8, 9.9), make_array('d', NULL, 'l', 'o', 'r')),
  (make_array(make_array(7, NULL),make_array(9, 10)), make_array(10.1, NULL, 12.2), make_array('s', 'i', 't')),
  (NULL, make_array(13.3, 14.4, 15.5), make_array('a', 'm', 'e', 't')),
  (make_array(make_array(11, 12),make_array(13, 14)), NULL, make_array(',')),
  (make_array(make_array(15, 16),make_array(NULL, 18)), make_array(16.6, 17.7, 18.8), NULL)
;

statement ok
CREATE TABLE large_arrays
AS
  SELECT
    arrow_cast(column1, 'LargeList(List(Int64))') AS column1,
    arrow_cast(column2, 'LargeList(Float64)') AS column2,
    arrow_cast(column3, 'LargeList(Utf8)') AS column3
  FROM arrays
;

statement ok
CREATE TABLE fixed_size_arrays
AS VALUES
  (arrow_cast(make_array(make_array(NULL, 2),make_array(3, NULL)), 'FixedSizeList(2, List(Int64))'), arrow_cast(make_array(1.1, 2.2, 3.3), 'FixedSizeList(3, Float64)'), arrow_cast(make_array('L', 'o', 'r', 'e', 'm'), 'FixedSizeList(5, Utf8)')),
  (arrow_cast(make_array(make_array(3, 4),make_array(5, 6)), 'FixedSizeList(2, List(Int64))'), arrow_cast(make_array(NULL, 5.5, 6.6), 'FixedSizeList(3, Float64)'), arrow_cast(make_array('i', 'p', NULL, 'u', 'm'), 'FixedSizeList(5, Utf8)')),
  (arrow_cast(make_array(make_array(5, 6),make_array(7, 8)), 'FixedSizeList(2, List(Int64))'), arrow_cast(make_array(7.7, 8.8, 9.9), 'FixedSizeList(3, Float64)'), arrow_cast(make_array('d', NULL, 'l', 'o', 'r'), 'FixedSizeList(5, Utf8)')),
  (arrow_cast(make_array(make_array(7, NULL),make_array(9, 10)), 'FixedSizeList(2, List(Int64))'), arrow_cast(make_array(10.1, NULL, 12.2), 'FixedSizeList(3, Float64)'), arrow_cast(make_array('s', 'i', 't', 'a', 'b'), 'FixedSizeList(5, Utf8)')),
  (NULL, arrow_cast(make_array(13.3, 14.4, 15.5), 'FixedSizeList(3, Float64)'), arrow_cast(make_array('a', 'm', 'e', 't', 'x'), 'FixedSizeList(5, Utf8)')),
  (arrow_cast(make_array(make_array(11, 12),make_array(13, 14)), 'FixedSizeList(2, List(Int64))'), NULL, arrow_cast(make_array(',','a','b','c','d'), 'FixedSizeList(5, Utf8)')),
  (arrow_cast(make_array(make_array(15, 16),make_array(NULL, 18)), 'FixedSizeList(2, List(Int64))'), arrow_cast(make_array(16.6, 17.7, 18.8), 'FixedSizeList(3, Float64)'), NULL)
;

statement ok
CREATE TABLE slices
AS VALUES
  (make_array(NULL, 2, 3, 4, 5, 6, 7, 8, 9, 10), 1, 1),
  (make_array(11, 12, 13, 14, 15, 16, 17, 18, NULL, 20), 2, -4),
  (make_array(21, 22, 23, NULL, 25, 26, 27, 28, 29, 30), 0, 0),
  (make_array(31, 32, 33, 34, 35, NULL, 37, 38, 39, 40), -4, -7),
  (NULL, 4, 5),
  (make_array(41, 42, 43, 44, 45, 46, 47, 48, 49, 50), NULL, 6),
  (make_array(51, 52, NULL, 54, 55, 56, 57, 58, 59, 60), 5, NULL)
;

statement ok
CREATE TABLE fixed_slices
AS VALUES
  (arrow_cast(make_array(NULL, 2, 3, 4, 5, 6, 7, 8, 9, 10), 'FixedSizeList(10, Int64)'), 1, 1),
  (arrow_cast(make_array(11, 12, 13, 14, 15, 16, 17, 18, NULL, 20), 'FixedSizeList(10, Int64)'), 2, -4),
  (arrow_cast(make_array(21, 22, 23, NULL, 25, 26, 27, 28, 29, 30), 'FixedSizeList(10, Int64)'), 0, 0),
  (arrow_cast(make_array(31, 32, 33, 34, 35, NULL, 37, 38, 39, 40), 'FixedSizeList(10, Int64)'), -4, -7),
  (arrow_cast(make_array(41, 42, 43, 44, 45, 46, 47, 48, 49, 50), 'FixedSizeList(10, Int64)'), NULL, 6),
  (arrow_cast(make_array(51, 52, NULL, 54, 55, 56, 57, 58, 59, 60),'FixedSizeList(10, Int64)'), 5, NULL)
;

statement ok
CREATE TABLE arrayspop
AS VALUES
  (make_array(1, 2, NULL)),
  (make_array(3, 4, 5, NULL)),
  (make_array(6, 7, 8, NULL, 9)),
  (make_array(NULL, NULL, 100)),
  (NULL),
  (make_array(NULL, 10, 11, 12))
;

statement ok
CREATE TABLE large_arrayspop
AS SELECT
  arrow_cast(column1, 'LargeList(Int64)') AS column1
FROM arrayspop
;

statement ok
CREATE TABLE nested_arrays
AS VALUES
  (make_array(make_array(1, 2, 3), make_array(2, 9, 1), make_array(7, 8, 9), make_array(1, 2, 3), make_array(1, 7, 4), make_array(4, 5, 6)), make_array(7, 8, 9), 2, make_array([[1, 2, 3], [4, 5, 6]], [[7, 8, 9], [10, 11, 12]]), make_array(11, 12, 13)),
  (make_array(make_array(4, 5, 6), make_array(10, 11, 12), make_array(4, 9, 8), make_array(7, 8, 9), make_array(10, 11, 12), make_array(1, 8, 7)), make_array(10, 11, 12), 3, make_array([[11, 12, 13], [14, 15, 16]], [[17, 18, 19], [20, 21, 22]]), make_array(121, 131, 141))
;

statement ok
CREATE TABLE large_nested_arrays
AS
  SELECT
    arrow_cast(column1, 'LargeList(LargeList(Int64))') AS column1,
    arrow_cast(column2, 'LargeList(Int64)') AS column2,
    column3,
    arrow_cast(column4, 'LargeList(LargeList(List(Int64)))') AS column4,
    arrow_cast(column5, 'LargeList(Int64)') AS column5
  FROM nested_arrays
;

statement ok
CREATE TABLE fixed_size_nested_arrays
AS VALUES
  (arrow_cast(make_array(make_array(1, 2, 3), make_array(2, 9, 1), make_array(7, 8, 9), make_array(1, 2, 3), make_array(1, 7, 4), make_array(4, 5, 6)), 'FixedSizeList(6, List(Int64))'), arrow_cast(make_array(7, 8, 9), 'FixedSizeList(3, Int64)'), 2, arrow_cast(make_array([[1, 2, 3], [4, 5, 6]], [[7, 8, 9], [10, 11, 12]]), 'FixedSizeList(2, List(List(Int64)))'), arrow_cast(make_array(11, 12, 13), 'FixedSizeList(3, Int64)')),
  (arrow_cast(make_array(make_array(4, 5, 6), make_array(10, 11, 12), make_array(4, 9, 8), make_array(7, 8, 9), make_array(10, 11, 12), make_array(1, 8, 7)), 'FixedSizeList(6, List(Int64))'), arrow_cast(make_array(10, 11, 12), 'FixedSizeList(3, Int64)'), 3, arrow_cast(make_array([[11, 12, 13], [14, 15, 16]], [[17, 18, 19], [20, 21, 22]]), 'FixedSizeList(2, List(List(Int64)))'), arrow_cast(make_array(121, 131, 141), 'FixedSizeList(3, Int64)'))
;

statement ok
CREATE TABLE arrays_values
AS VALUES
  (make_array(NULL, 2, 3, 4, 5, 6, 7, 8, 9, 10), 1, 1, ','),
  (make_array(11, 12, 13, 14, 15, 16, 17, 18, NULL, 20), 12, 2, '.'),
  (make_array(21, 22, 23, NULL, 25, 26, 27, 28, 29, 30), 23, 3, '-'),
  (make_array(31, 32, 33, 34, 35, NULL, 37, 38, 39, 40), 34, 4, 'ok'),
  (NULL, 44, 5, '@'),
  (make_array(41, 42, 43, 44, 45, 46, 47, 48, 49, 50), NULL, 6, '$'),
  (make_array(51, 52, NULL, 54, 55, 56, 57, 58, 59, 60), 55, NULL, '^'),
  (make_array(61, 62, 63, 64, 65, 66, 67, 68, 69, 70), 66, 7, NULL)
;

statement ok
CREATE TABLE large_arrays_values
AS SELECT
  arrow_cast(column1, 'LargeList(Int64)') AS column1,
  column2,
  column3,
  column4
FROM arrays_values
;

statement ok
CREATE TABLE fixed_arrays_values
AS SELECT
  arrow_cast(column1, 'FixedSizeList(10, Int64)') AS column1,
  column2,
  column3,
  column4
FROM arrays_values
;

statement ok
CREATE TABLE arrays_values_v2
AS VALUES
  (make_array(NULL, 2, 3), make_array(4, 5, NULL), 12, make_array([30, 40, 50])),
  (NULL, make_array(7, NULL, 8), 13, make_array(make_array(NULL,NULL,60))),
  (make_array(9, NULL, 10), NULL, 14, make_array(make_array(70,NULL,NULL))),
  (make_array(NULL, 1), make_array(NULL, 21), NULL, NULL),
  (make_array(11, 12), NULL, NULL, NULL),
  (NULL, NULL, NULL, NULL)
;

statement ok
CREATE TABLE large_arrays_values_v2
AS SELECT
  arrow_cast(column1, 'LargeList(Int64)') AS column1,
  arrow_cast(column2, 'LargeList(Int64)') AS column2,
  column3,
  arrow_cast(column4, 'LargeList(LargeList(Int64))') AS column4
FROM arrays_values_v2
;

statement ok
CREATE TABLE flatten_table
AS VALUES
  (make_array([1], [2], [3]), make_array([[1, 2, 3]], [[4, 5]], [[6]]), make_array([[[1]]], [[[2, 3]]]), make_array([1.0], [2.1, 2.2], [3.2, 3.3, 3.4])),
  (make_array([1, 2], [3, 4], [5, 6]), make_array([[8]]), make_array([[[1,2]]], [[[3]]]), make_array([1.0, 2.0], [3.0, 4.0], [5.0, 6.0]))
;

statement ok
CREATE TABLE large_flatten_table
AS
  SELECT
    arrow_cast(column1, 'LargeList(LargeList(Int64))') AS column1,
    arrow_cast(column2, 'LargeList(LargeList(LargeList(Int64)))') AS column2,
    arrow_cast(column3, 'LargeList(LargeList(LargeList(LargeList(Int64))))') AS column3,
    arrow_cast(column4, 'LargeList(LargeList(Float64))') AS column4
  FROM flatten_table
;

statement ok
CREATE TABLE fixed_size_flatten_table
AS VALUES
  (arrow_cast(make_array([1], [2], [3]), 'FixedSizeList(3, List(Int64))'),
   arrow_cast(make_array([[1, 2, 3]], [[4, 5]], [[6]]), 'FixedSizeList(3, List(List(Int64)))'),
   arrow_cast(make_array([[[1]]], [[[2, 3]]]), 'FixedSizeList(2, List(List(List(Int64))))'),
   arrow_cast(make_array([1.0], [2.1, 2.2], [3.2, 3.3, 3.4]), 'FixedSizeList(3, List(Float64))')
   ),
  (
    arrow_cast(make_array([1, 2], [3, 4], [5, 6]), 'FixedSizeList(3, List(Int64))'),
    arrow_cast(make_array([[8]], [[9, 10]], [[11, 12, 13]]), 'FixedSizeList(3, List(List(Int64)))'),
    arrow_cast(make_array([[[1,2]]], [[[3]]]), 'FixedSizeList(2, List(List(List(Int64))))'),
    arrow_cast(make_array([1.0, 2.0], [3.0, 4.0], [5.0, 6.0]), 'FixedSizeList(3, List(Float64))')
    )
;

statement ok
CREATE TABLE array_has_table_1D
AS VALUES
  (make_array(1, 2), 1, make_array(1,2,3), make_array(1,3), make_array(1,3,5), make_array(2,4,6,8,1,3,5)),
  (make_array(3, 4, 5), 2, make_array(1,2,3,4), make_array(2,5), make_array(2,4,6), make_array(1,3,5))
;

statement ok
CREATE TABLE fixed_size_array_has_table_1D
AS VALUES
  (arrow_cast(make_array(1, 2, 3), 'FixedSizeList(3, Int64)'), 1, arrow_cast(make_array(1, 2, 3, 4), 'FixedSizeList(4, Int64)'), arrow_cast(make_array(1,3), 'FixedSizeList(2, Int64)'), arrow_cast(make_array(1,3,5), 'FixedSizeList(3, Int64)'), arrow_cast(make_array(2, 4, 6, 8, 1, 3, 5), 'FixedSizeList(7, Int64)')),
  (arrow_cast(make_array(3, 4, 5), 'FixedSizeList(3, Int64)'), 2, arrow_cast(make_array(1, 2, 3, 4), 'FixedSizeList(4, Int64)'), arrow_cast(make_array(2,5), 'FixedSizeList(2, Int64)'), arrow_cast(make_array(2,4,6), 'FixedSizeList(3, Int64)'), arrow_cast(make_array(1, 3, 5, 7, 9, 11, 13), 'FixedSizeList(7, Int64)'))
;

statement ok
CREATE TABLE array_has_table_1D_Float
AS VALUES
  (make_array(1.0, 2.0), 1.0, make_array(1.0,2.0,3.0), make_array(1.0,3.0), make_array(1.11), make_array(2.22, 3.33)),
  (make_array(3.0, 4.0, 5.0), 2.0, make_array(1.0,2.0,3.0,4.0), make_array(2.0,5.0), make_array(2.22, 1.11), make_array(1.11, 3.33))
;

statement ok
CREATE TABLE fixed_size_array_has_table_1D_Float
AS VALUES
  (arrow_cast(make_array(1.0, 2.0, 3.0), 'FixedSizeList(3, Float64)'), 1.0, arrow_cast(make_array(1.0, 2.0, 3.0, 4.0), 'FixedSizeList(4, Float64)'), arrow_cast(make_array(1.0,3.0), 'FixedSizeList(2, Float64)'), arrow_cast(make_array(1.11, 2.22), 'FixedSizeList(2, Float64)'), arrow_cast(make_array(2.22, 3.33), 'FixedSizeList(2, Float64)')),
  (arrow_cast(make_array(3.0, 4.0, 5.0), 'FixedSizeList(3, Float64)'), 2.0, arrow_cast(make_array(1.0, 2.0, 3.0, 4.0), 'FixedSizeList(4, Float64)'), arrow_cast(make_array(2.0,5.0), 'FixedSizeList(2, Float64)'), arrow_cast(make_array(2.22, 1.11), 'FixedSizeList(2, Float64)'), arrow_cast(make_array(1.11, 3.33), 'FixedSizeList(2, Float64)'))
;

statement ok
CREATE TABLE array_has_table_1D_Boolean
AS VALUES
  (make_array(true, true, true), false, make_array(true, true, false, true, false), make_array(true, false, true), make_array(false), make_array(true, false)),
  (make_array(false, false, false), false, make_array(true, false, true), make_array(true, true), make_array(true, true), make_array(false,false,true))
;

statement ok
CREATE TABLE fixed_size_array_has_table_1D_Boolean
AS VALUES
  (arrow_cast(make_array(true, true, true), 'FixedSizeList(3, Boolean)'), false, arrow_cast(make_array(true, true, false, true, false), 'FixedSizeList(5, Boolean)'), arrow_cast(make_array(true, false, true), 'FixedSizeList(3, Boolean)'), arrow_cast(make_array(false, true), 'FixedSizeList(2, Boolean)'), arrow_cast(make_array(true, false, true), 'FixedSizeList(3, Boolean)')),
  (arrow_cast(make_array(false, false, false), 'FixedSizeList(3, Boolean)'), false, arrow_cast(make_array(true, false, true, true, false), 'FixedSizeList(5, Boolean)'), arrow_cast(make_array(true, true, false), 'FixedSizeList(3, Boolean)'), arrow_cast(make_array(true, true), 'FixedSizeList(2, Boolean)'), arrow_cast(make_array(false,false,true), 'FixedSizeList(3, Boolean)'))
;

statement ok
CREATE TABLE array_has_table_1D_UTF8
AS VALUES
  (make_array('a', 'bc', 'def'), 'bc', make_array('datafusion', 'rust', 'arrow'), make_array('rust', 'arrow'), make_array('rust', 'arrow', 'python'), make_array('data')),
  (make_array('a', 'bc', 'def'), 'defg', make_array('datafusion', 'rust', 'arrow'), make_array('datafusion', 'rust', 'arrow', 'python'), make_array('rust', 'arrow'), make_array('datafusion', 'rust', 'arrow'))
;

statement ok
CREATE TABLE fixed_size_array_has_table_1D_UTF8
AS VALUES
  (arrow_cast(make_array('a', 'bc', 'def'), 'FixedSizeList(3, Utf8)'), 'bc', arrow_cast(make_array('datafusion', 'rust', 'arrow'), 'FixedSizeList(3, Utf8)'), arrow_cast(make_array('rust', 'arrow', 'datafusion', 'rust'), 'FixedSizeList(4, Utf8)'), arrow_cast(make_array('rust', 'arrow', 'python'), 'FixedSizeList(3, Utf8)'), arrow_cast(make_array('data', 'fusion', 'rust'), 'FixedSizeList(3, Utf8)')),
  (arrow_cast(make_array('a', 'bc', 'def'), 'FixedSizeList(3, Utf8)'), 'defg', arrow_cast(make_array('datafusion', 'rust', 'arrow'), 'FixedSizeList(3, Utf8)'), arrow_cast(make_array('datafusion', 'rust', 'arrow', 'python'), 'FixedSizeList(4, Utf8)'), arrow_cast(make_array('rust', 'arrow', 'python'), 'FixedSizeList(3, Utf8)'), arrow_cast(make_array('datafusion', 'rust', 'arrow'), 'FixedSizeList(3, Utf8)'))
;

statement ok
CREATE TABLE array_has_table_2D
AS VALUES
  (make_array([1,2]), make_array(1,3), make_array([1,2,3], [4,5], [6,7]), make_array([4,5], [6,7])),
  (make_array([3,4], [5]), make_array(5), make_array([1,2,3,4], [5,6,7], [8,9,10]), make_array([1,2,3], [5,6,7], [8,9,10]))
;

statement ok
CREATE TABLE fixed_size_array_has_table_2D
AS VALUES
  (arrow_cast(make_array([1,2], [3,4]), 'FixedSizeList(2, List(Int64))'), arrow_cast(make_array(1,3), 'FixedSizeList(2, Int64)'), arrow_cast(make_array([1,2,3], [4,5], [6,7]), 'FixedSizeList(3, List(Int64))'), arrow_cast(make_array([4,5], [6,7], [1,2]), 'FixedSizeList(3, List(Int64))')),
  (arrow_cast(make_array([3,4], [5]), 'FixedSizeList(2, List(Int64))'), arrow_cast(make_array(5, 3), 'FixedSizeList(2, Int64)'), arrow_cast(make_array([1,2,3,4], [5,6,7], [8,9,10]), 'FixedSizeList(3, List(Int64))'), arrow_cast(make_array([1,2,3], [5,6,7], [8,9,10]), 'FixedSizeList(3, List(Int64))'))
;

statement ok
CREATE TABLE array_has_table_2D_float
AS VALUES
  (make_array([1.0, 2.0, 3.0], [1.1, 2.2], [3.3]), make_array([1.1, 2.2], [3.3])),
  (make_array([1.0, 2.0, 3.0], [1.1, 2.2], [3.3]), make_array([1.0], [1.1, 2.2], [3.3]))
;

statement ok
CREATE TABLE fixed_size_array_has_table_2D_Float
AS VALUES
  (arrow_cast(make_array([1.0, 2.0, 3.0], [1.1, 2.2], [3.3]), 'FixedSizeList(3, List(Float64))'), arrow_cast(make_array([1.1, 2.2], [3.3], [4.4]), 'FixedSizeList(3, List(Float64))')),
  (arrow_cast(make_array([1.0, 2.0, 3.0], [1.1, 2.2], [3.3]), 'FixedSizeList(3, List(Float64))'), arrow_cast(make_array([1.0], [1.1, 2.2], [3.3]), 'FixedSizeList(3, List(Float64))'))
;

statement ok
CREATE TABLE array_has_table_3D
AS VALUES
  (make_array([[1,2]]), make_array([1])),
  (make_array([[1,2]]), make_array([1,2])),
  (make_array([[1,2]]), make_array([1,2,3])),
  (make_array([[1], [2]]), make_array([2])),
  (make_array([[1], [2]]), make_array([1], [2])),
  (make_array([[1], [2]], [[2], [3]]), make_array([1], [2], [3])),
  (make_array([[1], [2]], [[2], [3]]), make_array([1], [2]))
;

statement ok
CREATE TABLE fixed_size_array_has_table_3D
AS VALUES
  (arrow_cast(make_array([[1,2]], [[3, 4]]), 'FixedSizeList(2, List(List(Int64)))'), arrow_cast(make_array([1], [2]), 'FixedSizeList(2, List(Int64))')),
  (arrow_cast(make_array([[1,2]], [[4, 4]]), 'FixedSizeList(2, List(List(Int64)))'), arrow_cast(make_array([1,2], [3, 4]), 'FixedSizeList(2, List(Int64))')),
  (arrow_cast(make_array([[1,2]], [[4, 4]]), 'FixedSizeList(2, List(List(Int64)))'), arrow_cast(make_array([1,2,3], [1]), 'FixedSizeList(2, List(Int64))')),
  (arrow_cast(make_array([[1], [2]], [[]]), 'FixedSizeList(2, List(List(Int64)))'), arrow_cast(make_array([2], [3]), 'FixedSizeList(2, List(Int64))')),
  (arrow_cast(make_array([[1], [2]], [[]]), 'FixedSizeList(2, List(List(Int64)))'), arrow_cast(make_array([1], [2]), 'FixedSizeList(2, List(Int64))')),
  (arrow_cast(make_array([[1], [2]], [[2], [3]]), 'FixedSizeList(2, List(List(Int64)))'), arrow_cast(make_array([1], [2]), 'FixedSizeList(2, List(Int64))')),
  (arrow_cast(make_array([[1], [2]], [[2], [3]]), 'FixedSizeList(2, List(List(Int64)))'), arrow_cast(make_array([1], [2]), 'FixedSizeList(2, List(Int64))'))
;

statement ok
CREATE TABLE array_has_table_null
AS VALUES
  (make_array(1, 2), 1),
  (make_array(1, NULL), 1),
  (make_array(3, 4, 5), 2),
  (make_array(3, NULL, 5), 2),
  (make_array(NULL, NULL, NULL), 2)
;

statement ok
CREATE TABLE array_distinct_table_1D
AS VALUES
  (make_array(1, 1, 2, 2, 3)),
  (make_array(1, 2, 3, 4, 5)),
  (make_array(3, 5, 3, 3, 3))
;

statement ok
CREATE TABLE array_distinct_table_1D_UTF8
AS VALUES
  (make_array('a', 'a', 'bc', 'bc', 'def')),
  (make_array('a', 'bc', 'def', 'defg', 'defg')),
  (make_array('defg', 'defg', 'defg', 'defg', 'defg'))
;

statement ok
CREATE TABLE array_distinct_table_2D
AS VALUES
  (make_array([1,2], [1,2], [3,4], [3,4], [5,6])),
  (make_array([1,2], [3,4], [5,6], [7,8], [9,10])),
  (make_array([5,6], [5,6], NULL))
;

statement ok
CREATE TABLE array_distinct_table_1D_large
AS SELECT
  arrow_cast(column1, 'LargeList(Int64)') AS column1
FROM array_distinct_table_1D
;

statement ok
CREATE TABLE array_distinct_table_1D_fixed
AS SELECT
  arrow_cast(column1, 'FixedSizeList(5, Int64)') AS column1
FROM array_distinct_table_1D
;

statement ok
CREATE TABLE array_distinct_table_1D_UTF8_fixed
AS SELECT
  arrow_cast(column1, 'FixedSizeList(5, Utf8)') AS column1
FROM array_distinct_table_1D_UTF8
;

statement ok
CREATE TABLE array_distinct_table_2D_fixed
AS VALUES
  (arrow_cast(make_array([1,2], [1,2], [3,4], [3,4], [5,6]), 'FixedSizeList(5, List(Int64))')),
  (arrow_cast(make_array([1,2], [3,4], [5,6], [7,8], [9,10]), 'FixedSizeList(5, List(Int64))')),
  (arrow_cast(make_array([5,6], [5,6], NULL, NULL, NULL), 'FixedSizeList(5, List(Int64))'))
;

statement ok
CREATE TABLE array_intersect_table_1D
AS VALUES
  (make_array(1, 2), make_array(1), make_array(1,2,3), make_array(1,3), make_array(1,3,5), make_array(2,4,6,8,1,3)),
  (make_array(11, 22), make_array(11), make_array(11,22,33), make_array(11,33), make_array(11,33,55), make_array(22,44,66,88,11,33))
;

statement ok
CREATE TABLE large_array_intersect_table_1D
AS
  SELECT
    arrow_cast(column1, 'LargeList(Int64)') as column1,
    arrow_cast(column2, 'LargeList(Int64)') as column2,
    arrow_cast(column3, 'LargeList(Int64)') as column3,
    arrow_cast(column4, 'LargeList(Int64)') as column4,
    arrow_cast(column5, 'LargeList(Int64)') as column5,
    arrow_cast(column6, 'LargeList(Int64)') as column6
FROM array_intersect_table_1D
;

statement ok
CREATE TABLE array_intersect_table_1D_Float
AS VALUES
  (make_array(1.0, 2.0), make_array(1.0), make_array(1.0,2.0,3.0), make_array(1.0,3.0), make_array(1.11), make_array(2.22, 3.33)),
  (make_array(3.0, 4.0, 5.0), make_array(2.0), make_array(1.0,2.0,3.0,4.0), make_array(2.0,5.0), make_array(2.22, 1.11), make_array(1.11, 3.33))
;

statement ok
CREATE TABLE large_array_intersect_table_1D_Float
AS
  SELECT
    arrow_cast(column1, 'LargeList(Float64)') as column1,
    arrow_cast(column2, 'LargeList(Float64)') as column2,
    arrow_cast(column3, 'LargeList(Float64)') as column3,
    arrow_cast(column4, 'LargeList(Float64)') as column4,
    arrow_cast(column5, 'LargeList(Float64)') as column5,
    arrow_cast(column6, 'LargeList(Float64)') as column6
FROM array_intersect_table_1D_Float
;

statement ok
CREATE TABLE array_intersect_table_1D_Boolean
AS VALUES
  (make_array(true, true, true), make_array(false), make_array(true, true, false, true, false), make_array(true, false, true), make_array(false), make_array(true, false)),
  (make_array(false, false, false), make_array(false), make_array(true, false, true), make_array(true, true), make_array(true, true), make_array(false,false,true))
;

statement ok
CREATE TABLE large_array_intersect_table_1D_Boolean
AS
  SELECT
    arrow_cast(column1, 'LargeList(Boolean)') as column1,
    arrow_cast(column2, 'LargeList(Boolean)') as column2,
    arrow_cast(column3, 'LargeList(Boolean)') as column3,
    arrow_cast(column4, 'LargeList(Boolean)') as column4,
    arrow_cast(column5, 'LargeList(Boolean)') as column5,
    arrow_cast(column6, 'LargeList(Boolean)') as column6
FROM array_intersect_table_1D_Boolean
;

statement ok
CREATE TABLE array_intersect_table_1D_UTF8
AS VALUES
  (make_array('a', 'bc', 'def'), make_array('bc'), make_array('datafusion', 'rust', 'arrow'), make_array('rust', 'arrow'), make_array('rust', 'arrow', 'python'), make_array('data')),
  (make_array('a', 'bc', 'def'), make_array('defg'), make_array('datafusion', 'rust', 'arrow'), make_array('datafusion', 'rust', 'arrow', 'python'), make_array('rust', 'arrow'), make_array('datafusion', 'rust', 'arrow'))
;

statement ok
CREATE TABLE large_array_intersect_table_1D_UTF8
AS
  SELECT
    arrow_cast(column1, 'LargeList(Utf8)') as column1,
    arrow_cast(column2, 'LargeList(Utf8)') as column2,
    arrow_cast(column3, 'LargeList(Utf8)') as column3,
    arrow_cast(column4, 'LargeList(Utf8)') as column4,
    arrow_cast(column5, 'LargeList(Utf8)') as column5,
    arrow_cast(column6, 'LargeList(Utf8)') as column6
FROM array_intersect_table_1D_UTF8
;

statement ok
CREATE TABLE array_intersect_table_2D
AS VALUES
  (make_array([1,2]), make_array([1,3]), make_array([1,2,3], [4,5], [6,7]), make_array([4,5], [6,7])),
  (make_array([3,4], [5]), make_array([3,4]), make_array([1,2,3,4], [5,6,7], [8,9,10]), make_array([1,2,3], [5,6,7], [8,9,10]))
;

statement ok
CREATE TABLE large_array_intersect_table_2D
AS
  SELECT
    arrow_cast(column1, 'LargeList(List(Int64))') as column1,
    arrow_cast(column2, 'LargeList(List(Int64))') as column2,
    arrow_cast(column3, 'LargeList(List(Int64))') as column3,
    arrow_cast(column4, 'LargeList(List(Int64))') as column4
FROM array_intersect_table_2D
;

statement ok
CREATE TABLE array_intersect_table_2D_float
AS VALUES
  (make_array([1.0, 2.0, 3.0], [1.1, 2.2], [3.3]), make_array([1.1, 2.2], [3.3])),
  (make_array([1.0, 2.0, 3.0], [1.1, 2.2], [3.3]), make_array([1.0], [1.1, 2.2], [3.3]))
;

statement ok
CREATE TABLE large_array_intersect_table_2D_Float
AS
  SELECT
    arrow_cast(column1, 'LargeList(List(Float64))') as column1,
    arrow_cast(column2, 'LargeList(List(Float64))') as column2
FROM array_intersect_table_2D_Float
;

statement ok
CREATE TABLE array_intersect_table_3D
AS VALUES
  (make_array([[1,2]]), make_array([[1]])),
  (make_array([[1,2]]), make_array([[1,2]]))
;

statement ok
CREATE TABLE large_array_intersect_table_3D
AS
  SELECT
    arrow_cast(column1, 'LargeList(List(List(Int64)))') as column1,
    arrow_cast(column2, 'LargeList(List(List(Int64)))') as column2
FROM array_intersect_table_3D
;

statement ok
CREATE TABLE arrays_values_without_nulls
AS VALUES
  (make_array(1, 2, 3, 4, 5, 6, 7, 8, 9, 10), 1, 1, ',', [2,3]),
  (make_array(11, 12, 13, 14, 15, 16, 17, 18, 19, 20), 12, 2, '.', [4,5]),
  (make_array(21, 22, 23, 24, 25, 26, 27, 28, 29, 30), 23, 3, '-', [6,7]),
  (make_array(31, 32, 33, 34, 35, 26, 37, 38, 39, 40), 34, 4, 'ok', [8,9])
;

statement ok
CREATE TABLE large_arrays_values_without_nulls
AS SELECT
  arrow_cast(column1, 'LargeList(Int64)') AS column1,
  column2,
  column3,
  column4,
  arrow_cast(column5, 'LargeList(Int64)') AS column5
FROM arrays_values_without_nulls
;

statement ok
CREATE TABLE fixed_size_arrays_values_without_nulls
AS VALUES
  (arrow_cast(make_array(1, 2, 3, 4, 5, 6, 7, 8, 9, 10), 'FixedSizeList(10, Int64)'), 1, 1, ',', [2,3]),
  (arrow_cast(make_array(11, 12, 13, 14, 15, 16, 17, 18, 19, 20), 'FixedSizeList(10, Int64)'), 12, 2, '.', [4,5]),
  (arrow_cast(make_array(21, 22, 23, 24, 25, 26, 27, 28, 29, 30), 'FixedSizeList(10, Int64)'), 23, 3, '-', [6,7]),
  (arrow_cast(make_array(31, 32, 33, 34, 35, 26, 37, 38, 39, 40), 'FixedSizeList(10, Int64)'), 34, 4, 'ok', [8,9])
;

statement ok
CREATE TABLE arrays_range
AS VALUES
  (3, 10, 2),
  (4, 13, 3)
;

statement ok
CREATE TABLE arrays_with_repeating_elements
AS VALUES
  (make_array(1, 2, 1, 3, 2, 2, 1, 3, 2, 3), 2, 4, 3),
  (make_array(4, 4, 5, 5, 6, 5, 5, 5, 4, 4), 4, 7, 2),
  (make_array(7, 7, 7, 8, 7, 9, 7, 8, 7, 7), 7, 10, 5),
  (make_array(10, 11, 12, 10, 11, 12, 10, 11, 12, 10), 10, 13, 10)
;

statement ok
CREATE TABLE large_arrays_with_repeating_elements
AS
  SELECT
    arrow_cast(column1, 'LargeList(Int64)') AS column1,
    column2,
    column3,
    column4
  FROM arrays_with_repeating_elements
;

statement ok
CREATE TABLE fixed_arrays_with_repeating_elements
AS VALUES
  (arrow_cast(make_array(1, 2, 1, 3, 2, 2, 1, 3, 2, 3), 'FixedSizeList(10, Int64)'), 2, 4, 3),
  (arrow_cast(make_array(4, 4, 5, 5, 6, 5, 5, 5, 4, 4), 'FixedSizeList(10, Int64)'), 4, 7, 2),
  (arrow_cast(make_array(7, 7, 7, 8, 7, 9, 7, 8, 7, 7), 'FixedSizeList(10, Int64)'), 7, 10, 5),
  (arrow_cast(make_array(10, 11, 12, 10, 11, 12, 10, 11, 12, 10), 'FixedSizeList(10, Int64)'), 10, 13, 10)
;

statement ok
CREATE TABLE nested_arrays_with_repeating_elements
AS VALUES
  (make_array([1, 2, 3], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]), [4, 5, 6], [10, 11, 12], 3),
  (make_array([10, 11, 12], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]), [10, 11, 12], [19, 20, 21], 2),
  (make_array([19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]), [19, 20, 21], [28, 29, 30], 5),
  (make_array([28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]), [28, 29, 30], [37, 38, 39], 10)
;

statement ok
CREATE TABLE large_nested_arrays_with_repeating_elements
AS
  SELECT
    arrow_cast(column1, 'LargeList(List(Int64))') AS column1,
    column2,
    column3,
    column4
  FROM nested_arrays_with_repeating_elements
;

statement ok
CREATE TABLE fixed_size_nested_arrays_with_repeating_elements
AS VALUES
  (arrow_cast(make_array([1, 2, 3], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]), 'FixedSizeList(10, List(Int64))'), [4, 5, 6], [10, 11, 12], 3),
  (arrow_cast(make_array([10, 11, 12], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]), 'FixedSizeList(10, List(Int64))'), [10, 11, 12], [19, 20, 21], 2),
  (arrow_cast(make_array([19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]), 'FixedSizeList(10, List(Int64))'), [19, 20, 21], [28, 29, 30], 5),
  (arrow_cast(make_array([28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]), 'FixedSizeList(10, List(Int64))'), [28, 29, 30], [28, 29, 30], 10)
;

statement ok
CREATE TABLE arrays_distance_table
AS VALUES
  (make_array(1, 2, 3), make_array(1, 2, 3), make_array(1.1, 2.2, 3.3) , make_array(1.1, NULL, 3.3)),
  (make_array(1, 2, 3), make_array(4, 5, 6), make_array(4.4, 5.5, 6.6), make_array(4.4, NULL, 6.6)),
  (make_array(1, 2, 3), make_array(7, 8, 9), make_array(7.7, 8.8, 9.9), make_array(7.7, NULL, 9.9)),
  (make_array(1, 2, 3), make_array(10, 11, 12), make_array(10.1, 11.2, 12.3), make_array(10.1, NULL, 12.3))
;

statement ok
CREATE TABLE large_arrays_distance_table
AS
  SELECT
    arrow_cast(column1, 'LargeList(Int64)') AS column1,
    arrow_cast(column2, 'LargeList(Int64)') AS column2,
    arrow_cast(column3, 'LargeList(Float64)') AS column3,
    arrow_cast(column4, 'LargeList(Float64)') AS column4
FROM arrays_distance_table
;

statement ok
CREATE TABLE fixed_size_arrays_distance_table
AS
  SELECT
    arrow_cast(column1, 'FixedSizeList(3, Int64)') AS column1,
    arrow_cast(column2, 'FixedSizeList(3, Int64)') AS column2,
    arrow_cast(column3, 'FixedSizeList(3, Float64)') AS column3,
    arrow_cast(column4, 'FixedSizeList(3, Float64)') AS column4
FROM arrays_distance_table
;


# Array literal

## boolean coercion is not supported
query error
select [1, true, null]

## wrapped in array_length to get deterministic results
query I
SELECT array_length([now()])
----
1

## array literal with functions
query ?
select [abs(-1.2), sin(-1), log(2), ceil(3.141)]
----
[1.2, -0.8414709848078965, 0.3010299801826477, 4.0]

## array literal with nested types
query ???
select
  [struct('foo', 1)],
  [struct('foo', [1,2,3])],
  [struct('foo', [struct(3, 'x')])]
;
----
[{c0: foo, c1: 1}] [{c0: foo, c1: [1, 2, 3]}] [{c0: foo, c1: [{c0: 3, c1: x}]}]

query TTT
select arrow_typeof(column1), arrow_typeof(column2), arrow_typeof(column3) from arrays;
----
List(Field { name: "item", data_type: List(Field { name: "item", data_type: Int64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }), nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }) List(Field { name: "item", data_type: Float64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }) List(Field { name: "item", data_type: Utf8, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} })
List(Field { name: "item", data_type: List(Field { name: "item", data_type: Int64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }), nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }) List(Field { name: "item", data_type: Float64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }) List(Field { name: "item", data_type: Utf8, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} })
List(Field { name: "item", data_type: List(Field { name: "item", data_type: Int64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }), nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }) List(Field { name: "item", data_type: Float64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }) List(Field { name: "item", data_type: Utf8, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} })
List(Field { name: "item", data_type: List(Field { name: "item", data_type: Int64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }), nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }) List(Field { name: "item", data_type: Float64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }) List(Field { name: "item", data_type: Utf8, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} })
List(Field { name: "item", data_type: List(Field { name: "item", data_type: Int64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }), nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }) List(Field { name: "item", data_type: Float64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }) List(Field { name: "item", data_type: Utf8, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} })
List(Field { name: "item", data_type: List(Field { name: "item", data_type: Int64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }), nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }) List(Field { name: "item", data_type: Float64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }) List(Field { name: "item", data_type: Utf8, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} })
List(Field { name: "item", data_type: List(Field { name: "item", data_type: Int64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }), nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }) List(Field { name: "item", data_type: Float64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }) List(Field { name: "item", data_type: Utf8, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} })

# arrays table
query ???
select column1, column2, column3 from arrays;
----
[[NULL, 2], [3, NULL]] [1.1, 2.2, 3.3] [L, o, r, e, m]
[[3, 4], [5, 6]] [NULL, 5.5, 6.6] [i, p, NULL, u, m]
[[5, 6], [7, 8]] [7.7, 8.8, 9.9] [d, NULL, l, o, r]
[[7, NULL], [9, 10]] [10.1, NULL, 12.2] [s, i, t]
NULL [13.3, 14.4, 15.5] [a, m, e, t]
[[11, 12], [13, 14]] NULL [,]
[[15, 16], [NULL, 18]] [16.6, 17.7, 18.8] NULL

# nested_arrays table
query ??I??
select column1, column2, column3, column4, column5 from nested_arrays;
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4], [4, 5, 6]] [7, 8, 9] 2 [[[1, 2, 3], [4, 5, 6]], [[7, 8, 9], [10, 11, 12]]] [11, 12, 13]
[[4, 5, 6], [10, 11, 12], [4, 9, 8], [7, 8, 9], [10, 11, 12], [1, 8, 7]] [10, 11, 12] 3 [[[11, 12, 13], [14, 15, 16]], [[17, 18, 19], [20, 21, 22]]] [121, 131, 141]

# values table
query IIIRT
select a, b, c, d, e from values;
----
1 1 2 1.1 Lorem
2 3 4 2.2 ipsum
3 5 6 3.3 dolor
4 7 8 4.4 sit
NULL 9 10 5.5 amet
5 NULL 12 6.6 ,
6 11 NULL 7.7 consectetur
7 13 14 NULL adipiscing
8 15 16 8.8 NULL

# arrays_values table
query ?IIT
select column1, column2, column3, column4 from arrays_values;
----
[NULL, 2, 3, 4, 5, 6, 7, 8, 9, 10] 1 1 ,
[11, 12, 13, 14, 15, 16, 17, 18, NULL, 20] 12 2 .
[21, 22, 23, NULL, 25, 26, 27, 28, 29, 30] 23 3 -
[31, 32, 33, 34, 35, NULL, 37, 38, 39, 40] 34 4 ok
NULL 44 5 @
[41, 42, 43, 44, 45, 46, 47, 48, 49, 50] NULL 6 $
[51, 52, NULL, 54, 55, 56, 57, 58, 59, 60] 55 NULL ^
[61, 62, 63, 64, 65, 66, 67, 68, 69, 70] 66 7 NULL

# slices table
query ?II
select column1, column2, column3 from slices;
----
[NULL, 2, 3, 4, 5, 6, 7, 8, 9, 10] 1 1
[11, 12, 13, 14, 15, 16, 17, 18, NULL, 20] 2 -4
[21, 22, 23, NULL, 25, 26, 27, 28, 29, 30] 0 0
[31, 32, 33, 34, 35, NULL, 37, 38, 39, 40] -4 -7
NULL 4 5
[41, 42, 43, 44, 45, 46, 47, 48, 49, 50] NULL 6
[51, 52, NULL, 54, 55, 56, 57, 58, 59, 60] 5 NULL

query ??I?
select column1, column2, column3, column4 from arrays_values_v2;
----
[NULL, 2, 3] [4, 5, NULL] 12 [[30, 40, 50]]
NULL [7, NULL, 8] 13 [[NULL, NULL, 60]]
[9, NULL, 10] NULL 14 [[70, NULL, NULL]]
[NULL, 1] [NULL, 21] NULL NULL
[11, 12] NULL NULL NULL
NULL NULL NULL NULL

# arrays_values_without_nulls table
query ?IIT
select column1, column2, column3, column4 from arrays_values_without_nulls;
----
[1, 2, 3, 4, 5, 6, 7, 8, 9, 10] 1 1 ,
[11, 12, 13, 14, 15, 16, 17, 18, 19, 20] 12 2 .
[21, 22, 23, 24, 25, 26, 27, 28, 29, 30] 23 3 -
[31, 32, 33, 34, 35, 26, 37, 38, 39, 40] 34 4 ok

# arrays_with_repeating_elements table
query ?III
select column1, column2, column3, column4 from arrays_with_repeating_elements;
----
[1, 2, 1, 3, 2, 2, 1, 3, 2, 3] 2 4 3
[4, 4, 5, 5, 6, 5, 5, 5, 4, 4] 4 7 2
[7, 7, 7, 8, 7, 9, 7, 8, 7, 7] 7 10 5
[10, 11, 12, 10, 11, 12, 10, 11, 12, 10] 10 13 10

# nested_arrays_with_repeating_elements table
query ???I
select column1, column2, column3, column4 from nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]] [4, 5, 6] [10, 11, 12] 3
[[10, 11, 12], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]] [10, 11, 12] [19, 20, 21] 2
[[19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]] [19, 20, 21] [28, 29, 30] 5
[[28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]] [28, 29, 30] [37, 38, 39] 10


### Array index


## array[i]

# single index with scalars #1 (positive index)
query IRT
select make_array(1, 2, 3)[1], make_array(1.0, 2.0, 3.0)[2], make_array('h', 'e', 'l', 'l', 'o')[3];
----
1 2 l

# single index with scalars #2 (zero index)
query I
select make_array(1, 2, 3)[0];
----
NULL

# single index with scalars #3 (negative index)
query IRT
select make_array(1, 2, 3)[-1], make_array(1.0, 2.0, 3.0)[-2], make_array('h', 'e', 'l', 'l', 'o')[-3];
----
3 2 l

# single index with scalars #4 (complex index)
query IRT
select make_array(1, 2, 3)[1 + 2 - 1], make_array(1.0, 2.0, 3.0)[2 * 1 * 0 - 2], make_array('h', 'e', 'l', 'l', 'o')[2 - 3];
----
2 2 o

# single index with columns #1 (positive index)
query ?RT
select column1[2], column2[3], column3[1] from arrays;
----
[3, NULL] 3.3 L
[5, 6] 6.6 i
[7, 8] 9.9 d
[9, 10] 12.2 s
NULL 15.5 a
[13, 14] NULL ,
[NULL, 18] 18.8 NULL

# single index with columns #2 (zero index)
query ?RT
select column1[0], column2[0], column3[0] from arrays;
----
NULL NULL NULL
NULL NULL NULL
NULL NULL NULL
NULL NULL NULL
NULL NULL NULL
NULL NULL NULL
NULL NULL NULL

# single index with columns #3 (negative index)
query ?RT
select column1[-2], column2[-3], column3[-1] from arrays;
----
[NULL, 2] 1.1 m
[3, 4] NULL m
[5, 6] 7.7 r
[7, NULL] 10.1 t
NULL 13.3 t
[11, 12] NULL ,
[15, 16] 16.6 NULL

# single index with columns #4 (complex index)
query ?RT
select column1[9 - 7], column2[2 * 0], column3[1 - 3] from arrays;
----
[3, NULL] NULL e
[5, 6] NULL u
[7, 8] NULL o
[9, 10] NULL i
NULL NULL e
[13, 14] NULL NULL
[NULL, 18] NULL NULL

# TODO: support index as column
# single index with columns #5 (index as column)
# query ?
# select make_array(1, 2, 3, 4, 5)[column2] from arrays_with_repeating_elements;
# ----

# TODO: support argument and index as columns
# single index with columns #6 (argument and index as columns)
# query I
# select column1[column2] from arrays_with_repeating_elements;
# ----

## array[i:j]

# multiple index with columns #1 (positive index)
query ???
select make_array(1, 2, 3)[1:2], make_array(1.0, 2.0, 3.0)[2:3], make_array('h', 'e', 'l', 'l', 'o')[2:4];
----
[1, 2] [2.0, 3.0] [e, l, l]

query ???
select arrow_cast([1, 2, 3], 'LargeList(Int64)')[1:2],
       arrow_cast([1.0, 2.0, 3.0], 'LargeList(Int64)')[2:3],
       arrow_cast(['h', 'e', 'l', 'l', 'o'], 'LargeList(Utf8)')[2:4]
;
----
[1, 2] [2, 3] [e, l, l]

# multiple index with columns #2 (zero index)
query ???
select make_array(1, 2, 3)[0:0], make_array(1.0, 2.0, 3.0)[0:2], make_array('h', 'e', 'l', 'l', 'o')[0:6];
----
[] [1.0, 2.0] [h, e, l, l, o]

query ???
select arrow_cast([1, 2, 3], 'LargeList(Int64)')[0:0],
       arrow_cast([1.0, 2.0, 3.0], 'LargeList(Int64)')[0:2],
       arrow_cast(['h', 'e', 'l', 'l', 'o'], 'LargeList(Utf8)')[0:6]
;
----
[] [1, 2] [h, e, l, l, o]

query I
select arrow_cast([1, 2, 3], 'LargeList(Int64)')[1];
----
1

# TODO: support multiple negative index
# multiple index with columns #3 (negative index)
# query II
# select make_array(1, 2, 3)[-3:-1], make_array(1.0, 2.0, 3.0)[-3:-1], make_array('h', 'e', 'l', 'l', 'o')[-2:0];
# ----

# TODO: support complex index
# multiple index with columns #4 (complex index)
# query III
# select make_array(1, 2, 3)[2 + 1 - 1:10], make_array(1.0, 2.0, 3.0)[2 | 2:10], make_array('h', 'e', 'l', 'l', 'o')[6 ^ 6:10];
# ----

# multiple index with columns #1 (positive index)
query ???
select column1[2:4], column2[1:4], column3[3:4] from arrays;
----
[[3, NULL]] [1.1, 2.2, 3.3] [r, e]
[[5, 6]] [NULL, 5.5, 6.6] [NULL, u]
[[7, 8]] [7.7, 8.8, 9.9] [l, o]
[[9, 10]] [10.1, NULL, 12.2] [t]
NULL [13.3, 14.4, 15.5] [e, t]
[[13, 14]] NULL []
[[NULL, 18]] [16.6, 17.7, 18.8] NULL

# multiple index with columns #2 (zero index)
query ???
select column1[0:5], column2[0:3], column3[0:9] from arrays;
----
[[NULL, 2], [3, NULL]] [1.1, 2.2, 3.3] [L, o, r, e, m]
[[3, 4], [5, 6]] [NULL, 5.5, 6.6] [i, p, NULL, u, m]
[[5, 6], [7, 8]] [7.7, 8.8, 9.9] [d, NULL, l, o, r]
[[7, NULL], [9, 10]] [10.1, NULL, 12.2] [s, i, t]
NULL [13.3, 14.4, 15.5] [a, m, e, t]
[[11, 12], [13, 14]] NULL [,]
[[15, 16], [NULL, 18]] [16.6, 17.7, 18.8] NULL

# TODO: support negative index
# multiple index with columns #3 (negative index)
# query ?RT
# select column1[-2:-4], column2[-3:-5], column3[-1:-4] from arrays;
# ----
# [NULL, 2] 1.1 m

# TODO: support complex index
# multiple index with columns #4 (complex index)
# query ?RT
# select column1[9 - 7:2 + 2], column2[1 * 0:2 * 3], column3[1 + 1 - 0:5 % 3] from arrays;
# ----

# TODO: support first index as column
# multiple index with columns #5 (first index as column)
# query ?
# select make_array(1, 2, 3, 4, 5)[column2:4] from arrays_with_repeating_elements
# ----

# TODO: support last index as column
# multiple index with columns #6 (last index as column)
# query ?RT
# select make_array(1, 2, 3, 4, 5)[2:column3] from arrays_with_repeating_elements;
# ----

# TODO: support argument and indices as column
# multiple index with columns #7 (argument and indices as column)
# query ?RT
# select column1[column2:column3] from arrays_with_repeating_elements;
# ----

# array[i:j:k]

# multiple index with columns #1 (positive index)
query ???
select make_array(1, 2, 3)[1:2:2], make_array(1.0, 2.0, 3.0)[2:3:2], make_array('h', 'e', 'l', 'l', 'o')[2:4:2];
----
[1] [2.0] [e, l]

# multiple index with columns #2 (zero index)
query ???
select make_array(1, 2, 3)[0:0:2], make_array(1.0, 2.0, 3.0)[0:2:2], make_array('h', 'e', 'l', 'l', 'o')[0:6:2];
----
[] [1.0] [h, l, o]

#TODO: sqlparser does not support negative index
## multiple index with columns #3 (negative index)
#query ???
#select make_array(1, 2, 3)[-1:-2:-2], make_array(1.0, 2.0, 3.0)[-2:-3:-2], make_array('h', 'e', 'l', 'l', 'o')[-2:-4:-2];
#----
#[1] [2.0] [e, l]

# multiple index with columns #1 (positive index)
query ???
select column1[2:4:2], column2[1:4:2], column3[3:4:2] from arrays;
----
[[3, NULL]] [1.1, 3.3] [r]
[[5, 6]] [NULL, 6.6] [NULL]
[[7, 8]] [7.7, 9.9] [l]
[[9, 10]] [10.1, 12.2] [t]
NULL [13.3, 15.5] [e]
[[13, 14]] NULL []
[[NULL, 18]] [16.6, 18.8] NULL

# multiple index with columns #2 (zero index)
query ???
select column1[0:5:2], column2[0:3:2], column3[0:9:2] from arrays;
----
[[NULL, 2]] [1.1, 3.3] [L, r, m]
[[3, 4]] [NULL, 6.6] [i, NULL, m]
[[5, 6]] [7.7, 9.9] [d, l, r]
[[7, NULL]] [10.1, 12.2] [s, t]
NULL [13.3, 15.5] [a, e]
[[11, 12]] NULL [,]
[[15, 16]] [16.6, 18.8] NULL


### Array function tests


## make_array (aliases: `make_list`)

# make_array scalar function #1
query ???
select make_array(1, 2, 3), make_array(1.0, 2.0, 3.0), make_array('h', 'e', 'l', 'l', 'o');
----
[1, 2, 3] [1.0, 2.0, 3.0] [h, e, l, l, o]

# make_array scalar function #2
query ???
select make_array(1, 2, 3), make_array(make_array(1, 2), make_array(3, 4)), make_array([[[[1], [2]]]]);
----
[1, 2, 3] [[1, 2], [3, 4]] [[[[[1], [2]]]]]

# make_array scalar function #3
query ??
select make_array([1, 2, 3], [4, 5, 6], [7, 8, 9]), make_array([[1, 2], [3, 4]], [[5, 6], [7, 8]]);
----
[[1, 2, 3], [4, 5, 6], [7, 8, 9]] [[[1, 2], [3, 4]], [[5, 6], [7, 8]]]

# make_array scalar function #4
query ??
select make_array([1.0, 2.0], [3.0, 4.0]), make_array('h', 'e', 'l', 'l', 'o');
----
[[1.0, 2.0], [3.0, 4.0]] [h, e, l, l, o]

# make_array scalar function #5
query ?
select make_array(make_array(make_array(make_array(1, 2, 3), make_array(4, 5, 6)), make_array(make_array(7, 8, 9), make_array(10, 11, 12))))
----
[[[[1, 2, 3], [4, 5, 6]], [[7, 8, 9], [10, 11, 12]]]]

# make_array scalar function #6
query ?
select make_array()
----
[]

# make_array scalar function #7
query ??
select make_array(make_array()), make_array(make_array(make_array()))
----
[[]] [[[]]]

# make_list scalar function #8 (function alias: `make_array`)
query ???
select make_list(1, 2, 3), make_list(1.0, 2.0, 3.0), make_list('h', 'e', 'l', 'l', 'o');
----
[1, 2, 3] [1.0, 2.0, 3.0] [h, e, l, l, o]

# make_array scalar function with nulls
query ???
select make_array(1, NULL, 3), make_array(NULL, 2.0, NULL), make_array('h', NULL, 'l', NULL, 'o');
----
[1, NULL, 3] [NULL, 2.0, NULL] [h, NULL, l, NULL, o]

# make_array scalar function with nulls #2
query ??
select make_array(1, 2, NULL), make_array(make_array(NULL, 2), make_array(NULL, 3));
----
[1, 2, NULL] [[NULL, 2], [NULL, 3]]

# make_array scalar function with nulls #3
query ???
select make_array(NULL), make_array(NULL, NULL, NULL), make_array(make_array(NULL, NULL), make_array(NULL, NULL));
----
[NULL] [NULL, NULL, NULL] [[NULL, NULL], [NULL, NULL]]

# make_array with 1 columns
query ???
select make_array(a), make_array(d), make_array(e) from values;
----
[1] [1.1] [Lorem]
[2] [2.2] [ipsum]
[3] [3.3] [dolor]
[4] [4.4] [sit]
[NULL] [5.5] [amet]
[5] [6.6] [,]
[6] [7.7] [consectetur]
[7] [NULL] [adipiscing]
[8] [8.8] [NULL]

# make_array with 2 columns #1
query ??
select make_array(b, c), make_array(e, f) from values;
----
[1, 2] [Lorem, A]
[3, 4] [ipsum, ]
[5, 6] [dolor, BB]
[7, 8] [sit, NULL]
[9, 10] [amet, CCC]
[NULL, 12] [,, DD]
[11, NULL] [consectetur, E]
[13, 14] [adipiscing, F]
[15, 16] [NULL, ]

# make_array with 4 columns
query ?
select make_array(a, b, c, d) from values;
----
[1.0, 1.0, 2.0, 1.1]
[2.0, 3.0, 4.0, 2.2]
[3.0, 5.0, 6.0, 3.3]
[4.0, 7.0, 8.0, 4.4]
[NULL, 9.0, 10.0, 5.5]
[5.0, NULL, 12.0, 6.6]
[6.0, 11.0, NULL, 7.7]
[7.0, 13.0, 14.0, NULL]
[8.0, 15.0, 16.0, 8.8]

# make_array with column of list
query ??
select column1, column5 from arrays_values_without_nulls;
----
[1, 2, 3, 4, 5, 6, 7, 8, 9, 10] [2, 3]
[11, 12, 13, 14, 15, 16, 17, 18, 19, 20] [4, 5]
[21, 22, 23, 24, 25, 26, 27, 28, 29, 30] [6, 7]
[31, 32, 33, 34, 35, 26, 37, 38, 39, 40] [8, 9]

# make array with arrays of different types
query ?
select make_array(make_array(1), arrow_cast(make_array(-1), 'LargeList(Int8)'))
----
[[1], [-1]]

query T
select arrow_typeof(make_array(make_array(1), arrow_cast(make_array(-1), 'LargeList(Int8)')));
----
List(Field { name: "item", data_type: LargeList(Field { name: "item", data_type: Int64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }), nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} })


query ???
select make_array(column1),
       make_array(column1, column5),
       make_array(column1, make_array(50,51,52))
from arrays_values_without_nulls;
----
[[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]] [[1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [2, 3]] [[1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [50, 51, 52]]
[[11, 12, 13, 14, 15, 16, 17, 18, 19, 20]] [[11, 12, 13, 14, 15, 16, 17, 18, 19, 20], [4, 5]] [[11, 12, 13, 14, 15, 16, 17, 18, 19, 20], [50, 51, 52]]
[[21, 22, 23, 24, 25, 26, 27, 28, 29, 30]] [[21, 22, 23, 24, 25, 26, 27, 28, 29, 30], [6, 7]] [[21, 22, 23, 24, 25, 26, 27, 28, 29, 30], [50, 51, 52]]
[[31, 32, 33, 34, 35, 26, 37, 38, 39, 40]] [[31, 32, 33, 34, 35, 26, 37, 38, 39, 40], [8, 9]] [[31, 32, 33, 34, 35, 26, 37, 38, 39, 40], [50, 51, 52]]

## array_element (aliases: array_extract, list_extract, list_element)

# Testing with empty arguments should result in an error
query error DataFusion error: Error during planning: 'array_element' does not support zero arguments
select array_element();

# array_element error
query error
select array_element(1, 2);

# array_element with null
query I
select array_element([1, 2], NULL);
----
NULL

query I
select array_element(NULL, 2);
----
NULL

# array_element scalar function #1 (with positive index)
query IT
select array_element(make_array(1, 2, 3, 4, 5), 2), array_element(make_array('h', 'e', 'l', 'l', 'o'), 3);
----
2 l

query IT
select array_element(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 2), array_element(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 3);
----
2 l

query IT
select array_element(arrow_cast(make_array(1, 2, 3, 4, 5), 'FixedSizeList(5, Int64)'), 2), array_element(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'FixedSizeList(5, Utf8)'), 3);
----
2 l

# array_element scalar function #2 (with positive index; out of bounds)
query IT
select array_element(make_array(1, 2, 3, 4, 5), 7), array_element(make_array('h', 'e', 'l', 'l', 'o'), 11);
----
NULL NULL

query IT
select array_element(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 7), array_element(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 11);
----
NULL NULL

query IT
select array_element(arrow_cast(make_array(1, 2, 3, 4, 5), 'FixedSizeList(5, Int64)'), 7), array_element(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'FixedSizeList(5, Utf8)'), 11);
----
NULL NULL

# array_element scalar function #3 (with zero)
query IT
select array_element(make_array(1, 2, 3, 4, 5), 0), array_element(make_array('h', 'e', 'l', 'l', 'o'), 0);
----
NULL NULL

query IT
select array_element(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 0), array_element(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 0);
----
NULL NULL

query IT
select array_element(arrow_cast(make_array(1, 2, 3, 4, 5), 'FixedSizeList(5, Int64)'), 0), array_element(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'FixedSizeList(5, Utf8)'), 0);
----
NULL NULL

# array_element scalar function #4 (with NULL)
query IT
select array_element(make_array(1, 2, 3, 4, 5), NULL), array_element(make_array('h', 'e', 'l', 'l', 'o'), NULL);
----
NULL NULL

query IT
select array_element(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), NULL), array_element(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), NULL);
----
NULL NULL

query IT
select array_element(arrow_cast(make_array(1, 2, 3, 4, 5), 'FixedSizeList(5, Int64)'), NULL), array_element(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'FixedSizeList(5, Utf8)'), NULL);
----
NULL NULL

# array_element scalar function #5 (with negative index)
query IT
select array_element(make_array(1, 2, 3, 4, 5), -2), array_element(make_array('h', 'e', 'l', 'l', 'o'), -3);
----
4 l

query IT
select array_element(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), -2), array_element(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), -3);
----
4 l

query IT
select array_element(arrow_cast(make_array(1, 2, 3, 4, 5), 'FixedSizeList(5, Int64)'), -2), array_element(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'FixedSizeList(5, Utf8)'), -3);
----
4 l

# array_element scalar function #6 (with negative index; out of bounds)
query IT
select array_element(make_array(1, 2, 3, 4, 5), -11), array_element(make_array('h', 'e', 'l', 'l', 'o'), -7);
----
NULL NULL

query IT
select array_element(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), -11), array_element(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), -7);
----
NULL NULL

query IT
select array_element(arrow_cast(make_array(1, 2, 3, 4, 5), 'FixedSizeList(5, Int64)'), -11), array_element(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'FixedSizeList(5, Utf8)'), -7);
----
NULL NULL

# array_element scalar function #7 (nested array)
query ?
select array_element(make_array(make_array(1, 2, 3, 4, 5), make_array(6, 7, 8, 9, 10)), 1);
----
[1, 2, 3, 4, 5]

query ?
select array_element(arrow_cast(make_array(make_array(1, 2, 3, 4, 5), make_array(6, 7, 8, 9, 10)), 'LargeList(List(Int64))'), 1);
----
[1, 2, 3, 4, 5]

query ?
select array_element(arrow_cast(make_array(make_array(1, 2, 3, 4, 5), make_array(6, 7, 8, 9, 10)), 'FixedSizeList(2, List(Int64))'), 1);
----
[1, 2, 3, 4, 5]

# array_extract scalar function #8 (function alias `array_element`)
query IT
select array_extract(make_array(1, 2, 3, 4, 5), 2), array_extract(make_array('h', 'e', 'l', 'l', 'o'), 3);
----
2 l

query IT
select array_extract(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 2), array_extract(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 3);
----
2 l

query IT
select array_extract(arrow_cast(make_array(1, 2, 3, 4, 5), 'FixedSizeList(5, Int64)'), 2), array_extract(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'FixedSizeList(5, Utf8)'), 3);
----
2 l

# list_element scalar function #9 (function alias `array_element`)
query IT
select list_element(make_array(1, 2, 3, 4, 5), 2), list_element(make_array('h', 'e', 'l', 'l', 'o'), 3);
----
2 l

query IT
select list_element(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 2), array_extract(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 3);
----
2 l

query IT
select list_element(arrow_cast(make_array(1, 2, 3, 4, 5), 'FixedSizeList(5, Int64)'), 2), list_element(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'FixedSizeList(5, Utf8)'), 3);
----
2 l

# list_extract scalar function #10 (function alias `array_element`)
query IT
select list_extract(make_array(1, 2, 3, 4, 5), 2), list_extract(make_array('h', 'e', 'l', 'l', 'o'), 3);
----
2 l

query IT
select list_extract(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 2), list_extract(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 3);
----
2 l

query IT
select list_extract(arrow_cast(make_array(1, 2, 3, 4, 5), 'FixedSizeList(5, Int64)'), 2), list_extract(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'FixedSizeList(5, Utf8)'), 3);
----
2 l

# array_element with columns
query I
select array_element(column1, column2) from slices;
----
NULL
12
NULL
37
NULL
NULL
55

query I
select array_element(arrow_cast(column1, 'LargeList(Int64)'), column2) from slices;
----
NULL
12
NULL
37
NULL
NULL
55

query I
select array_element(column1, column2) from fixed_slices;
----
NULL
12
NULL
37
NULL
55

# array_element with columns and scalars
query II
select array_element(make_array(1, 2, 3, 4, 5), column2), array_element(column1, 3) from slices;
----
1 3
2 13
NULL 23
2 33
4 NULL
NULL 43
5 NULL

query II
select array_element(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), column2), array_element(arrow_cast(column1, 'LargeList(Int64)'), 3) from slices;
----
1 3
2 13
NULL 23
2 33
4 NULL
NULL 43
5 NULL

query II
select array_element(make_array(1, 2, 3, 4, 5), column2), array_element(column1, 3) from fixed_slices;
----
1 3
2 13
NULL 23
2 33
NULL 43
5 NULL


## array_max
# array_max scalar function #1 (with positive index)
query I
select array_max(make_array(5, 3, 6, 4));
----
6

query I
select array_max(make_array(5, 3, 4, NULL, 6, NULL));
----
6

query I
select array_max(make_array(NULL, NULL));
----
NULL

query T
select array_max(make_array('h', 'e', 'o', 'l', 'l'));
----
o

query T
select array_max(make_array('h', 'e', 'l', NULL, 'l', 'o', NULL));
----
o

query B
select array_max(make_array(false, true, false, true));
----
true

query B
select array_max(make_array(false, true, NULL, false, true));
----
true

query D
select array_max(make_array(DATE '1992-09-01', DATE '1993-03-01', DATE '1999-05-01', DATE '1985-11-01'));
----
1999-05-01

query D
select array_max(make_array(DATE '1995-09-01', DATE '1999-05-01', DATE '1993-03-01', NULL));
----
1999-05-01

query P
select array_max(make_array(TIMESTAMP '1992-09-01', TIMESTAMP '1995-06-01', TIMESTAMP '1984-10-01'));
----
1995-06-01T00:00:00

query P
select array_max(make_array(NULL, TIMESTAMP '1996-10-01', TIMESTAMP '1995-06-01'));
----
1996-10-01T00:00:00

query R
select array_max(make_array(5.1, -3.2, 6.3, 4.9));
----
6.3

query ?I
select input, array_max(input) from (select make_array(d - 1, d, d + 1) input from (values (0), (10), (20), (30), (NULL)) t(d))
----
[-1, 0, 1] 1
[9, 10, 11] 11
[19, 20, 21] 21
[29, 30, 31] 31
[NULL, NULL, NULL] NULL

query II
select array_max(arrow_cast(make_array(1, 2, 3), 'FixedSizeList(3, Int64)')), array_max(arrow_cast(make_array(1), 'FixedSizeList(1, Int64)'));
----
3 1

query I
select array_max(make_array());
----
NULL

# Testing with empty arguments should result in an error
query error DataFusion error: Error during planning: 'array_max' does not support zero arguments
select array_max();


## array_pop_back (aliases: `list_pop_back`)

# array_pop_back scalar function with null
#TODO: https://github.com/apache/datafusion/issues/7142
# follow clickhouse and duckdb
#query ?
#select array_pop_back(null);
#----
#NULL

# array_pop_back scalar function #1
query ??
select array_pop_back(make_array(1, 2, 3, 4, 5)), array_pop_back(make_array('h', 'e', 'l', 'l', 'o'));
----
[1, 2, 3, 4] [h, e, l, l]

query ??
select array_pop_back(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)')), array_pop_back(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'));
----
[1, 2, 3, 4] [h, e, l, l]

query ??
select array_pop_back(arrow_cast(make_array(1, 2, 3, 4, 5), 'FixedSizeList(5, Int64)')), array_pop_back(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'FixedSizeList(5, Utf8)'));
----
[1, 2, 3, 4] [h, e, l, l]

# array_pop_back scalar function #2 (after array_pop_back, array is empty)
query ?
select array_pop_back(make_array(1));
----
[]

query ?
select array_pop_back(arrow_cast(make_array(1), 'LargeList(Int64)'));
----
[]

query ?
select array_pop_back(arrow_cast(make_array(1), 'FixedSizeList(1, Int64)'));
----
[]

# array_pop_back scalar function #3 (array_pop_back the empty array)
query ?
select array_pop_back(array_pop_back(make_array(1)));
----
[]

query ?
select array_pop_back(array_pop_back(arrow_cast(make_array(1), 'LargeList(Int64)')));
----
[]

query ?
select array_pop_back(array_pop_back(arrow_cast(make_array(1), 'FixedSizeList(1, Int64)')));
----
[]

# array_pop_back scalar function #4 (array_pop_back the arrays which have NULL)
query ??
select array_pop_back(make_array(1, 2, 3, 4, NULL)), array_pop_back(make_array(NULL, 'e', 'l', NULL, 'o'));
----
[1, 2, 3, 4] [NULL, e, l, NULL]

query ??
select array_pop_back(arrow_cast(make_array(1, 2, 3, 4, NULL), 'LargeList(Int64)')), array_pop_back(arrow_cast(make_array(NULL, 'e', 'l', NULL, 'o'), 'LargeList(Utf8)'));
----
[1, 2, 3, 4] [NULL, e, l, NULL]

query ??
select array_pop_back(arrow_cast(make_array(1, 2, 3, 4, NULL), 'FixedSizeList(5, Int64)')), array_pop_back(arrow_cast(make_array(NULL, 'e', 'l', NULL, 'o'), 'FixedSizeList(5, Utf8)'));
----
[1, 2, 3, 4] [NULL, e, l, NULL]

# array_pop_back scalar function #5 (array_pop_back the nested arrays)
query ?
select array_pop_back(make_array(make_array(1, 2, 3), make_array(2, 9, 1), make_array(7, 8, 9), make_array(1, 2, 3), make_array(1, 7, 4), make_array(4, 5, 6)));
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4]]

query ?
select array_pop_back(arrow_cast(make_array(make_array(1, 2, 3), make_array(2, 9, 1), make_array(7, 8, 9), make_array(1, 2, 3), make_array(1, 7, 4), make_array(4, 5, 6)), 'LargeList(List(Int64))'));
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4]]

query ?
select array_pop_back(arrow_cast(make_array(make_array(1, 2, 3), make_array(2, 9, 1), make_array(7, 8, 9), make_array(1, 2, 3), make_array(1, 7, 4), make_array(4, 5, 6)), 'FixedSizeList(6, List(Int64))'));
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4]]

# array_pop_back scalar function #6 (array_pop_back the nested arrays with NULL)
query ?
select array_pop_back(make_array(make_array(1, 2, 3), make_array(2, 9, 1), make_array(7, 8, 9), make_array(1, 2, 3), make_array(1, 7, 4), NULL));
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4]]

query ?
select array_pop_back(arrow_cast(make_array(make_array(1, 2, 3), make_array(2, 9, 1), make_array(7, 8, 9), make_array(1, 2, 3), make_array(1, 7, 4), NULL), 'LargeList(List(Int64))'));
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4]]

query ?
select array_pop_back(arrow_cast(make_array(make_array(1, 2, 3), make_array(2, 9, 1), make_array(7, 8, 9), make_array(1, 2, 3), make_array(1, 7, 4), NULL), 'FixedSizeList(6, List(Int64))'));
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4]]

# array_pop_back scalar function #7 (array_pop_back the nested arrays with NULL)
query ?
select array_pop_back(make_array(make_array(1, 2, 3), make_array(2, 9, 1), make_array(7, 8, 9), NULL, make_array(1, 7, 4)));
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], NULL]

query ?
select array_pop_back(arrow_cast(make_array(make_array(1, 2, 3), make_array(2, 9, 1), make_array(7, 8, 9), NULL, make_array(1, 7, 4)), 'LargeList(List(Int64))'));
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], NULL]

query ?
select array_pop_back(arrow_cast(make_array(make_array(1, 2, 3), make_array(2, 9, 1), make_array(7, 8, 9), NULL, make_array(1, 7, 4)), 'FixedSizeList(5, List(Int64))'));
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], NULL]

# array_pop_back scalar function #8 (after array_pop_back, nested array is empty)
query ?
select array_pop_back(make_array(make_array(1, 2, 3)));
----
[]

query ?
select array_pop_back(arrow_cast(make_array(make_array(1, 2, 3)), 'LargeList(List(Int64))'));
----
[]

query ?
select array_pop_back(arrow_cast(make_array(make_array(1, 2, 3)), 'FixedSizeList(1, List(Int64))'));
----
[]

# array_pop_back with columns
query ?
select array_pop_back(column1) from arrayspop;
----
[1, 2]
[3, 4, 5]
[6, 7, 8, NULL]
[NULL, NULL]
NULL
[NULL, 10, 11]

query ?
select array_pop_back(arrow_cast(column1, 'LargeList(Int64)')) from arrayspop;
----
[1, 2]
[3, 4, 5]
[6, 7, 8, NULL]
[NULL, NULL]
NULL
[NULL, 10, 11]

query ?
select array_pop_back(column1) from large_arrayspop;
----
[1, 2]
[3, 4, 5]
[6, 7, 8, NULL]
[NULL, NULL]
NULL
[NULL, 10, 11]

query ?
select array_pop_back(arrow_cast(column1, 'LargeList(Int64)')) from large_arrayspop;
----
[1, 2]
[3, 4, 5]
[6, 7, 8, NULL]
[NULL, NULL]
NULL
[NULL, 10, 11]

## array_pop_front (aliases: `list_pop_front`)

#TODO:https://github.com/apache/datafusion/issues/7142
# array_pop_front scalar function with null
# follow clickhouse and duckdb
#query ?
#select array_pop_front(null);
#----
#NULL

# array_pop_front scalar function #1
query ??
select array_pop_front(make_array(1, 2, 3, 4, 5)), array_pop_front(make_array('h', 'e', 'l', 'l', 'o'));
----
[2, 3, 4, 5] [e, l, l, o]

query ??
select array_pop_front(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)')), array_pop_front(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'));
----
[2, 3, 4, 5] [e, l, l, o]

query ??
select array_pop_front(arrow_cast(make_array(1, 2, 3, 4, 5), 'FixedSizeList(5, Int64)')), array_pop_front(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'FixedSizeList(5, Utf8)'));
----
[2, 3, 4, 5] [e, l, l, o]

# array_pop_front scalar function #2 (after array_pop_front, array is empty)
query ?
select array_pop_front(make_array(1));
----
[]

query ?
select array_pop_front(arrow_cast(make_array(1), 'LargeList(Int64)'));
----
[]

query ?
select array_pop_front(arrow_cast(make_array(1), 'FixedSizeList(1, Int64)'));
----
[]

# array_pop_front scalar function #3 (array_pop_front the empty array)
query ?
select array_pop_front(array_pop_front(make_array(1)));
----
[]

query ?
select array_pop_front(array_pop_front(arrow_cast(make_array(1), 'LargeList(Int64)')));
----
[]

query ?
select array_pop_front(array_pop_front(arrow_cast(make_array(1), 'FixedSizeList(1, Int64)')));
----
[]

# array_pop_front scalar function #5 (array_pop_front the nested arrays)
query ?
select array_pop_front(make_array(make_array(1, 2, 3), make_array(2, 9, 1), make_array(7, 8, 9), make_array(1, 2, 3), make_array(1, 7, 4), make_array(4, 5, 6)));
----
[[2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4], [4, 5, 6]]

query ?
select array_pop_front(arrow_cast(make_array(make_array(1, 2, 3), make_array(2, 9, 1), make_array(7, 8, 9), make_array(1, 2, 3), make_array(1, 7, 4), make_array(4, 5, 6)), 'LargeList(List(Int64))'));
----
[[2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4], [4, 5, 6]]

query ?
select array_pop_front(arrow_cast(make_array(make_array(1, 2, 3), make_array(2, 9, 1), make_array(7, 8, 9), make_array(1, 2, 3), make_array(1, 7, 4), make_array(4, 5, 6)), 'FixedSizeList(6, List(Int64))'));
----
[[2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4], [4, 5, 6]]

# array_pop_front scalar function #6 (array_pop_front the nested arrays with NULL)
query ?
select array_pop_front(make_array(NULL, make_array(1, 2, 3), make_array(2, 9, 1), make_array(7, 8, 9), make_array(1, 2, 3), make_array(1, 7, 4)));
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4]]

query ?
select array_pop_front(arrow_cast(make_array(NULL, make_array(1, 2, 3), make_array(2, 9, 1), make_array(7, 8, 9), make_array(1, 2, 3), make_array(1, 7, 4)), 'LargeList(List(Int64))'));
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4]]

query ?
select array_pop_front(arrow_cast(make_array(NULL, make_array(1, 2, 3), make_array(2, 9, 1), make_array(7, 8, 9), make_array(1, 2, 3), make_array(1, 7, 4)), 'FixedSizeList(6, List(Int64))'));
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4]]

# array_pop_front scalar function #8 (after array_pop_front, nested array is empty)
query ?
select array_pop_front(make_array(make_array(1, 2, 3)));
----
[]

query ?
select array_pop_front(arrow_cast(make_array(make_array(1, 2, 3)), 'LargeList(List(Int64))'));
----
[]

query ?
select array_pop_front(arrow_cast(make_array(make_array(1, 2, 3)), 'FixedSizeList(1, List(Int64))'));
----
[]

## array_slice (aliases: list_slice)

# array_slice scalar function #1 (with positive indexes)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), 2, 4), array_slice(make_array('h', 'e', 'l', 'l', 'o'), 1, 2);
----
[2, 3, 4] [h, e]

query ????
select array_slice(make_array(1, 2, 3, 4, 5), 1, 5, 2), array_slice(make_array('h', 'e', 'l', 'l', 'o'), 1, 5, 2),
       array_slice(make_array(1, 2, 3, 4, 5), 0, 5, 2), array_slice(make_array('h', 'e', 'l', 'l', 'o'), 0, 5, 2);
----
[1, 3, 5] [h, l, o] [1, 3, 5] [h, l, o]

query ??
select array_slice(make_array(1, 2, 3, 4, 5), 1, 5, -1), array_slice(make_array('h', 'e', 'l', 'l', 'o'), 1, 5, -1);
----
[] []

query error Execution error: array_slice got invalid stride: 0, it cannot be 0
select array_slice(make_array(1, 2, 3, 4, 5), 1, 5, 0), array_slice(make_array('h', 'e', 'l', 'l', 'o'), 1, 5, 0);

query ??
select array_slice(make_array(1, 2, 3, 4, 5), 5, 1, -2), array_slice(make_array('h', 'e', 'l', 'l', 'o'), 5, 1, -2);
----
[5, 3, 1] [o, l, h]

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 2, 4), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 1, 2);
----
[2, 3, 4] [h, e]

# array_slice scalar function #2 (with positive indexes; full array)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), 0, 6), array_slice(make_array('h', 'e', 'l', 'l', 'o'), 0, 5);
----
[1, 2, 3, 4, 5] [h, e, l, l, o]

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 0, 6), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 0, 5);
----
[1, 2, 3, 4, 5] [h, e, l, l, o]

# array_slice scalar function #3 (with positive indexes; first index = second index)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), 4, 4), array_slice(make_array('h', 'e', 'l', 'l', 'o'), 3, 3);
----
[4] [l]

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 4, 4), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 3, 3);
----
[4] [l]

# array_slice scalar function #4 (with positive indexes; first index > second_index)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), 2, 1), array_slice(make_array('h', 'e', 'l', 'l', 'o'), 4, 1);
----
[] []

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 2, 1), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 4, 1);
----
[] []

# array_slice scalar function #5 (with positive indexes; out of bounds)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), 2, 6), array_slice(make_array('h', 'e', 'l', 'l', 'o'), 3, 7);
----
[2, 3, 4, 5] [l, l, o]

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 2, 6), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 3, 7);
----
[2, 3, 4, 5] [l, l, o]

# array_slice scalar function #6 (with positive indexes; nested array)
query ?
select array_slice(make_array(make_array(1, 2, 3, 4, 5), make_array(6, 7, 8, 9, 10)), 1, 1);
----
[[1, 2, 3, 4, 5]]

query ?
select array_slice(arrow_cast(make_array(make_array(1, 2, 3, 4, 5), make_array(6, 7, 8, 9, 10)), 'LargeList(List(Int64))'), 1, 1);
----
[[1, 2, 3, 4, 5]]

# array_slice scalar function #7 (with zero and positive number)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), 0, 4), array_slice(make_array('h', 'e', 'l', 'l', 'o'), 0, 3);
----
[1, 2, 3, 4] [h, e, l]

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 0, 4), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 0, 3);
----
[1, 2, 3, 4] [h, e, l]

# array_slice scalar function #8 (with NULL and positive number)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), NULL, 4), array_slice(make_array('h', 'e', 'l', 'l', 'o'), NULL, 3);
----
NULL NULL

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), NULL, 4), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), NULL, 3);
----
NULL NULL

# array_slice scalar function #9 (with positive number and NULL)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), 2, NULL), array_slice(make_array('h', 'e', 'l', 'l', 'o'), 3, NULL);
----
NULL NULL

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 2, NULL), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 3, NULL);
----
NULL NULL

# array_slice scalar function #10 (with zero-zero)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), 0, 0), array_slice(make_array('h', 'e', 'l', 'l', 'o'), 0, 0);
----
[] []

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 0, 0), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 0, 0);
----
[] []

# array_slice scalar function #11 (with NULL-NULL)
query error
select array_slice(make_array(1, 2, 3, 4, 5), NULL), array_slice(make_array('h', 'e', 'l', 'l', 'o'), NULL);

query error
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), NULL), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), NULL);

# array_slice scalar function #12 (with zero and negative number)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), 0, -4), array_slice(make_array('h', 'e', 'l', 'l', 'o'), 0, -3);
----
[1, 2] [h, e, l]

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 0, -4), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 0, -3);
----
[1, 2] [h, e, l]

# array_slice scalar function #13 (with negative number and NULL)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), -2, NULL), array_slice(make_array('h', 'e', 'l', 'l', 'o'), -3, NULL);
----
NULL NULL

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), -2, NULL), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), -3, NULL);
----
NULL NULL

# array_slice scalar function #14 (with NULL and negative number)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), NULL, -4), array_slice(make_array('h', 'e', 'l', 'l', 'o'), NULL, -3);
----
NULL NULL

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), NULL, -4), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), NULL, -3);
----
NULL NULL

# array_slice scalar function #15 (with negative indexes)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), -4, -1), array_slice(make_array('h', 'e', 'l', 'l', 'o'), -3, -1);
----
[2, 3, 4, 5] [l, l, o]

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), -4, -1), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), -3, -1);
----
[2, 3, 4, 5] [l, l, o]

# array_slice scalar function #16 (with negative indexes; almost full array (only with negative indices cannot return full array))
query ??
select array_slice(make_array(1, 2, 3, 4, 5), -5, -1), array_slice(make_array('h', 'e', 'l', 'l', 'o'), -5, -1);
----
[1, 2, 3, 4, 5] [h, e, l, l, o]

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), -5, -1), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), -5, -1);
----
[1, 2, 3, 4, 5] [h, e, l, l, o]

# array_slice scalar function #17 (with negative indexes; first index = second index)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), -4, -4), array_slice(make_array('h', 'e', 'l', 'l', 'o'), -3, -3);
----
[2] [l]

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), -4, -4), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), -3, -3);
----
[2] [l]

# array_slice scalar function #18 (with negative indexes; first index > second_index)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), -4, -6), array_slice(make_array('h', 'e', 'l', 'l', 'o'), -3, -6);
----
[] []

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), -4, -6), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), -3, -6);
----
[] []

# array_slice scalar function #19 (with negative indexes; out of bounds)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), -7, -2), array_slice(make_array('h', 'e', 'l', 'l', 'o'), -7, -3);
----
[1, 2, 3, 4] [h, e, l]

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), -7, -2), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), -7, -3);
----
[1, 2, 3, 4] [h, e, l]

# array_slice scalar function #20 (with negative indexes; nested array)
query ??
select array_slice(make_array(make_array(1, 2, 3, 4, 5), make_array(6, 7, 8, 9, 10)), -2, -1), array_slice(make_array(make_array(1, 2, 3), make_array(6, 7, 8)), -1, -1);
----
[[1, 2, 3, 4, 5], [6, 7, 8, 9, 10]] [[6, 7, 8]]

query ??
select array_slice(arrow_cast(make_array(make_array(1, 2, 3, 4, 5), make_array(6, 7, 8, 9, 10)), 'LargeList(List(Int64))'), -2, -1), array_slice(arrow_cast(make_array(make_array(1, 2, 3), make_array(6, 7, 8)), 'LargeList(List(Int64))'), -1, -1);
----
[[1, 2, 3, 4, 5], [6, 7, 8, 9, 10]] [[6, 7, 8]]


# array_slice scalar function #21 (with first positive index and last negative index)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), 2, -3), array_slice(make_array('h', 'e', 'l', 'l', 'o'), 2, -2);
----
[2, 3] [e, l, l]

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 2, -3), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 2, -2);
----
[2, 3] [e, l, l]

# array_slice scalar function #22 (with first negative index and last positive index)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), -2, 5), array_slice(make_array('h', 'e', 'l', 'l', 'o'), -3, 4);
----
[4, 5] [l, l]

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), -2, 5), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), -3, 4);
----
[4, 5] [l, l]

# list_slice scalar function #23 (function alias `array_slice`)
query ??
select list_slice(make_array(1, 2, 3, 4, 5), 2, 4), list_slice(make_array('h', 'e', 'l', 'l', 'o'), 1, 2);
----
[2, 3, 4] [h, e]

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 2, 4), array_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 1, 2);
----
[2, 3, 4] [h, e]

# array_slice scalar function #24 (with first negative index larger than len)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), -2147483648, 1), list_slice(make_array('h', 'e', 'l', 'l', 'o'), -2147483648, 1);
----
[1] [h]

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), -9223372036854775808, 1), list_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), -9223372036854775808, 1);
----
[1] [h]

# array_slice scalar function #25 (with negative step and equal indexes)
query ??
select array_slice(make_array(1, 2, 3, 4, 5), 2, 2, -1), list_slice(make_array('h', 'e', 'l', 'l', 'o'), 2, 2, -1);
----
[2] [e]

query ??
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 2, 2, -1), list_slice(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 2, 2, -1);
----
[2] [e]

# array_slice with columns
query ?
select array_slice(column1, column2, column3) from slices;
----
[NULL]
[12, 13, 14, 15, 16, 17]
[]
[]
NULL
NULL
NULL

query ?
select array_slice(arrow_cast(column1, 'LargeList(Int64)'), column2, column3) from slices;
----
[NULL]
[12, 13, 14, 15, 16, 17]
[]
[]
NULL
NULL
NULL

# TODO: support NULLS in output instead of `[]`
# array_slice with columns and scalars
query ???
select array_slice(make_array(1, 2, 3, 4, 5), column2, column3), array_slice(column1, 3, column3), array_slice(column1, column2, 5) from slices;
----
[1] [] [NULL, 2, 3, 4, 5]
[2] [13, 14, 15, 16, 17] [12, 13, 14, 15]
[] [] [21, 22, 23, NULL, 25]
[] [33, 34] []
[4, 5] NULL NULL
NULL [43, 44, 45, 46] NULL
NULL NULL [55]

query ???
select array_slice(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), column2, column3), array_slice(arrow_cast(column1, 'LargeList(Int64)'), 3, column3), array_slice(arrow_cast(column1, 'LargeList(Int64)'), column2, 5) from slices;
----
[1] [] [NULL, 2, 3, 4, 5]
[2] [13, 14, 15, 16, 17] [12, 13, 14, 15]
[] [] [21, 22, 23, NULL, 25]
[] [33, 34] []
[4, 5] NULL NULL
NULL [43, 44, 45, 46] NULL
NULL NULL [55]

# Test issue: https://github.com/apache/datafusion/issues/10425
# `from` may be larger than `to` and `stride` is positive
query ????
select array_slice(a, -1, 2, 1), array_slice(a, -1, 2),
       array_slice(a, 3, 2, 1), array_slice(a, 3, 2)
  from (values ([1.0, 2.0, 3.0, 3.0]), ([4.0, 5.0, 3.0]), ([6.0])) t(a);
----
[] [] [] []
[] [] [] []
[6.0] [6.0] [] []

# Testing with empty arguments should result in an error
query error DataFusion error: Error during planning: 'array_slice' does not support zero arguments
select array_slice();

query error Failed to coerce arguments
select array_slice(3.5, NULL, NULL);

## array_any_value (aliases: list_any_value)

# Testing with empty arguments should result in an error
query error
select array_any_value();

# Testing with non-array arguments should result in an error
query error
select array_any_value(1), array_any_value('a'), array_any_value(NULL);

# array_any_value scalar function #1 (with null and non-null elements)

query ITII
select array_any_value(make_array(NULL, 1, 2, 3, 4, 5)), array_any_value(make_array(NULL, 'h', 'e', 'l', 'l', 'o')), array_any_value(make_array(NULL, NULL)), array_any_value(make_array(NULL, NULL, 1, 2, 3));
----
1 h NULL 1

query ITITI
select array_any_value(arrow_cast(make_array(NULL, 1, 2, 3, 4, 5), 'LargeList(Int64)')), array_any_value(arrow_cast(make_array(NULL, 'h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)')), array_any_value(arrow_cast(make_array(NULL, NULL), 'LargeList(Int64)')), array_any_value(arrow_cast(make_array(NULL, NULL), 'LargeList(Utf8)')), array_any_value(arrow_cast(make_array(NULL, NULL, 1, 2, 3), 'LargeList(Int64)'));;
----
1 h NULL NULL 1

query ITITI
select array_any_value(arrow_cast(make_array(NULL, 1, 2, 3, 4, 5), 'FixedSizeList(6, Int64)')), array_any_value(arrow_cast(make_array(NULL, 'h', 'e', 'l', 'l', 'o'), 'FixedSizeList(6, Utf8)')), array_any_value(arrow_cast(make_array(NULL, NULL), 'FixedSizeList(2, Int64)')), array_any_value(arrow_cast(make_array(NULL, NULL), 'FixedSizeList(2, Utf8)')), array_any_value(arrow_cast(make_array(NULL, NULL, 1, 2, 3, 4), 'FixedSizeList(6, Int64)'));
----
1 h NULL NULL 1

# array_any_value scalar function #2 (with nested array)

query ?
select array_any_value(make_array(NULL, make_array(NULL, 1, 2, 3, 4, 5), make_array(NULL, 6, 7, 8, 9, 10)));
----
[NULL, 1, 2, 3, 4, 5]

query ?
select array_any_value(arrow_cast(make_array(NULL, make_array(NULL, 1, 2, 3, 4, 5), make_array(NULL, 6, 7, 8, 9, 10)), 'LargeList(List(Int64))'));
----
[NULL, 1, 2, 3, 4, 5]

query ?
select array_any_value(arrow_cast(make_array(NULL, make_array(NULL, 1, 2, 3, 4, 5), make_array(NULL, 6, 7, 8, 9, 10)), 'FixedSizeList(3, List(Int64))'));
----
[NULL, 1, 2, 3, 4, 5]

# array_any_value scalar function #3 (using function alias `list_any_value`)
query IT
select list_any_value(make_array(NULL, 1, 2, 3, 4, 5)), list_any_value(make_array(NULL, 'h', 'e', 'l', 'l', 'o'));
----
1 h

query IT
select list_any_value(arrow_cast(make_array(NULL, 1, 2, 3, 4, 5), 'LargeList(Int64)')), list_any_value(arrow_cast(make_array(NULL, 'h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'));
----
1 h

query IT
select list_any_value(arrow_cast(make_array(NULL, 1, 2, 3, 4, 5), 'FixedSizeList(6, Int64)')), list_any_value(arrow_cast(make_array(NULL, 'h', 'e', 'l', 'l', 'o'), 'FixedSizeList(6, Utf8)'));
----
1 h

# array_any_value with columns

query I
select array_any_value(column1) from slices;
----
2
11
21
31
NULL
41
51

query I
select array_any_value(arrow_cast(column1, 'LargeList(Int64)')) from slices;
----
2
11
21
31
NULL
41
51

query I
select array_any_value(column1) from fixed_slices;
----
2
11
21
31
41
51

# array_any_value with columns and scalars

query II
select array_any_value(make_array(NULL, 1, 2, 3, 4, 5)), array_any_value(column1) from slices;
----
1 2
1 11
1 21
1 31
1 NULL
1 41
1 51

query II
select array_any_value(arrow_cast(make_array(NULL, 1, 2, 3, 4, 5), 'LargeList(Int64)')), array_any_value(arrow_cast(column1, 'LargeList(Int64)')) from slices;
----
1 2
1 11
1 21
1 31
1 NULL
1 41
1 51

query II
select array_any_value(make_array(NULL, 1, 2, 3, 4, 5)), array_any_value(column1) from fixed_slices;
----
1 2
1 11
1 21
1 31
1 41
1 51

# make_array with nulls
query ???????
select make_array(make_array('a','b'), null),
       make_array(make_array('a','b'), null, make_array('c','d')),
       make_array(null, make_array('a','b'), null),
       make_array(null, make_array('a','b'), null, null, make_array('c','d')),
       make_array(['a', 'bc', 'def'], null, make_array('rust')),
       make_array([1,2,3], null, make_array(4,5,6,7)),
       make_array(null, 1, null, 2, null, 3, null, null, 4, 5);
----
[[a, b], NULL] [[a, b], NULL, [c, d]] [NULL, [a, b], NULL] [NULL, [a, b], NULL, NULL, [c, d]] [[a, bc, def], NULL, [rust]] [[1, 2, 3], NULL, [4, 5, 6, 7]] [NULL, 1, NULL, 2, NULL, 3, NULL, NULL, 4, 5]

query ?
select make_array(column5, null, column5) from arrays_values_without_nulls;
----
[[2, 3], NULL, [2, 3]]
[[4, 5], NULL, [4, 5]]
[[6, 7], NULL, [6, 7]]
[[8, 9], NULL, [8, 9]]

query ?
select make_array(['a','b'], null);
----
[[a, b], NULL]

## array_sort (aliases: `list_sort`)
query ???
select array_sort(make_array(1, 3, null, 5, NULL, -5)), array_sort(make_array(1, 3, null, 2), 'ASC'), array_sort(make_array(1, 3, null, 2), 'desc', 'NULLS FIRST');
----
[NULL, NULL, -5, 1, 3, 5] [NULL, 1, 2, 3] [NULL, 3, 2, 1]

query ?
select array_sort(column1, 'DESC', 'NULLS LAST') from arrays_values;
----
[10, 9, 8, 7, 6, 5, 4, 3, 2, NULL]
[20, 18, 17, 16, 15, 14, 13, 12, 11, NULL]
[30, 29, 28, 27, 26, 25, 23, 22, 21, NULL]
[40, 39, 38, 37, 35, 34, 33, 32, 31, NULL]
NULL
[50, 49, 48, 47, 46, 45, 44, 43, 42, 41]
[60, 59, 58, 57, 56, 55, 54, 52, 51, NULL]
[70, 69, 68, 67, 66, 65, 64, 63, 62, 61]

query ?
select array_sort(column1, 'ASC', 'NULLS FIRST') from arrays_values;
----
[NULL, 2, 3, 4, 5, 6, 7, 8, 9, 10]
[NULL, 11, 12, 13, 14, 15, 16, 17, 18, 20]
[NULL, 21, 22, 23, 25, 26, 27, 28, 29, 30]
[NULL, 31, 32, 33, 34, 35, 37, 38, 39, 40]
NULL
[41, 42, 43, 44, 45, 46, 47, 48, 49, 50]
[NULL, 51, 52, 54, 55, 56, 57, 58, 59, 60]
[61, 62, 63, 64, 65, 66, 67, 68, 69, 70]

# test with empty array
query ?
select array_sort([]);
----
[]

# test with null arguments
query ?
select array_sort(NULL);
----
NULL

query ?
select array_sort(column1, NULL) from arrays_values;
----
NULL
NULL
NULL
NULL
NULL
NULL
NULL
NULL

query ??
select array_sort(column1, 'DESC', NULL), array_sort(column1, 'ASC', NULL) from arrays_values;
----
NULL NULL
NULL NULL
NULL NULL
NULL NULL
NULL NULL
NULL NULL
NULL NULL
NULL NULL

query ??
select array_sort(column1, NULL, 'NULLS FIRST'), array_sort(column1, NULL, 'NULLS LAST') from arrays_values;
----
NULL NULL
NULL NULL
NULL NULL
NULL NULL
NULL NULL
NULL NULL
NULL NULL
NULL NULL

query ?
select array_sort([struct('foo', 3), struct('foo', 1), struct('bar', 1)])
----
[{c0: bar, c1: 1}, {c0: foo, c1: 1}, {c0: foo, c1: 3}]

## test with argument of incorrect types
query error DataFusion error: Execution error: the second parameter of array_sort expects DESC or ASC
select array_sort([1, 3, null, 5, NULL, -5], 1), array_sort([1, 3, null, 5, NULL, -5], 'DESC', 1), array_sort([1, 3, null, 5, NULL, -5], 1, 1);

# test with empty row, the row that does not match the condition has row count 0
statement ok
create table t1(a int, b int) as values (100, 1), (101, 2), (102, 3), (101, 2);

# rowsort is to ensure the order of group by is deterministic, array_sort has no effect here, since the sum() always returns single row.
query ? rowsort
select array_sort([sum(a)]) from t1 where a > 100 group by b;
----
[102]
[202]

statement ok
drop table t1;

## list_sort (aliases: `array_sort`)
query ???
select list_sort(make_array(1, 3, null, 5, NULL, -5)), list_sort(make_array(1, 3, null, 2), 'ASC'), list_sort(make_array(1, 3, null, 2), 'desc', 'NULLS FIRST');
----
[NULL, NULL, -5, 1, 3, 5] [NULL, 1, 2, 3] [NULL, 3, 2, 1]


## array_append (aliases: `list_append`, `array_push_back`, `list_push_back`)

# array_append with NULLs

query ?
select array_append(null, 1);
----
[1]

query error
select array_append(null, [2, 3]);

query error
select array_append(null, [[4]]);

query ????
select
  array_append(make_array(), 4),
  array_append(make_array(), null),
  array_append(make_array(1, null, 3), 4),
  array_append(make_array(null, null), 1)
;
----
[4] [NULL] [1, NULL, 3, 4] [NULL, NULL, 1]

query ????
select
  array_append(arrow_cast(make_array(), 'LargeList(Int64)'), 4),
  array_append(arrow_cast(make_array(), 'LargeList(Int64)'), null),
  array_append(arrow_cast(make_array(1, null, 3), 'LargeList(Int64)'), 4),
  array_append(arrow_cast(make_array(null, null), 'LargeList(Int64)'), 1)
;
----
[4] [NULL] [1, NULL, 3, 4] [NULL, NULL, 1]

query ??
select
  array_append(arrow_cast(make_array(1, null, 3), 'FixedSizeList(3, Int64)'), 4),
  array_append(arrow_cast(make_array(null, null), 'FixedSizeList(2, Int64)'), 1)
;
----
[1, NULL, 3, 4] [NULL, NULL, 1]

# test invalid (non-null)
query error
select array_append(1, 2);

query error
select array_append(1, [2]);

query error
select array_append([1], [2]);

query ??
select
  array_append(make_array(make_array(1, null, 3)), make_array(null)),
  array_append(make_array(make_array(1, null, 3)), null);
----
[[1, NULL, 3], [NULL]] [[1, NULL, 3], NULL]

query ??
select
  array_append(arrow_cast(make_array(make_array(1, null, 3)), 'LargeList(LargeList(Int64))'), arrow_cast(make_array(null), 'LargeList(Int64)')),
  array_append(arrow_cast(make_array(make_array(1, null, 3)), 'LargeList(LargeList(Int64))'), null);
----
[[1, NULL, 3], [NULL]] [[1, NULL, 3], NULL]

query ??
select
  array_append(arrow_cast(make_array(make_array(1, null, 3)), 'FixedSizeList(1, List(Int64))'), [null]),
  array_append(arrow_cast(make_array(make_array(1, null, 3)), 'FixedSizeList(1, List(Int64))'), null);
----
[[1, NULL, 3], [NULL]] [[1, NULL, 3], NULL]

# array_append scalar function #3
query ???
select array_append(make_array(1, 2, 3), 4), array_append(make_array(1.0, 2.0, 3.0), 4.0), array_append(make_array('h', 'e', 'l', 'l'), 'o');
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

query ???
select array_append(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)'), 4), array_append(arrow_cast(make_array(1.0, 2.0, 3.0), 'LargeList(Float64)'), 4.0), array_append(arrow_cast(make_array('h', 'e', 'l', 'l'), 'LargeList(Utf8)'), 'o');
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

query ???
select array_append(arrow_cast(make_array(1, 2, 3), 'FixedSizeList(3, Int64)'), 4), array_append(arrow_cast(make_array(1.0, 2.0, 3.0), 'FixedSizeList(3, Float64)'), 4.0), array_append(arrow_cast(make_array('h', 'e', 'l', 'l'), 'FixedSizeList(4, Utf8)'), 'o');
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

# array_append scalar function #4 (element is list)
query ???
select array_append(make_array([1], [2], [3]), make_array(4)), array_append(make_array([1.0], [2.0], [3.0]), make_array(4.0)), array_append(make_array(['h'], ['e'], ['l'], ['l']), make_array('o'));
----
[[1], [2], [3], [4]] [[1.0], [2.0], [3.0], [4.0]] [[h], [e], [l], [l], [o]]

query ???
select array_append(arrow_cast(make_array([1], [2], [3]), 'LargeList(LargeList(Int64))'), arrow_cast(make_array(4), 'LargeList(Int64)')), array_append(arrow_cast(make_array([1.0], [2.0], [3.0]), 'LargeList(LargeList(Float64))'), arrow_cast(make_array(4.0), 'LargeList(Float64)')), array_append(arrow_cast(make_array(['h'], ['e'], ['l'], ['l']), 'LargeList(LargeList(Utf8))'), arrow_cast(make_array('o'), 'LargeList(Utf8)'));
----
[[1], [2], [3], [4]] [[1.0], [2.0], [3.0], [4.0]] [[h], [e], [l], [l], [o]]

query ???
select array_append(arrow_cast(make_array([1], [2], [3]), 'FixedSizeList(3, List(Int64))'), [4]), array_append(arrow_cast(make_array([1.0], [2.0], [3.0]), 'FixedSizeList(3, List(Float64))'), [4.0]), array_append(arrow_cast(make_array(['h'], ['e'], ['l'], ['l']), 'FixedSizeList(4, List(Utf8))'), ['o']);
----
[[1], [2], [3], [4]] [[1.0], [2.0], [3.0], [4.0]] [[h], [e], [l], [l], [o]]

# list_append scalar function #5 (function alias `array_append`)
query ???
select list_append(make_array(1, 2, 3), 4), list_append(make_array(1.0, 2.0, 3.0), 4.0), list_append(make_array('h', 'e', 'l', 'l'), 'o');
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

query ???
select list_append(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)'), 4), list_append(arrow_cast(make_array(1.0, 2.0, 3.0), 'LargeList(Float64)'), 4.0), list_append(make_array('h', 'e', 'l', 'l'), 'o');
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

# array_push_back scalar function #6 (function alias `array_append`)
query ???
select array_push_back(make_array(1, 2, 3), 4), array_push_back(make_array(1.0, 2.0, 3.0), 4.0), array_push_back(make_array('h', 'e', 'l', 'l'), 'o');
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

query ???
select array_push_back(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)'), 4), array_push_back(arrow_cast(make_array(1.0, 2.0, 3.0), 'LargeList(Float64)'), 4.0), array_push_back(make_array('h', 'e', 'l', 'l'), 'o');
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

# list_push_back scalar function #7 (function alias `array_append`)
query ???
select list_push_back(make_array(1, 2, 3), 4), list_push_back(make_array(1.0, 2.0, 3.0), 4.0), list_push_back(make_array('h', 'e', 'l', 'l'), 'o');
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

query ???
select list_push_back(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)'), 4), list_push_back(arrow_cast(make_array(1.0, 2.0, 3.0), 'LargeList(Float64)'), 4.0), list_push_back(make_array('h', 'e', 'l', 'l'), 'o');
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

# array_append with columns #1
query ?
select array_append(column1, column2) from arrays_values;
----
[NULL, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1]
[11, 12, 13, 14, 15, 16, 17, 18, NULL, 20, 12]
[21, 22, 23, NULL, 25, 26, 27, 28, 29, 30, 23]
[31, 32, 33, 34, 35, NULL, 37, 38, 39, 40, 34]
[44]
[41, 42, 43, 44, 45, 46, 47, 48, 49, 50, NULL]
[51, 52, NULL, 54, 55, 56, 57, 58, 59, 60, 55]
[61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 66]

query ?
select array_append(column1, column2) from large_arrays_values;
----
[NULL, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1]
[11, 12, 13, 14, 15, 16, 17, 18, NULL, 20, 12]
[21, 22, 23, NULL, 25, 26, 27, 28, 29, 30, 23]
[31, 32, 33, 34, 35, NULL, 37, 38, 39, 40, 34]
[44]
[41, 42, 43, 44, 45, 46, 47, 48, 49, 50, NULL]
[51, 52, NULL, 54, 55, 56, 57, 58, 59, 60, 55]
[61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 66]

query ?
select array_append(column1, column2) from fixed_arrays_values;
----
[NULL, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1]
[11, 12, 13, 14, 15, 16, 17, 18, NULL, 20, 12]
[21, 22, 23, NULL, 25, 26, 27, 28, 29, 30, 23]
[31, 32, 33, 34, 35, NULL, 37, 38, 39, 40, 34]
[NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 44]
[41, 42, 43, 44, 45, 46, 47, 48, 49, 50, NULL]
[51, 52, NULL, 54, 55, 56, 57, 58, 59, 60, 55]
[61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 66]

# array_append with columns #2 (element is list)
query ?
select array_append(column1, column2) from nested_arrays;
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4], [4, 5, 6], [7, 8, 9]]
[[4, 5, 6], [10, 11, 12], [4, 9, 8], [7, 8, 9], [10, 11, 12], [1, 8, 7], [10, 11, 12]]

query ?
select array_append(column1, column2) from large_nested_arrays;
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4], [4, 5, 6], [7, 8, 9]]
[[4, 5, 6], [10, 11, 12], [4, 9, 8], [7, 8, 9], [10, 11, 12], [1, 8, 7], [10, 11, 12]]

query ?
select array_append(column1, column2) from fixed_size_nested_arrays;
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4], [4, 5, 6], [7, 8, 9]]
[[4, 5, 6], [10, 11, 12], [4, 9, 8], [7, 8, 9], [10, 11, 12], [1, 8, 7], [10, 11, 12]]

# array_append with columns and scalars #1
query ??
select array_append(column2, 100.1), array_append(column3, '.') from arrays;
----
[1.1, 2.2, 3.3, 100.1] [L, o, r, e, m, .]
[NULL, 5.5, 6.6, 100.1] [i, p, NULL, u, m, .]
[7.7, 8.8, 9.9, 100.1] [d, NULL, l, o, r, .]
[10.1, NULL, 12.2, 100.1] [s, i, t, .]
[13.3, 14.4, 15.5, 100.1] [a, m, e, t, .]
[100.1] [,, .]
[16.6, 17.7, 18.8, 100.1] [.]

query ??
select array_append(column2, 100.1), array_append(column3, '.') from large_arrays;
----
[1.1, 2.2, 3.3, 100.1] [L, o, r, e, m, .]
[NULL, 5.5, 6.6, 100.1] [i, p, NULL, u, m, .]
[7.7, 8.8, 9.9, 100.1] [d, NULL, l, o, r, .]
[10.1, NULL, 12.2, 100.1] [s, i, t, .]
[13.3, 14.4, 15.5, 100.1] [a, m, e, t, .]
[100.1] [,, .]
[16.6, 17.7, 18.8, 100.1] [.]

query ??
select array_append(column2, 100.1), array_append(column3, '.') from fixed_size_arrays;
----
[1.1, 2.2, 3.3, 100.1] [L, o, r, e, m, .]
[NULL, 5.5, 6.6, 100.1] [i, p, NULL, u, m, .]
[7.7, 8.8, 9.9, 100.1] [d, NULL, l, o, r, .]
[10.1, NULL, 12.2, 100.1] [s, i, t, a, b, .]
[13.3, 14.4, 15.5, 100.1] [a, m, e, t, x, .]
[NULL, NULL, NULL, 100.1] [,, a, b, c, d, .]
[16.6, 17.7, 18.8, 100.1] [NULL, NULL, NULL, NULL, NULL, .]

# array_append with columns and scalars #2
query ??
select array_append(column1, make_array(1, 11, 111)), array_append(make_array(make_array(1, 2, 3), make_array(11, 12, 13)), column2) from nested_arrays;
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4], [4, 5, 6], [1, 11, 111]] [[1, 2, 3], [11, 12, 13], [7, 8, 9]]
[[4, 5, 6], [10, 11, 12], [4, 9, 8], [7, 8, 9], [10, 11, 12], [1, 8, 7], [1, 11, 111]] [[1, 2, 3], [11, 12, 13], [10, 11, 12]]

query ??
select array_append(column1, arrow_cast(make_array(1, 11, 111), 'LargeList(Int64)')), array_append(arrow_cast(make_array(make_array(1, 2, 3), make_array(11, 12, 13)), 'LargeList(LargeList(Int64))'), column2) from large_nested_arrays;
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4], [4, 5, 6], [1, 11, 111]] [[1, 2, 3], [11, 12, 13], [7, 8, 9]]
[[4, 5, 6], [10, 11, 12], [4, 9, 8], [7, 8, 9], [10, 11, 12], [1, 8, 7], [1, 11, 111]] [[1, 2, 3], [11, 12, 13], [10, 11, 12]]

query ??
select array_append(column1, arrow_cast(make_array(1, 11, 111), 'FixedSizeList(3, Int64)')), array_append(arrow_cast(make_array(make_array(1, 2, 3), make_array(11, 12, 13)), 'FixedSizeList(2, List(Int64))'), column2) from fixed_size_nested_arrays;
----
[[1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4], [4, 5, 6], [1, 11, 111]] [[1, 2, 3], [11, 12, 13], [7, 8, 9]]
[[4, 5, 6], [10, 11, 12], [4, 9, 8], [7, 8, 9], [10, 11, 12], [1, 8, 7], [1, 11, 111]] [[1, 2, 3], [11, 12, 13], [10, 11, 12]]

## array_prepend (aliases: `list_prepend`, `array_push_front`, `list_push_front`)

# array_prepend with NULLs

# DuckDB: [4]
# ClickHouse: Null
# Since they dont have the same result, we just follow Postgres, return error
query ?
select array_prepend(4, NULL);
----
[4]

query ?
select array_prepend(4, []);
----
[4]

query ?
select array_prepend(4, [null]);
----
[4, NULL]

# DuckDB: [null]
# ClickHouse: [null]
query ?
select array_prepend(null, []);
----
[NULL]

query ?
select array_prepend(null, [1]);
----
[NULL, 1]

query ?
select array_prepend(null, [[1,2,3]]);
----
[NULL, [1, 2, 3]]

# DuckDB: [[]]
# ClickHouse: [[]]
# TODO: We may also return [[]]
query error
select array_prepend([], []);

query ?
select array_prepend(null, null);
----
[NULL]

query ?
select array_append([], null);
----
[NULL]


# array_prepend scalar function #3
query ???
select array_prepend(1, make_array(2, 3, 4)), array_prepend(1.0, make_array(2.0, 3.0, 4.0)), array_prepend('h', make_array('e', 'l', 'l', 'o'));
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

query ???
select array_prepend(1, arrow_cast(make_array(2, 3, 4), 'LargeList(Int64)')), array_prepend(1.0, arrow_cast(make_array(2.0, 3.0, 4.0), 'LargeList(Float64)')), array_prepend('h', arrow_cast(make_array('e', 'l', 'l', 'o'), 'LargeList(Utf8)'));
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

query ???
select array_prepend(1, arrow_cast([2, 3, 4], 'FixedSizeList(3, Int64)')), array_prepend(1.0, arrow_cast([2.0, 3.0, 4.0], 'FixedSizeList(3, Float64)')), array_prepend('h', arrow_cast(['e', 'l', 'l', 'o'], 'FixedSizeList(4, Utf8)'));
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

# array_prepend scalar function #4 (element is list)
query ???
select array_prepend(make_array(1), make_array(make_array(2), make_array(3), make_array(4))), array_prepend(make_array(1.0), make_array([2.0], [3.0], [4.0])), array_prepend(make_array('h'), make_array(['e'], ['l'], ['l'], ['o']));
----
[[1], [2], [3], [4]] [[1.0], [2.0], [3.0], [4.0]] [[h], [e], [l], [l], [o]]

query ???
select array_prepend(arrow_cast(make_array(1), 'LargeList(Int64)'), arrow_cast(make_array(make_array(2), make_array(3), make_array(4)), 'LargeList(LargeList(Int64))')),
       array_prepend(arrow_cast(make_array(1.0), 'LargeList(Float64)'), arrow_cast(make_array([2.0], [3.0], [4.0]), 'LargeList(LargeList(Float64))')),
       array_prepend(arrow_cast(make_array('h'), 'LargeList(Utf8)'), arrow_cast(make_array(['e'], ['l'], ['l'], ['o']), 'LargeList(LargeList(Utf8))'));
----
[[1], [2], [3], [4]] [[1.0], [2.0], [3.0], [4.0]] [[h], [e], [l], [l], [o]]

query ???
select array_prepend(arrow_cast([1], 'FixedSizeList(1, Int64)'), arrow_cast([[1], [2], [3]], 'FixedSizeList(3, List(Int64))')),
       array_prepend(arrow_cast([1.0], 'FixedSizeList(1, Float64)'), arrow_cast([[2.0], [3.0], [4.0]], 'FixedSizeList(3, List(Float64))')),
       array_prepend(arrow_cast(['h'], 'FixedSizeList(1, Utf8)'), arrow_cast([['e'], ['l'], ['l'], ['o']], 'FixedSizeList(4, List(Utf8))'));
----
[[1], [1], [2], [3]] [[1.0], [2.0], [3.0], [4.0]] [[h], [e], [l], [l], [o]]

# list_prepend scalar function #5 (function alias `array_prepend`)
query ???
select list_prepend(1, make_array(2, 3, 4)), list_prepend(1.0, make_array(2.0, 3.0, 4.0)), list_prepend('h', make_array('e', 'l', 'l', 'o'));
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

query ???
select list_prepend(1, arrow_cast(make_array(2, 3, 4), 'LargeList(Int64)')), list_prepend(1.0, arrow_cast(make_array(2.0, 3.0, 4.0), 'LargeList(Float64)')), list_prepend('h', arrow_cast(make_array('e', 'l', 'l', 'o'), 'LargeList(Utf8)'));
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

# array_push_front scalar function #6 (function alias `array_prepend`)
query ???
select array_push_front(1, make_array(2, 3, 4)), array_push_front(1.0, make_array(2.0, 3.0, 4.0)), array_push_front('h', make_array('e', 'l', 'l', 'o'));
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

query ???
select array_push_front(1, arrow_cast(make_array(2, 3, 4), 'LargeList(Int64)')), array_push_front(1.0, arrow_cast(make_array(2.0, 3.0, 4.0), 'LargeList(Float64)')), array_push_front('h', arrow_cast(make_array('e', 'l', 'l', 'o'), 'LargeList(Utf8)'));
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

# list_push_front scalar function #7 (function alias `array_prepend`)
query ???
select list_push_front(1, make_array(2, 3, 4)), list_push_front(1.0, make_array(2.0, 3.0, 4.0)), list_push_front('h', make_array('e', 'l', 'l', 'o'));
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

query ???
select list_push_front(1, arrow_cast(make_array(2, 3, 4), 'LargeList(Int64)')), list_push_front(1.0, arrow_cast(make_array(2.0, 3.0, 4.0), 'LargeList(Float64)')), list_push_front('h', arrow_cast(make_array('e', 'l', 'l', 'o'), 'LargeList(Utf8)'));
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

# array_prepend scalar function #7 (element is fixed size list)
query ???
select array_prepend(arrow_cast(make_array(1), 'FixedSizeList(1, Int64)'), make_array(arrow_cast(make_array(2), 'FixedSizeList(1, Int64)'), arrow_cast(make_array(3), 'FixedSizeList(1, Int64)'), arrow_cast(make_array(4), 'FixedSizeList(1, Int64)'))),
       array_prepend(arrow_cast(make_array(1.0), 'FixedSizeList(1, Float64)'), make_array(arrow_cast([2.0], 'FixedSizeList(1, Float64)'), arrow_cast([3.0], 'FixedSizeList(1, Float64)'), arrow_cast([4.0], 'FixedSizeList(1, Float64)'))),
       array_prepend(arrow_cast(make_array('h'), 'FixedSizeList(1, Utf8)'), make_array(arrow_cast(['e'], 'FixedSizeList(1, Utf8)'), arrow_cast(['l'], 'FixedSizeList(1, Utf8)'), arrow_cast(['l'], 'FixedSizeList(1, Utf8)'), arrow_cast(['o'], 'FixedSizeList(1, Utf8)')));
----
[[1], [2], [3], [4]] [[1.0], [2.0], [3.0], [4.0]] [[h], [e], [l], [l], [o]]

query ???
select array_prepend(arrow_cast(make_array(1), 'FixedSizeList(1, Int64)'), arrow_cast(make_array(make_array(2), make_array(3), make_array(4)), 'LargeList(FixedSizeList(1, Int64))')),
       array_prepend(arrow_cast(make_array(1.0), 'FixedSizeList(1, Float64)'), arrow_cast(make_array([2.0], [3.0], [4.0]), 'LargeList(FixedSizeList(1, Float64))')),
       array_prepend(arrow_cast(make_array('h'), 'FixedSizeList(1, Utf8)'), arrow_cast(make_array(['e'], ['l'], ['l'], ['o']), 'LargeList(FixedSizeList(1, Utf8))'));
----
[[1], [2], [3], [4]] [[1.0], [2.0], [3.0], [4.0]] [[h], [e], [l], [l], [o]]

query ???
select array_prepend(arrow_cast([1], 'FixedSizeList(1, Int64)'), arrow_cast([[1], [2], [3]], 'FixedSizeList(3, FixedSizeList(1, Int64))')),
       array_prepend(arrow_cast([1.0], 'FixedSizeList(1, Float64)'), arrow_cast([[2.0], [3.0], [4.0]], 'FixedSizeList(3, FixedSizeList(1, Float64))')),
       array_prepend(arrow_cast(['h'], 'FixedSizeList(1, Utf8)'), arrow_cast([['e'], ['l'], ['l'], ['o']], 'FixedSizeList(4, FixedSizeList(1, Utf8))'));
----
[[1], [1], [2], [3]] [[1.0], [2.0], [3.0], [4.0]] [[h], [e], [l], [l], [o]]

# array_prepend with columns #1
query ?
select array_prepend(column2, column1) from arrays_values;
----
[1, NULL, 2, 3, 4, 5, 6, 7, 8, 9, 10]
[12, 11, 12, 13, 14, 15, 16, 17, 18, NULL, 20]
[23, 21, 22, 23, NULL, 25, 26, 27, 28, 29, 30]
[34, 31, 32, 33, 34, 35, NULL, 37, 38, 39, 40]
[44]
[NULL, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50]
[55, 51, 52, NULL, 54, 55, 56, 57, 58, 59, 60]
[66, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70]

query ?
select array_prepend(column2, column1) from large_arrays_values;
----
[1, NULL, 2, 3, 4, 5, 6, 7, 8, 9, 10]
[12, 11, 12, 13, 14, 15, 16, 17, 18, NULL, 20]
[23, 21, 22, 23, NULL, 25, 26, 27, 28, 29, 30]
[34, 31, 32, 33, 34, 35, NULL, 37, 38, 39, 40]
[44]
[NULL, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50]
[55, 51, 52, NULL, 54, 55, 56, 57, 58, 59, 60]
[66, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70]

query ?
select array_prepend(column2, column1) from fixed_arrays_values;
----
[1, NULL, 2, 3, 4, 5, 6, 7, 8, 9, 10]
[12, 11, 12, 13, 14, 15, 16, 17, 18, NULL, 20]
[23, 21, 22, 23, NULL, 25, 26, 27, 28, 29, 30]
[34, 31, 32, 33, 34, 35, NULL, 37, 38, 39, 40]
[44, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL]
[NULL, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50]
[55, 51, 52, NULL, 54, 55, 56, 57, 58, 59, 60]
[66, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70]

# array_prepend with columns #2 (element is list)
query ?
select array_prepend(column2, column1) from nested_arrays;
----
[[7, 8, 9], [1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4], [4, 5, 6]]
[[10, 11, 12], [4, 5, 6], [10, 11, 12], [4, 9, 8], [7, 8, 9], [10, 11, 12], [1, 8, 7]]

query ?
select array_prepend(column2, column1) from large_nested_arrays;
----
[[7, 8, 9], [1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4], [4, 5, 6]]
[[10, 11, 12], [4, 5, 6], [10, 11, 12], [4, 9, 8], [7, 8, 9], [10, 11, 12], [1, 8, 7]]

query ?
select array_prepend(column2, column1) from fixed_size_nested_arrays;
----
[[7, 8, 9], [1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4], [4, 5, 6]]
[[10, 11, 12], [4, 5, 6], [10, 11, 12], [4, 9, 8], [7, 8, 9], [10, 11, 12], [1, 8, 7]]

# array_prepend with columns and scalars #1
query ??
select array_prepend(100.1, column2), array_prepend('.', column3) from arrays;
----
[100.1, 1.1, 2.2, 3.3] [., L, o, r, e, m]
[100.1, NULL, 5.5, 6.6] [., i, p, NULL, u, m]
[100.1, 7.7, 8.8, 9.9] [., d, NULL, l, o, r]
[100.1, 10.1, NULL, 12.2] [., s, i, t]
[100.1, 13.3, 14.4, 15.5] [., a, m, e, t]
[100.1] [., ,]
[100.1, 16.6, 17.7, 18.8] [.]

query ??
select array_prepend(100.1, column2), array_prepend('.', column3) from large_arrays;
----
[100.1, 1.1, 2.2, 3.3] [., L, o, r, e, m]
[100.1, NULL, 5.5, 6.6] [., i, p, NULL, u, m]
[100.1, 7.7, 8.8, 9.9] [., d, NULL, l, o, r]
[100.1, 10.1, NULL, 12.2] [., s, i, t]
[100.1, 13.3, 14.4, 15.5] [., a, m, e, t]
[100.1] [., ,]
[100.1, 16.6, 17.7, 18.8] [.]

query ??
select array_prepend(100.1, column2), array_prepend('.', column3) from fixed_size_arrays;
----
[100.1, 1.1, 2.2, 3.3] [., L, o, r, e, m]
[100.1, NULL, 5.5, 6.6] [., i, p, NULL, u, m]
[100.1, 7.7, 8.8, 9.9] [., d, NULL, l, o, r]
[100.1, 10.1, NULL, 12.2] [., s, i, t, a, b]
[100.1, 13.3, 14.4, 15.5] [., a, m, e, t, x]
[100.1, NULL, NULL, NULL] [., ,, a, b, c, d]
[100.1, 16.6, 17.7, 18.8] [., NULL, NULL, NULL, NULL, NULL]

# array_prepend with columns and scalars #2 (element is list)
query ??
select array_prepend(make_array(1, 11, 111), column1), array_prepend(column2, make_array(make_array(1, 2, 3), make_array(11, 12, 13))) from nested_arrays;
----
[[1, 11, 111], [1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4], [4, 5, 6]] [[7, 8, 9], [1, 2, 3], [11, 12, 13]]
[[1, 11, 111], [4, 5, 6], [10, 11, 12], [4, 9, 8], [7, 8, 9], [10, 11, 12], [1, 8, 7]] [[10, 11, 12], [1, 2, 3], [11, 12, 13]]

query ??
select array_prepend(arrow_cast(make_array(1, 11, 111), 'LargeList(Int64)'), column1), array_prepend(column2, arrow_cast(make_array(make_array(1, 2, 3), make_array(11, 12, 13)), 'LargeList(LargeList(Int64))')) from large_nested_arrays;
----
[[1, 11, 111], [1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4], [4, 5, 6]] [[7, 8, 9], [1, 2, 3], [11, 12, 13]]
[[1, 11, 111], [4, 5, 6], [10, 11, 12], [4, 9, 8], [7, 8, 9], [10, 11, 12], [1, 8, 7]] [[10, 11, 12], [1, 2, 3], [11, 12, 13]]

query ??
select array_prepend(arrow_cast(make_array(1, 11, 111), 'FixedSizeList(3, Int64)'), column1), array_prepend(column2, arrow_cast(make_array(make_array(1, 2, 3), make_array(11, 12, 13)), 'FixedSizeList(2, List(Int64))')) from fixed_size_nested_arrays;
----
[[1, 11, 111], [1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4], [4, 5, 6]] [[7, 8, 9], [1, 2, 3], [11, 12, 13]]
[[1, 11, 111], [4, 5, 6], [10, 11, 12], [4, 9, 8], [7, 8, 9], [10, 11, 12], [1, 8, 7]] [[10, 11, 12], [1, 2, 3], [11, 12, 13]]

## array_repeat (aliases: `list_repeat`)

# array_repeat scalar function #1
query ????????
select
  array_repeat(1, 5),
  array_repeat(3.14, 3),
  array_repeat('l', 4),
  array_repeat(null, 2),
  list_repeat(-1, 5),
  list_repeat(-3.14, 0),
  list_repeat('rust', 4),
  list_repeat(null, 0);
----
[1, 1, 1, 1, 1] [3.14, 3.14, 3.14] [l, l, l, l] [NULL, NULL] [-1, -1, -1, -1, -1] [] [rust, rust, rust, rust] []

# array_repeat scalar function #2 (element as list)
query ????
select
  array_repeat([1], 5),
  array_repeat([1.1, 2.2, 3.3], 3),
  array_repeat([null, null], 3),
  array_repeat([[1, 2], [3, 4]], 2);
----
[[1], [1], [1], [1], [1]] [[1.1, 2.2, 3.3], [1.1, 2.2, 3.3], [1.1, 2.2, 3.3]] [[NULL, NULL], [NULL, NULL], [NULL, NULL]] [[[1, 2], [3, 4]], [[1, 2], [3, 4]]]

query ????
select
  array_repeat(arrow_cast([1], 'LargeList(Int64)'), 5),
  array_repeat(arrow_cast([1.1, 2.2, 3.3], 'LargeList(Float64)'), 3),
  array_repeat(arrow_cast([null, null], 'LargeList(Int64)'), 3),
  array_repeat(arrow_cast([[1, 2], [3, 4]], 'LargeList(List(Int64))'), 2);
----
[[1], [1], [1], [1], [1]] [[1.1, 2.2, 3.3], [1.1, 2.2, 3.3], [1.1, 2.2, 3.3]] [[NULL, NULL], [NULL, NULL], [NULL, NULL]] [[[1, 2], [3, 4]], [[1, 2], [3, 4]]]

# array_repeat scalar function with count of different integer types
query ????????
Select
  array_repeat(1, arrow_cast(2,'Int8')),
  array_repeat(2, arrow_cast(2,'Int16')),
  array_repeat(3, arrow_cast(2,'Int32')),
  array_repeat(4, arrow_cast(2,'Int64')),
  array_repeat(1, arrow_cast(2,'UInt8')),
  array_repeat(2, arrow_cast(2,'UInt16')),
  array_repeat(3, arrow_cast(2,'UInt32')),
  array_repeat(4, arrow_cast(2,'UInt64'));
----
[1, 1] [2, 2] [3, 3] [4, 4] [1, 1] [2, 2] [3, 3] [4, 4]

# array_repeat scalar function with count of negative integer types
query ????
Select
  array_repeat(1, arrow_cast(-2,'Int8')),
  array_repeat(2, arrow_cast(-2,'Int16')),
  array_repeat(3, arrow_cast(-2,'Int32')),
  array_repeat(4, arrow_cast(-2,'Int64'));
----
[] [] [] []

# array_repeat with columns #1

statement ok
CREATE TABLE array_repeat_table
AS VALUES
  (1, 1, 1.1, 'a', make_array(4, 5, 6)),
  (2, null, null, null, null),
  (3, 2, 2.2, 'rust', make_array(7)),
  (0, 3, 3.3, 'datafusion', make_array(8, 9));

statement ok
CREATE TABLE large_array_repeat_table
AS SELECT
  column1,
  column2,
  column3,
  column4,
  arrow_cast(column5, 'LargeList(Int64)') as column5
FROM array_repeat_table;

query ??????
select
  array_repeat(column2, column1),
  array_repeat(column3, column1),
  array_repeat(column4, column1),
  array_repeat(column5, column1),
  array_repeat(column2, 3),
  array_repeat(make_array(1), column1)
from array_repeat_table;
----
[1] [1.1] [a] [[4, 5, 6]] [1, 1, 1] [[1]]
[NULL, NULL] [NULL, NULL] [NULL, NULL] [NULL, NULL] [NULL, NULL, NULL] [[1], [1]]
[2, 2, 2] [2.2, 2.2, 2.2] [rust, rust, rust] [[7], [7], [7]] [2, 2, 2] [[1], [1], [1]]
[] [] [] [] [3, 3, 3] []

query ??????
select
  array_repeat(column2, column1),
  array_repeat(column3, column1),
  array_repeat(column4, column1),
  array_repeat(column5, column1),
  array_repeat(column2, 3),
  array_repeat(make_array(1), column1)
from large_array_repeat_table;
----
[1] [1.1] [a] [[4, 5, 6]] [1, 1, 1] [[1]]
[NULL, NULL] [NULL, NULL] [NULL, NULL] [NULL, NULL] [NULL, NULL, NULL] [[1], [1]]
[2, 2, 2] [2.2, 2.2, 2.2] [rust, rust, rust] [[7], [7], [7]] [2, 2, 2] [[1], [1], [1]]
[] [] [] [] [3, 3, 3] []

statement ok
drop table array_repeat_table;

statement ok
drop table large_array_repeat_table;

## array_concat (aliases: `array_cat`, `list_concat`, `list_cat`)

# test with empty array
query ?
select array_concat([]);
----
[]

# Concatenating strings arrays
query ?
select array_concat(
  ['1', '2'],
  ['3']
);
----
[1, 2, 3]

# Concatenating string arrays
query ?
select array_concat(
  [arrow_cast('1', 'LargeUtf8'), arrow_cast('2', 'LargeUtf8')],
  [arrow_cast('3', 'LargeUtf8')]
);
----
[1, 2, 3]

# Concatenating stringview
query ?
select array_concat(
  [arrow_cast('1', 'Utf8View'), arrow_cast('2', 'Utf8View')],
  [arrow_cast('3', 'Utf8View')]
);
----
[1, 2, 3]

# Concatenating Mixed types (doesn't work)
query error DataFusion error: Error during planning: It is not possible to concatenate arrays of different types\. Expected: List\(Field \{ name: "item", data_type: Utf8, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: \{\} \}\), got: List\(Field \{ name: "item", data_type: LargeUtf8, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: \{\} \}\)
select array_concat(
  [arrow_cast('1', 'Utf8'), arrow_cast('2', 'Utf8')],
  [arrow_cast('3', 'LargeUtf8')]
);

# Concatenating Mixed types (doesn't work)
query error DataFusion error: Error during planning: It is not possible to concatenate arrays of different types\. Expected: List\(Field \{ name: "item", data_type: Utf8, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: \{\} \}\), got: List\(Field \{ name: "item", data_type: Utf8View, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: \{\} \}\)
select array_concat(
  [arrow_cast('1', 'Utf8'), arrow_cast('2', 'Utf8')],
  [arrow_cast('3', 'Utf8View')]
);

# array_concat error
query error DataFusion error: Error during planning: The array_concat function can only accept list as the args\.
select array_concat(1, 2);

# array_concat scalar function #1
query ??
select array_concat(make_array(1, 2, 3), make_array(4, 5, 6), make_array(7, 8, 9)), array_concat(make_array([1], [2]), make_array([3], [4]));
----
[1, 2, 3, 4, 5, 6, 7, 8, 9] [[1], [2], [3], [4]]

# array_concat scalar function #2
query ?
select array_concat(make_array(make_array(1, 2), make_array(3, 4)), make_array(make_array(5, 6), make_array(7, 8)));
----
[[1, 2], [3, 4], [5, 6], [7, 8]]

# array_concat scalar function #3
query ?
select array_concat(make_array([1], [2], [3]), make_array([4], [5], [6]), make_array([7], [8], [9]));
----
[[1], [2], [3], [4], [5], [6], [7], [8], [9]]

# array_concat scalar function #4
query ?
select array_concat(make_array([[1]]), make_array([[2]]));
----
[[[1]], [[2]]]

# array_concat scalar function #5
query ?
select array_concat(make_array(2, 3), make_array());
----
[2, 3]

# array_concat scalar function #6
query ?
select array_concat(make_array(), make_array(2, 3));
----
[2, 3]

# array_concat scalar function #7 (with empty arrays)
query ?
select array_concat(make_array(make_array(1, 2), make_array(3, 4)), make_array(make_array()));
----
[[1, 2], [3, 4], []]

# array_concat scalar function #8 (with empty arrays)
query ?
select array_concat(make_array(make_array(1, 2), make_array(3, 4)), make_array(make_array()), make_array(make_array(), make_array()), make_array(make_array(5, 6), make_array(7, 8)));
----
[[1, 2], [3, 4], [], [], [], [5, 6], [7, 8]]

# array_concat scalar function #9 (with empty arrays)
query ?
select array_concat(make_array(make_array()), make_array(make_array(1, 2), make_array(3, 4)));
----
[[], [1, 2], [3, 4]]

# array_cat scalar function #10 (function alias `array_concat`)
query ??
select array_cat(make_array(1, 2, 3), make_array(4, 5, 6), make_array(7, 8, 9)), array_cat(make_array([1], [2]), make_array([3], [4]));
----
[1, 2, 3, 4, 5, 6, 7, 8, 9] [[1], [2], [3], [4]]

# list_concat scalar function #11 (function alias `array_concat`)
query ??
select list_concat(make_array(1, 2, 3), make_array(4, 5, 6), make_array(7, 8, 9)), list_concat(make_array([1], [2]), make_array([3], [4]));
----
[1, 2, 3, 4, 5, 6, 7, 8, 9] [[1], [2], [3], [4]]

# list_cat scalar function #12 (function alias `array_concat`)
query ??
select list_cat(make_array(1, 2, 3), make_array(4, 5, 6), make_array(7, 8, 9)), list_cat(make_array([1], [2]), make_array([3], [4]));
----
[1, 2, 3, 4, 5, 6, 7, 8, 9] [[1], [2], [3], [4]]

# array_concat with different dimensions #1 (2D + 1D)
query ?
select array_concat(make_array([1,2], [3,4]), make_array(5, 6));
----
[[1, 2], [3, 4], [5, 6]]

# array_concat with different dimensions #2 (1D + 2D)
query ?
select array_concat(make_array(5, 6), make_array([1,2], [3,4]));
----
[[5, 6], [1, 2], [3, 4]]

# array_concat with different dimensions #3 (2D + 1D + 1D)
query ?
select array_concat(make_array([1,2], [3,4]), make_array(5, 6), make_array(7,8));
----
[[1, 2], [3, 4], [5, 6], [7, 8]]

# array_concat with different dimensions #4 (1D + 2D + 3D)
query ?
select array_concat(make_array(10, 20), make_array([30, 40]), make_array([[50, 60]]));
----
[[[10, 20]], [[30, 40]], [[50, 60]]]

# array_concat with different dimensions #5 (2D + 1D + 3D)
query ?
select array_concat(make_array([30, 40]), make_array(10, 20), make_array([[50, 60]]));
----
[[[30, 40]], [[10, 20]], [[50, 60]]]

# array_concat with different dimensions #6 (2D + 1D + 3D + 4D + 3D)
query ?
select array_concat(make_array([30, 40]), make_array(10, 20), make_array([[50, 60]]),  make_array([[[70, 80]]]), make_array([[80, 40]]));
----
[[[[30, 40]]], [[[10, 20]]], [[[50, 60]]], [[[70, 80]]], [[[80, 40]]]]

# array_concat column-wise #1
query ?
select array_concat(column1, make_array(0)) from arrays_values_without_nulls;
----
[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 0]
[11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 0]
[21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 0]
[31, 32, 33, 34, 35, 26, 37, 38, 39, 40, 0]

# array_concat column-wise #2
query ?
select array_concat(column1, column1) from arrays_values_without_nulls;
----
[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
[11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
[21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30]
[31, 32, 33, 34, 35, 26, 37, 38, 39, 40, 31, 32, 33, 34, 35, 26, 37, 38, 39, 40]

# array_concat column-wise #3
query ?
select array_concat(make_array(column2), make_array(column3)) from arrays_values_without_nulls;
----
[1, 1]
[12, 2]
[23, 3]
[34, 4]

# array_concat column-wise #4
query ?
select array_concat(make_array(column2), make_array(0)) from arrays_values;
----
[1, 0]
[12, 0]
[23, 0]
[34, 0]
[44, 0]
[NULL, 0]
[55, 0]
[66, 0]

# array_concat column-wise #5
query ???
select array_concat(column1, column1), array_concat(column2, column2), array_concat(column3, column3) from arrays;
----
[[NULL, 2], [3, NULL], [NULL, 2], [3, NULL]] [1.1, 2.2, 3.3, 1.1, 2.2, 3.3] [L, o, r, e, m, L, o, r, e, m]
[[3, 4], [5, 6], [3, 4], [5, 6]] [NULL, 5.5, 6.6, NULL, 5.5, 6.6] [i, p, NULL, u, m, i, p, NULL, u, m]
[[5, 6], [7, 8], [5, 6], [7, 8]] [7.7, 8.8, 9.9, 7.7, 8.8, 9.9] [d, NULL, l, o, r, d, NULL, l, o, r]
[[7, NULL], [9, 10], [7, NULL], [9, 10]] [10.1, NULL, 12.2, 10.1, NULL, 12.2] [s, i, t, s, i, t]
NULL [13.3, 14.4, 15.5, 13.3, 14.4, 15.5] [a, m, e, t, a, m, e, t]
[[11, 12], [13, 14], [11, 12], [13, 14]] NULL [,, ,]
[[15, 16], [NULL, 18], [15, 16], [NULL, 18]] [16.6, 17.7, 18.8, 16.6, 17.7, 18.8] NULL

# array_concat column-wise #6
query ??
select array_concat(column1, make_array(make_array(1, 2), make_array(3, 4))), array_concat(column2, make_array(1.1, 2.2, 3.3)) from arrays;
----
[[NULL, 2], [3, NULL], [1, 2], [3, 4]] [1.1, 2.2, 3.3, 1.1, 2.2, 3.3]
[[3, 4], [5, 6], [1, 2], [3, 4]] [NULL, 5.5, 6.6, 1.1, 2.2, 3.3]
[[5, 6], [7, 8], [1, 2], [3, 4]] [7.7, 8.8, 9.9, 1.1, 2.2, 3.3]
[[7, NULL], [9, 10], [1, 2], [3, 4]] [10.1, NULL, 12.2, 1.1, 2.2, 3.3]
[[1, 2], [3, 4]] [13.3, 14.4, 15.5, 1.1, 2.2, 3.3]
[[11, 12], [13, 14], [1, 2], [3, 4]] [1.1, 2.2, 3.3]
[[15, 16], [NULL, 18], [1, 2], [3, 4]] [16.6, 17.7, 18.8, 1.1, 2.2, 3.3]

# array_concat column-wise #7
query ?
select array_concat(column3, make_array('.', '.', '.')) from arrays;
----
[L, o, r, e, m, ., ., .]
[i, p, NULL, u, m, ., ., .]
[d, NULL, l, o, r, ., ., .]
[s, i, t, ., ., .]
[a, m, e, t, ., ., .]
[,, ., ., .]
[., ., .]

# query ??I?
# select column1, column2, column3, column4 from arrays_values_v2;
# ----
# [NULL, 2, 3] [4, 5, NULL] 12 [[30, 40, 50]]
# NULL [7, NULL, 8] 13 [[NULL, NULL, 60]]
# [9, NULL, 10] NULL 14 [[70, NULL, NULL]]
# [NULL, 1] [NULL, 21] NULL NULL
# [11, 12] NULL NULL NULL
# NULL NULL NULL NULL


# array_concat column-wise #8 (1D + 1D)
query ?
select array_concat(column1, column2) from arrays_values_v2;
----
[NULL, 2, 3, 4, 5, NULL]
[7, NULL, 8]
[9, NULL, 10]
[NULL, 1, NULL, 21]
[11, 12]
NULL

# array_concat column-wise #9 (2D + 1D)
query ?
select array_concat(column4, make_array(column3)) from arrays_values_v2;
----
[[30, 40, 50], [12]]
[[NULL, NULL, 60], [13]]
[[70, NULL, NULL], [14]]
[[NULL]]
[[NULL]]
[[NULL]]

# array_concat column-wise #10 (3D + 2D + 1D)
query ?
select array_concat(column4, column1, column2) from nested_arrays;
----
[[[1, 2, 3], [4, 5, 6]], [[7, 8, 9], [10, 11, 12]], [[1, 2, 3], [2, 9, 1], [7, 8, 9], [1, 2, 3], [1, 7, 4], [4, 5, 6]], [[7, 8, 9]]]
[[[11, 12, 13], [14, 15, 16]], [[17, 18, 19], [20, 21, 22]], [[4, 5, 6], [10, 11, 12], [4, 9, 8], [7, 8, 9], [10, 11, 12], [1, 8, 7]], [[10, 11, 12]]]

# array_concat column-wise #11 (2D + 1D)
query ?
select array_concat(column4, column1) from arrays_values_v2;
----
[[30, 40, 50], [NULL, 2, 3]]
[[NULL, NULL, 60], NULL]
[[70, NULL, NULL], [9, NULL, 10]]
[[NULL, 1]]
[[11, 12]]
[NULL]

# array_concat column-wise #12 (1D + 1D + 1D)
query ?
select array_concat(make_array(column3), column1, column2) from arrays_values_v2;
----
[12, NULL, 2, 3, 4, 5, NULL]
[13, 7, NULL, 8]
[14, 9, NULL, 10]
[NULL, NULL, 1, NULL, 21]
[NULL, 11, 12]
[NULL]

## array_position (aliases: `list_position`, `array_indexof`, `list_indexof`)

## array_position with NULL (follow PostgreSQL)
#query I
#select array_position([1, 2, 3, 4, 5], null), array_position(NULL, 1);
#----
#NULL NULL

# array_position scalar function #1
query III
select array_position(['h', 'e', 'l', 'l', 'o'], 'l'), array_position([1, 2, 3, 4, 5], 5), array_position([1, 1, 1], 1);
----
3 5 1

query III
select array_position(arrow_cast(['h', 'e', 'l', 'l', 'o'], 'LargeList(Utf8)'), 'l'), array_position(arrow_cast([1, 2, 3, 4, 5], 'LargeList(Int64)'), 5), array_position(arrow_cast([1, 1, 1], 'LargeList(Int64)'), 1);
----
3 5 1

# array_position scalar function #2 (with optional argument)
query III
select array_position(['h', 'e', 'l', 'l', 'o'], 'l', 4), array_position([1, 2, 5, 4, 5], 5, 4), array_position([1, 1, 1], 1, 2);
----
4 5 2

query III
select array_position(arrow_cast(['h', 'e', 'l', 'l', 'o'], 'LargeList(Utf8)'), 'l', 4), array_position(arrow_cast([1, 2, 3, 4, 5], 'LargeList(Int64)'), 5, 4), array_position(arrow_cast([1, 1, 1], 'LargeList(Int64)'), 1, 2);
----
4 5 2

# array_position scalar function #3 (element is list)
query II
select array_position(make_array([1, 2, 3], [4, 5, 6], [5, 5, 5], [4, 5, 6], [7, 8, 9]), [4, 5, 6]), array_position(make_array([1, 3, 2], [2, 3, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]), [2, 3, 4]);
----
2 2

# array_position scalar function #4 (element in list; with optional argument)
query II
select array_position(make_array([1, 2, 3], [4, 5, 6], [5, 5, 5], [4, 5, 6], [7, 8, 9]), [4, 5, 6], 3), array_position(make_array([1, 3, 2], [2, 3, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]), [2, 3, 4], 3);
----
4 3

query II
select array_position(arrow_cast(make_array([1, 2, 3], [4, 5, 6], [5, 5, 5], [4, 5, 6], [7, 8, 9]), 'LargeList(List(Int64))'), [4, 5, 6]), array_position(arrow_cast(make_array([1, 3, 2], [2, 3, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]), 'LargeList(List(Int64))'), [2, 3, 4]);
----
2 2

query I
SELECT array_position(arrow_cast([5, 2, 3, 4, 5], 'List(Int32)'), 5)
----
1

query I
SELECT array_position(arrow_cast([5, 2, 3, 4, 5], 'List(Int32)'), 5, 2)
----
5

query I
SELECT array_position(arrow_cast([1, 1, 100, 1, 1], 'LargeList(Int32)'), 100)
----
3

query I
SELECT array_position([1, 2, 3], 'foo')
----
NULL

query I
SELECT array_position([1, 2, 3], 'foo', 2)
----
NULL

# list_position scalar function #5 (function alias `array_position`)
query III
select list_position(['h', 'e', 'l', 'l', 'o'], 'l'), list_position([1, 2, 3, 4, 5], 5), list_position([1, 1, 1], 1);
----
3 5 1

query III
select list_position(arrow_cast(['h', 'e', 'l', 'l', 'o'], 'LargeList(Utf8)'), 'l'), list_position(arrow_cast([1, 2, 3, 4, 5], 'LargeList(Int64)'), 5), list_position(arrow_cast([1, 1, 1], 'LargeList(Int64)'), 1);
----
3 5 1

# array_indexof scalar function #6 (function alias `array_position`)
query III
select array_indexof(['h', 'e', 'l', 'l', 'o'], 'l'), array_indexof([1, 2, 3, 4, 5], 5), array_indexof([1, 1, 1], 1);
----
3 5 1

query III
select array_indexof(arrow_cast(['h', 'e', 'l', 'l', 'o'], 'LargeList(Utf8)'), 'l'), array_indexof(arrow_cast([1, 2, 3, 4, 5], 'LargeList(Int64)'), 5), array_indexof(arrow_cast([1, 1, 1], 'LargeList(Int64)'), 1);
----
3 5 1

# list_indexof scalar function #7 (function alias `array_position`)
query III
select list_indexof(['h', 'e', 'l', 'l', 'o'], 'l'), list_indexof([1, 2, 3, 4, 5], 5), list_indexof([1, 1, 1], 1);
----
3 5 1

query III
select list_indexof(arrow_cast(['h', 'e', 'l', 'l', 'o'], 'LargeList(Utf8)'), 'l'), list_indexof(arrow_cast([1, 2, 3, 4, 5], 'LargeList(Int64)'), 5), list_indexof(arrow_cast([1, 1, 1], 'LargeList(Int64)'), 1);
----
3 5 1

# array_position with columns #1
query II
select array_position(column1, column2), array_position(column1, column2, column3) from arrays_values_without_nulls;
----
1 1
2 2
3 3
4 4

query II
select array_position(column1, column2), array_position(column1, column2, column3) from large_arrays_values_without_nulls;
----
1 1
2 2
3 3
4 4

# array_position with columns #2 (element is list)
query II
select array_position(column1, column2), array_position(column1, column2, column3) from nested_arrays;
----
3 3
2 5

query II
select array_position(column1, column2), array_position(column1, column2, column3) from nested_arrays;
----
3 3
2 5

# array_position with columns and scalars #1
query III
select array_position(make_array(1, 2, 3, 4, 5), column2), array_position(column1, 3), array_position(column1, 3, 5) from arrays_values_without_nulls;
----
1 3 NULL
NULL NULL NULL
NULL NULL NULL
NULL NULL NULL

query III
select array_position(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), column2), array_position(column1, 3), array_position(column1, 3, 5) from large_arrays_values_without_nulls;
----
1 3 NULL
NULL NULL NULL
NULL NULL NULL
NULL NULL NULL

# array_position with columns and scalars #2 (element is list)
query III
select array_position(make_array([1, 2, 3], [4, 5, 6], [11, 12, 13]), column2), array_position(column1, make_array(4, 5, 6)), array_position(column1, make_array(1, 2, 3), 2) from nested_arrays;
----
NULL 6 4
NULL 1 NULL

query III
select array_position(arrow_cast(make_array([1, 2, 3], [4, 5, 6], [11, 12, 13]), 'LargeList(LargeList(Int64))'), column2), array_position(column1, arrow_cast(make_array(4, 5, 6), 'LargeList(Int64)')), array_position(column1, arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)'), 2) from large_nested_arrays;
----
NULL 6 4
NULL 1 NULL

## array_positions (aliases: `list_positions`)

query ?
select array_positions([1, 2, 3, 4, 5], null);
----
[]

#TODO: https://github.com/apache/datafusion/issues/7142
# array_positions with NULL (follow PostgreSQL)
#query ?
#select array_positions(null, 1);
#----
#NULL

# array_positions scalar function #1
query ???
select array_positions(['h', 'e', 'l', 'l', 'o'], 'l'), array_positions([1, 2, 3, 4, 5], 5), array_positions([1, 1, 1], 1);
----
[3, 4] [5] [1, 2, 3]

query ???
select array_positions(arrow_cast(['h', 'e', 'l', 'l', 'o'], 'LargeList(Utf8)'), 'l'), array_positions(arrow_cast([1, 2, 3, 4, 5], 'LargeList(Int64)'), 5), array_positions(arrow_cast([1, 1, 1], 'LargeList(Int64)'), 1);
----
[3, 4] [5] [1, 2, 3]

query ???
select array_positions(arrow_cast(['h', 'e', 'l', 'l', 'o'], 'FixedSizeList(5, Utf8)'), 'l'), array_positions(arrow_cast([1, 2, 3, 4, 5], 'FixedSizeList(5, Int64)'), 5), array_positions(arrow_cast([1, 1, 1], 'FixedSizeList(3, Int64)'), 1);
----
[3, 4] [5] [1, 2, 3]

# array_positions scalar function #2 (element is list)
query ?
select array_positions(make_array([1, 2, 3], [2, 1, 3], [1, 5, 6], [2, 1, 3], [4, 5, 6]), [2, 1, 3]);
----
[2, 4]

query ?
select array_positions(arrow_cast(make_array([1, 2, 3], [2, 1, 3], [1, 5, 6], [2, 1, 3], [4, 5, 6]), 'LargeList(List(Int64))'), [2, 1, 3]);
----
[2, 4]

query ?
select array_positions(arrow_cast(make_array([1, 2, 3], [2, 1, 3], [1, 5, 6], [2, 1, 3], [4, 5, 6]), 'FixedSizeList(5, List(Int64))'), [2, 1, 3]);
----
[2, 4]

# list_positions scalar function #3 (function alias `array_positions`)
query ???
select list_positions(['h', 'e', 'l', 'l', 'o'], 'l'), list_positions([1, 2, 3, 4, 5], 5), list_positions([1, 1, 1], 1);
----
[3, 4] [5] [1, 2, 3]

query ???
select list_positions(arrow_cast(['h', 'e', 'l', 'l', 'o'], 'LargeList(Utf8)'), 'l'), list_positions(arrow_cast([1, 2, 3, 4, 5], 'LargeList(Int64)'), 5), list_positions(arrow_cast([1, 1, 1], 'LargeList(Int64)'), 1);
----
[3, 4] [5] [1, 2, 3]

query ???
select list_positions(arrow_cast(['h', 'e', 'l', 'l', 'o'], 'FixedSizeList(5, Utf8)'), 'l'),
       list_positions(arrow_cast([1, 2, 3, 4, 5], 'FixedSizeList(5, Int64)'), 5),
       list_positions(arrow_cast([1, 1, 1], 'FixedSizeList(3, Int64)'), 1);
----
[3, 4] [5] [1, 2, 3]

# array_positions with columns #1
query ?
select array_positions(column1, column2) from arrays_values_without_nulls;
----
[1]
[2]
[3]
[4]

query ?
select array_positions(arrow_cast(column1, 'LargeList(Int64)'), column2) from arrays_values_without_nulls;
----
[1]
[2]
[3]
[4]

query ?
select array_positions(arrow_cast(column1, 'LargeList(Int64)'), column2) from fixed_size_arrays_values_without_nulls;
----
[1]
[2]
[3]
[4]

# array_positions with columns #2 (element is list)
query ?
select array_positions(column1, column2) from nested_arrays;
----
[3]
[2, 5]

query ?
select array_positions(arrow_cast(column1, 'LargeList(List(Int64))'), column2) from nested_arrays;
----
[3]
[2, 5]

query ?
select array_positions(column1, column2) from fixed_size_nested_arrays;
----
[3]
[2, 5]

# array_positions with columns and scalars #1
query ??
select array_positions(column1, 4), array_positions(array[1, 2, 23, 13, 33, 45], column2) from arrays_values_without_nulls;
----
[4] [1]
[] []
[] [3]
[] []

query ??
select array_positions(arrow_cast(column1, 'LargeList(Int64)'), 4), array_positions(array[1, 2, 23, 13, 33, 45], column2) from arrays_values_without_nulls;
----
[4] [1]
[] []
[] [3]
[] []

query ??
select array_positions(column1, 4), array_positions(array[1, 2, 23, 13, 33, 45], column2) from fixed_size_arrays_values_without_nulls;
----
[4] [1]
[] []
[] [3]
[] []

# array_positions with columns and scalars #2 (element is list)
query ??
select array_positions(column1, make_array(4, 5, 6)), array_positions(make_array([1, 2, 3], [11, 12, 13], [4, 5, 6]), column2) from nested_arrays;
----
[6] []
[1] []

query ??
select array_positions(arrow_cast(column1, 'LargeList(List(Int64))'), make_array(4, 5, 6)), array_positions(arrow_cast(make_array([1, 2, 3], [11, 12, 13], [4, 5, 6]), 'LargeList(List(Int64))'), column2) from nested_arrays;
----
[6] []
[1] []

query ??
select array_positions(column1, make_array(4, 5, 6)), array_positions(make_array([1, 2, 3], [11, 12, 13], [4, 5, 6]), column2) from fixed_size_nested_arrays;
----
[6] []
[1] []

## array_replace (aliases: `list_replace`)

# array_replace scalar function #1
query ???
select
  array_replace(make_array(1, 2, 3, 4), 2, 3),
  array_replace(make_array(1, 4, 4, 5, 4, 6, 7), 4, 0),
  array_replace(make_array(1, 2, 3), 4, 0);
----
[1, 3, 3, 4] [1, 0, 4, 5, 4, 6, 7] [1, 2, 3]

query ???
select
  array_replace(arrow_cast(make_array(1, 2, 3, 4), 'LargeList(Int64)'), 2, 3),
  array_replace(arrow_cast(make_array(1, 4, 4, 5, 4, 6, 7), 'LargeList(Int64)'), 4, 0),
  array_replace(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)'), 4, 0);
----
[1, 3, 3, 4] [1, 0, 4, 5, 4, 6, 7] [1, 2, 3]

# array_replace scalar function #2 (element is list)
query ??
select
  array_replace(
    make_array([1, 2, 3], [4, 5, 6], [5, 5, 5], [4, 5, 6], [7, 8, 9]),
    [4, 5, 6],
    [1, 1, 1]
  ),
  array_replace(
    make_array([1, 3, 2], [2, 3, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]),
    [2, 3, 4],
    [3, 1, 4]
  );
----
[[1, 2, 3], [1, 1, 1], [5, 5, 5], [4, 5, 6], [7, 8, 9]] [[1, 3, 2], [3, 1, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]]

query ??
select
  array_replace(
    arrow_cast(make_array([1, 2, 3], [4, 5, 6], [5, 5, 5], [4, 5, 6], [7, 8, 9]), 'LargeList(List(Int64))'),
    [4, 5, 6],
    [1, 1, 1]
  ),
  array_replace(
    arrow_cast(make_array([1, 3, 2], [2, 3, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]), 'LargeList(List(Int64))'),
    [2, 3, 4],
    [3, 1, 4]
  );
----
[[1, 2, 3], [1, 1, 1], [5, 5, 5], [4, 5, 6], [7, 8, 9]] [[1, 3, 2], [3, 1, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]]

# list_replace scalar function #3 (function alias `list_replace`)
query ???
select list_replace(
  make_array(1, 2, 3, 4), 2, 3),
  list_replace(make_array(1, 4, 4, 5, 4, 6, 7), 4, 0),
  list_replace(make_array(1, 2, 3), 4, 0);
----
[1, 3, 3, 4] [1, 0, 4, 5, 4, 6, 7] [1, 2, 3]

query ???
select list_replace(
  arrow_cast(make_array(1, 2, 3, 4), 'LargeList(Int64)'), 2, 3),
  list_replace(arrow_cast(make_array(1, 4, 4, 5, 4, 6, 7), 'LargeList(Int64)'), 4, 0),
  list_replace(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)'), 4, 0);
----
[1, 3, 3, 4] [1, 0, 4, 5, 4, 6, 7] [1, 2, 3]

# array_replace scalar function #4 (null input)
query ?
select array_replace(make_array(1, 2, 3, 4, 5), NULL, NULL);
----
[1, 2, 3, 4, 5]

query ?
select array_replace(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), NULL, NULL);
----
[1, 2, 3, 4, 5]

# array_replace scalar function with columns #1
query ?
select array_replace(column1, column2, column3) from arrays_with_repeating_elements;
----
[1, 4, 1, 3, 2, 2, 1, 3, 2, 3]
[7, 4, 5, 5, 6, 5, 5, 5, 4, 4]
[10, 7, 7, 8, 7, 9, 7, 8, 7, 7]
[13, 11, 12, 10, 11, 12, 10, 11, 12, 10]

query ?
select array_replace(column1, column2, column3) from large_arrays_with_repeating_elements;
----
[1, 4, 1, 3, 2, 2, 1, 3, 2, 3]
[7, 4, 5, 5, 6, 5, 5, 5, 4, 4]
[10, 7, 7, 8, 7, 9, 7, 8, 7, 7]
[13, 11, 12, 10, 11, 12, 10, 11, 12, 10]

# array_replace scalar function with columns #2 (element is list)
query ?
select array_replace(column1, column2, column3) from nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [10, 11, 12], [1, 2, 3], [7, 8, 9], [4, 5, 6], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]]
[[19, 20, 21], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]]
[[28, 29, 30], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]]
[[37, 38, 39], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]]

query ?
select array_replace(column1, column2, column3) from large_nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [10, 11, 12], [1, 2, 3], [7, 8, 9], [4, 5, 6], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]]
[[19, 20, 21], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]]
[[28, 29, 30], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]]
[[37, 38, 39], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]]

# array_replace scalar function with columns and scalars #1
query ???
select
  array_replace(make_array(1, 2, 2, 4, 5, 4, 4, 7, 7, 10, 7, 8), column2, column3),
  array_replace(column1, 1, column3),
  array_replace(column1, column2, 4)
from arrays_with_repeating_elements;
----
[1, 4, 2, 4, 5, 4, 4, 7, 7, 10, 7, 8] [4, 2, 1, 3, 2, 2, 1, 3, 2, 3] [1, 4, 1, 3, 2, 2, 1, 3, 2, 3]
[1, 2, 2, 7, 5, 4, 4, 7, 7, 10, 7, 8] [4, 4, 5, 5, 6, 5, 5, 5, 4, 4] [4, 4, 5, 5, 6, 5, 5, 5, 4, 4]
[1, 2, 2, 4, 5, 4, 4, 10, 7, 10, 7, 8] [7, 7, 7, 8, 7, 9, 7, 8, 7, 7] [4, 7, 7, 8, 7, 9, 7, 8, 7, 7]
[1, 2, 2, 4, 5, 4, 4, 7, 7, 13, 7, 8] [10, 11, 12, 10, 11, 12, 10, 11, 12, 10] [4, 11, 12, 10, 11, 12, 10, 11, 12, 10]

query ???
select
  array_replace(arrow_cast(make_array(1, 2, 2, 4, 5, 4, 4, 7, 7, 10, 7, 8), 'LargeList(Int64)'), column2, column3),
  array_replace(column1, 1, column3),
  array_replace(column1, column2, 4)
from large_arrays_with_repeating_elements;
----
[1, 4, 2, 4, 5, 4, 4, 7, 7, 10, 7, 8] [4, 2, 1, 3, 2, 2, 1, 3, 2, 3] [1, 4, 1, 3, 2, 2, 1, 3, 2, 3]
[1, 2, 2, 7, 5, 4, 4, 7, 7, 10, 7, 8] [4, 4, 5, 5, 6, 5, 5, 5, 4, 4] [4, 4, 5, 5, 6, 5, 5, 5, 4, 4]
[1, 2, 2, 4, 5, 4, 4, 10, 7, 10, 7, 8] [7, 7, 7, 8, 7, 9, 7, 8, 7, 7] [4, 7, 7, 8, 7, 9, 7, 8, 7, 7]
[1, 2, 2, 4, 5, 4, 4, 7, 7, 13, 7, 8] [10, 11, 12, 10, 11, 12, 10, 11, 12, 10] [4, 11, 12, 10, 11, 12, 10, 11, 12, 10]

# array_replace scalar function with columns and scalars #2 (element is list)
query ???
select
  array_replace(
    make_array(
      [1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]),
      column2,
      column3
    ),
  array_replace(column1, make_array(1, 2, 3), column3),
  array_replace(column1, column2, make_array(11, 12, 13))
from nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [10, 11, 12], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[10, 11, 12], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]] [[1, 2, 3], [11, 12, 13], [1, 2, 3], [7, 8, 9], [4, 5, 6], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [19, 20, 21], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[10, 11, 12], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]] [[11, 12, 13], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [28, 29, 30], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]] [[11, 12, 13], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [37, 38, 39], [19, 20, 21], [22, 23, 24]] [[28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]] [[11, 12, 13], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]]

query ???
select
  array_replace(
    arrow_cast(make_array(
      [1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]),'LargeList(List(Int64))'),
      column2,
      column3
    ),
  array_replace(column1, make_array(1, 2, 3), column3),
  array_replace(column1, column2, make_array(11, 12, 13))
from large_nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [10, 11, 12], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[10, 11, 12], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]] [[1, 2, 3], [11, 12, 13], [1, 2, 3], [7, 8, 9], [4, 5, 6], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [19, 20, 21], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[10, 11, 12], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]] [[11, 12, 13], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [28, 29, 30], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]] [[11, 12, 13], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [37, 38, 39], [19, 20, 21], [22, 23, 24]] [[28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]] [[11, 12, 13], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]]

## array_replace_n (aliases: `list_replace_n`)

# array_replace_n scalar function #1
query ???
select
  array_replace_n(make_array(1, 2, 3, 4), 2, 3, 2),
  array_replace_n(make_array(1, 4, 4, 5, 4, 6, 7), 4, 0, 2),
  array_replace_n(make_array(1, 2, 3), 4, 0, 3);
----
[1, 3, 3, 4] [1, 0, 0, 5, 4, 6, 7] [1, 2, 3]

query ???
select
  array_replace_n(arrow_cast(make_array(1, 2, 3, 4), 'LargeList(Int64)'), 2, 3, 2),
  array_replace_n(arrow_cast(make_array(1, 4, 4, 5, 4, 6, 7), 'LargeList(Int64)'), 4, 0, 2),
  array_replace_n(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)'), 4, 0, 3);
----
[1, 3, 3, 4] [1, 0, 0, 5, 4, 6, 7] [1, 2, 3]

# array_replace_n scalar function #2 (element is list)
query ??
select
  array_replace_n(
    make_array([1, 2, 3], [4, 5, 6], [5, 5, 5], [4, 5, 6], [7, 8, 9]),
      [4, 5, 6],
      [1, 1, 1],
      2
    ),
  array_replace_n(
    make_array([1, 3, 2], [2, 3, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]),
    [2, 3, 4],
    [3, 1, 4],
    2
  );
----
[[1, 2, 3], [1, 1, 1], [5, 5, 5], [1, 1, 1], [7, 8, 9]] [[1, 3, 2], [3, 1, 4], [3, 1, 4], [5, 3, 1], [1, 3, 2]]

query ??
select
  array_replace_n(
    arrow_cast(make_array([1, 2, 3], [4, 5, 6], [5, 5, 5], [4, 5, 6], [7, 8, 9]), 'LargeList(List(Int64))'),
      [4, 5, 6],
      [1, 1, 1],
      2
    ),
  array_replace_n(
    arrow_cast(make_array([1, 3, 2], [2, 3, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]), 'LargeList(List(Int64))'),
    [2, 3, 4],
    [3, 1, 4],
    2
  );
----
[[1, 2, 3], [1, 1, 1], [5, 5, 5], [1, 1, 1], [7, 8, 9]] [[1, 3, 2], [3, 1, 4], [3, 1, 4], [5, 3, 1], [1, 3, 2]]

# list_replace_n scalar function #3 (function alias `array_replace_n`)
query ???
select
  list_replace_n(make_array(1, 2, 3, 4), 2, 3, 2),
  list_replace_n(make_array(1, 4, 4, 5, 4, 6, 7), 4, 0, 2),
  list_replace_n(make_array(1, 2, 3), 4, 0, 3);
----
[1, 3, 3, 4] [1, 0, 0, 5, 4, 6, 7] [1, 2, 3]

query ???
select
  list_replace_n(arrow_cast(make_array(1, 2, 3, 4), 'LargeList(Int64)'), 2, 3, 2),
  list_replace_n(arrow_cast(make_array(1, 4, 4, 5, 4, 6, 7), 'LargeList(Int64)'), 4, 0, 2),
  list_replace_n(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)'), 4, 0, 3);
----
[1, 3, 3, 4] [1, 0, 0, 5, 4, 6, 7] [1, 2, 3]

# array_replace_n scalar function #4 (null input)
query ?
select array_replace_n(make_array(1, 2, 3, 4, 5), NULL, NULL, NULL);
----
[1, 2, 3, 4, 5]

query ?
select array_replace_n(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), NULL, NULL, NULL);
----
[1, 2, 3, 4, 5]

# array_replace_n scalar function with columns #1
query ?
select
  array_replace_n(column1, column2, column3, column4)
from arrays_with_repeating_elements;
----
[1, 4, 1, 3, 4, 4, 1, 3, 2, 3]
[7, 7, 5, 5, 6, 5, 5, 5, 4, 4]
[10, 10, 10, 8, 10, 9, 10, 8, 7, 7]
[13, 11, 12, 13, 11, 12, 13, 11, 12, 13]

query ?
select
  array_replace_n(column1, column2, column3, column4)
from large_arrays_with_repeating_elements;
----
[1, 4, 1, 3, 4, 4, 1, 3, 2, 3]
[7, 7, 5, 5, 6, 5, 5, 5, 4, 4]
[10, 10, 10, 8, 10, 9, 10, 8, 7, 7]
[13, 11, 12, 13, 11, 12, 13, 11, 12, 13]

# array_replace_n scalar function with columns #2 (element is list)
query ?
select
  array_replace_n(column1, column2, column3, column4)
from nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [10, 11, 12], [1, 2, 3], [7, 8, 9], [10, 11, 12], [10, 11, 12], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]]
[[19, 20, 21], [19, 20, 21], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]]
[[28, 29, 30], [28, 29, 30], [28, 29, 30], [22, 23, 24], [28, 29, 30], [25, 26, 27], [28, 29, 30], [22, 23, 24], [19, 20, 21], [19, 20, 21]]
[[37, 38, 39], [31, 32, 33], [34, 35, 36], [37, 38, 39], [31, 32, 33], [34, 35, 36], [37, 38, 39], [31, 32, 33], [34, 35, 36], [37, 38, 39]]

query ?
select
  array_replace_n(column1, column2, column3, column4)
from large_nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [10, 11, 12], [1, 2, 3], [7, 8, 9], [10, 11, 12], [10, 11, 12], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]]
[[19, 20, 21], [19, 20, 21], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]]
[[28, 29, 30], [28, 29, 30], [28, 29, 30], [22, 23, 24], [28, 29, 30], [25, 26, 27], [28, 29, 30], [22, 23, 24], [19, 20, 21], [19, 20, 21]]
[[37, 38, 39], [31, 32, 33], [34, 35, 36], [37, 38, 39], [31, 32, 33], [34, 35, 36], [37, 38, 39], [31, 32, 33], [34, 35, 36], [37, 38, 39]]


# array_replace_n scalar function with columns and scalars #1
query ????
select
  array_replace_n(make_array(1, 2, 2, 4, 5, 4, 4, 7, 7, 10, 7, 8), column2, column3, column4),
  array_replace_n(column1, 1, column3, column4),
  array_replace_n(column1, column2, 4, column4),
  array_replace_n(column1, column2, column3, 2)
from arrays_with_repeating_elements;
----
[1, 4, 4, 4, 5, 4, 4, 7, 7, 10, 7, 8] [4, 2, 4, 3, 2, 2, 4, 3, 2, 3] [1, 4, 1, 3, 4, 4, 1, 3, 2, 3] [1, 4, 1, 3, 4, 2, 1, 3, 2, 3]
[1, 2, 2, 7, 5, 7, 4, 7, 7, 10, 7, 8] [4, 4, 5, 5, 6, 5, 5, 5, 4, 4] [4, 4, 5, 5, 6, 5, 5, 5, 4, 4] [7, 7, 5, 5, 6, 5, 5, 5, 4, 4]
[1, 2, 2, 4, 5, 4, 4, 10, 10, 10, 10, 8] [7, 7, 7, 8, 7, 9, 7, 8, 7, 7] [4, 4, 4, 8, 4, 9, 4, 8, 7, 7] [10, 10, 7, 8, 7, 9, 7, 8, 7, 7]
[1, 2, 2, 4, 5, 4, 4, 7, 7, 13, 7, 8] [10, 11, 12, 10, 11, 12, 10, 11, 12, 10] [4, 11, 12, 4, 11, 12, 4, 11, 12, 4] [13, 11, 12, 13, 11, 12, 10, 11, 12, 10]

query ????
select
  array_replace_n(arrow_cast(make_array(1, 2, 2, 4, 5, 4, 4, 7, 7, 10, 7, 8), 'LargeList(Int64)'), column2, column3, column4),
  array_replace_n(column1, 1, column3, column4),
  array_replace_n(column1, column2, 4, column4),
  array_replace_n(column1, column2, column3, 2)
from large_arrays_with_repeating_elements;
----
[1, 4, 4, 4, 5, 4, 4, 7, 7, 10, 7, 8] [4, 2, 4, 3, 2, 2, 4, 3, 2, 3] [1, 4, 1, 3, 4, 4, 1, 3, 2, 3] [1, 4, 1, 3, 4, 2, 1, 3, 2, 3]
[1, 2, 2, 7, 5, 7, 4, 7, 7, 10, 7, 8] [4, 4, 5, 5, 6, 5, 5, 5, 4, 4] [4, 4, 5, 5, 6, 5, 5, 5, 4, 4] [7, 7, 5, 5, 6, 5, 5, 5, 4, 4]
[1, 2, 2, 4, 5, 4, 4, 10, 10, 10, 10, 8] [7, 7, 7, 8, 7, 9, 7, 8, 7, 7] [4, 4, 4, 8, 4, 9, 4, 8, 7, 7] [10, 10, 7, 8, 7, 9, 7, 8, 7, 7]
[1, 2, 2, 4, 5, 4, 4, 7, 7, 13, 7, 8] [10, 11, 12, 10, 11, 12, 10, 11, 12, 10] [4, 11, 12, 4, 11, 12, 4, 11, 12, 4] [13, 11, 12, 13, 11, 12, 10, 11, 12, 10]

# array_replace_n scalar function with columns and scalars #2 (element is list)
query ????
select
  array_replace_n(
    make_array(
      [7, 8, 9], [2, 1, 3], [1, 5, 6], [10, 11, 12], [2, 1, 3], [7, 8, 9], [4, 5, 6]),
      column2,
      column3,
      column4
    ),
    array_replace_n(column1, make_array(1, 2, 3), column3, column4),
    array_replace_n(column1, column2, make_array(11, 12, 13), column4),
    array_replace_n(column1, column2, column3, 2)
from nested_arrays_with_repeating_elements;
----
[[7, 8, 9], [2, 1, 3], [1, 5, 6], [10, 11, 12], [2, 1, 3], [7, 8, 9], [10, 11, 12]] [[10, 11, 12], [4, 5, 6], [10, 11, 12], [7, 8, 9], [4, 5, 6], [4, 5, 6], [10, 11, 12], [7, 8, 9], [4, 5, 6], [7, 8, 9]] [[1, 2, 3], [11, 12, 13], [1, 2, 3], [7, 8, 9], [11, 12, 13], [11, 12, 13], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]] [[1, 2, 3], [10, 11, 12], [1, 2, 3], [7, 8, 9], [10, 11, 12], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]]
[[7, 8, 9], [2, 1, 3], [1, 5, 6], [19, 20, 21], [2, 1, 3], [7, 8, 9], [4, 5, 6]] [[10, 11, 12], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]] [[11, 12, 13], [11, 12, 13], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]] [[19, 20, 21], [19, 20, 21], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]]
[[7, 8, 9], [2, 1, 3], [1, 5, 6], [10, 11, 12], [2, 1, 3], [7, 8, 9], [4, 5, 6]] [[19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]] [[11, 12, 13], [11, 12, 13], [11, 12, 13], [22, 23, 24], [11, 12, 13], [25, 26, 27], [11, 12, 13], [22, 23, 24], [19, 20, 21], [19, 20, 21]] [[28, 29, 30], [28, 29, 30], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]]
[[7, 8, 9], [2, 1, 3], [1, 5, 6], [10, 11, 12], [2, 1, 3], [7, 8, 9], [4, 5, 6]] [[28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]] [[11, 12, 13], [31, 32, 33], [34, 35, 36], [11, 12, 13], [31, 32, 33], [34, 35, 36], [11, 12, 13], [31, 32, 33], [34, 35, 36], [11, 12, 13]] [[37, 38, 39], [31, 32, 33], [34, 35, 36], [37, 38, 39], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]]

query ????
select
  array_replace_n(
    arrow_cast(make_array(
      [7, 8, 9], [2, 1, 3], [1, 5, 6], [10, 11, 12], [2, 1, 3], [7, 8, 9], [4, 5, 6]), 'LargeList(List(Int64))'),
      column2,
      column3,
      column4
    ),
    array_replace_n(column1, make_array(1, 2, 3), column3, column4),
    array_replace_n(column1, column2, make_array(11, 12, 13), column4),
    array_replace_n(column1, column2, column3, 2)
from large_nested_arrays_with_repeating_elements;
----
[[7, 8, 9], [2, 1, 3], [1, 5, 6], [10, 11, 12], [2, 1, 3], [7, 8, 9], [10, 11, 12]] [[10, 11, 12], [4, 5, 6], [10, 11, 12], [7, 8, 9], [4, 5, 6], [4, 5, 6], [10, 11, 12], [7, 8, 9], [4, 5, 6], [7, 8, 9]] [[1, 2, 3], [11, 12, 13], [1, 2, 3], [7, 8, 9], [11, 12, 13], [11, 12, 13], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]] [[1, 2, 3], [10, 11, 12], [1, 2, 3], [7, 8, 9], [10, 11, 12], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]]
[[7, 8, 9], [2, 1, 3], [1, 5, 6], [19, 20, 21], [2, 1, 3], [7, 8, 9], [4, 5, 6]] [[10, 11, 12], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]] [[11, 12, 13], [11, 12, 13], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]] [[19, 20, 21], [19, 20, 21], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]]
[[7, 8, 9], [2, 1, 3], [1, 5, 6], [10, 11, 12], [2, 1, 3], [7, 8, 9], [4, 5, 6]] [[19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]] [[11, 12, 13], [11, 12, 13], [11, 12, 13], [22, 23, 24], [11, 12, 13], [25, 26, 27], [11, 12, 13], [22, 23, 24], [19, 20, 21], [19, 20, 21]] [[28, 29, 30], [28, 29, 30], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]]
[[7, 8, 9], [2, 1, 3], [1, 5, 6], [10, 11, 12], [2, 1, 3], [7, 8, 9], [4, 5, 6]] [[28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]] [[11, 12, 13], [31, 32, 33], [34, 35, 36], [11, 12, 13], [31, 32, 33], [34, 35, 36], [11, 12, 13], [31, 32, 33], [34, 35, 36], [11, 12, 13]] [[37, 38, 39], [31, 32, 33], [34, 35, 36], [37, 38, 39], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]]

## array_replace_all (aliases: `list_replace_all`)

# array_replace_all scalar function #1
query ???
select
  array_replace_all(make_array(1, 2, 3, 4), 2, 3),
  array_replace_all(make_array(1, 4, 4, 5, 4, 6, 7), 4, 0),
  array_replace_all(make_array(1, 2, 3), 4, 0);
----
[1, 3, 3, 4] [1, 0, 0, 5, 0, 6, 7] [1, 2, 3]

query ???
select
  array_replace_all(arrow_cast(make_array(1, 2, 3, 4), 'LargeList(Int64)'), 2, 3),
  array_replace_all(arrow_cast(make_array(1, 4, 4, 5, 4, 6, 7), 'LargeList(Int64)'), 4, 0),
  array_replace_all(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)'), 4, 0);
----
[1, 3, 3, 4] [1, 0, 0, 5, 0, 6, 7] [1, 2, 3]

# array_replace_all scalar function #2 (element is list)
query ??
select
  array_replace_all(
    make_array([1, 2, 3], [4, 5, 6], [5, 5, 5], [4, 5, 6], [7, 8, 9]),
    [4, 5, 6],
    [1, 1, 1]
  ),
  array_replace_all(
    make_array([1, 3, 2], [2, 3, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]),
    [2, 3, 4],
    [3, 1, 4]
  );
----
[[1, 2, 3], [1, 1, 1], [5, 5, 5], [1, 1, 1], [7, 8, 9]] [[1, 3, 2], [3, 1, 4], [3, 1, 4], [5, 3, 1], [1, 3, 2]]

query ??
select
  array_replace_all(
    arrow_cast(make_array([1, 2, 3], [4, 5, 6], [5, 5, 5], [4, 5, 6], [7, 8, 9]), 'LargeList(List(Int64))'),
    [4, 5, 6],
    [1, 1, 1]
  ),
  array_replace_all(
    arrow_cast(make_array([1, 3, 2], [2, 3, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]), 'LargeList(List(Int64))'),
    [2, 3, 4],
    [3, 1, 4]
  );
----
[[1, 2, 3], [1, 1, 1], [5, 5, 5], [1, 1, 1], [7, 8, 9]] [[1, 3, 2], [3, 1, 4], [3, 1, 4], [5, 3, 1], [1, 3, 2]]

# list_replace_all scalar function #3 (function alias `array_replace_all`)
query ???
select
  list_replace_all(make_array(1, 2, 3, 4), 2, 3),
  list_replace_all(make_array(1, 4, 4, 5, 4, 6, 7), 4, 0),
  list_replace_all(make_array(1, 2, 3), 4, 0);
----
[1, 3, 3, 4] [1, 0, 0, 5, 0, 6, 7] [1, 2, 3]

query ???
select
  list_replace_all(arrow_cast(make_array(1, 2, 3, 4), 'LargeList(Int64)'), 2, 3),
  list_replace_all(arrow_cast(make_array(1, 4, 4, 5, 4, 6, 7), 'LargeList(Int64)'), 4, 0),
  list_replace_all(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)'), 4, 0);
----
[1, 3, 3, 4] [1, 0, 0, 5, 0, 6, 7] [1, 2, 3]

# array_replace_all scalar function #4 (null input)
query ?
select array_replace_all(make_array(1, 2, 3, 4, 5), NULL, NULL);
----
[1, 2, 3, 4, 5]

query ?
select array_replace_all(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), NULL, NULL);
----
[1, 2, 3, 4, 5]

# array_replace_all scalar function with columns #1
query ?
select
  array_replace_all(column1, column2, column3)
from arrays_with_repeating_elements;
----
[1, 4, 1, 3, 4, 4, 1, 3, 4, 3]
[7, 7, 5, 5, 6, 5, 5, 5, 7, 7]
[10, 10, 10, 8, 10, 9, 10, 8, 10, 10]
[13, 11, 12, 13, 11, 12, 13, 11, 12, 13]

query ?
select
  array_replace_all(column1, column2, column3)
from large_arrays_with_repeating_elements;
----
[1, 4, 1, 3, 4, 4, 1, 3, 4, 3]
[7, 7, 5, 5, 6, 5, 5, 5, 7, 7]
[10, 10, 10, 8, 10, 9, 10, 8, 10, 10]
[13, 11, 12, 13, 11, 12, 13, 11, 12, 13]

# array_replace_all scalar function with columns #2 (element is list)
query ?
select
  array_replace_all(column1, column2, column3)
from nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [10, 11, 12], [1, 2, 3], [7, 8, 9], [10, 11, 12], [10, 11, 12], [1, 2, 3], [7, 8, 9], [10, 11, 12], [7, 8, 9]]
[[19, 20, 21], [19, 20, 21], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [19, 20, 21], [19, 20, 21]]
[[28, 29, 30], [28, 29, 30], [28, 29, 30], [22, 23, 24], [28, 29, 30], [25, 26, 27], [28, 29, 30], [22, 23, 24], [28, 29, 30], [28, 29, 30]]
[[37, 38, 39], [31, 32, 33], [34, 35, 36], [37, 38, 39], [31, 32, 33], [34, 35, 36], [37, 38, 39], [31, 32, 33], [34, 35, 36], [37, 38, 39]]

query ?
select
  array_replace_all(column1, column2, column3)
from large_nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [10, 11, 12], [1, 2, 3], [7, 8, 9], [10, 11, 12], [10, 11, 12], [1, 2, 3], [7, 8, 9], [10, 11, 12], [7, 8, 9]]
[[19, 20, 21], [19, 20, 21], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [19, 20, 21], [19, 20, 21]]
[[28, 29, 30], [28, 29, 30], [28, 29, 30], [22, 23, 24], [28, 29, 30], [25, 26, 27], [28, 29, 30], [22, 23, 24], [28, 29, 30], [28, 29, 30]]
[[37, 38, 39], [31, 32, 33], [34, 35, 36], [37, 38, 39], [31, 32, 33], [34, 35, 36], [37, 38, 39], [31, 32, 33], [34, 35, 36], [37, 38, 39]]

# array_replace_all scalar function with columns and scalars #1
query ???
select
  array_replace_all(make_array(1, 2, 2, 4, 5, 4, 4, 7, 7, 10, 7, 8), column2, column3),
  array_replace_all(column1, 1, column3),
  array_replace_all(column1, column2, 4)
from arrays_with_repeating_elements;
----
[1, 4, 4, 4, 5, 4, 4, 7, 7, 10, 7, 8] [4, 2, 4, 3, 2, 2, 4, 3, 2, 3] [1, 4, 1, 3, 4, 4, 1, 3, 4, 3]
[1, 2, 2, 7, 5, 7, 7, 7, 7, 10, 7, 8] [4, 4, 5, 5, 6, 5, 5, 5, 4, 4] [4, 4, 5, 5, 6, 5, 5, 5, 4, 4]
[1, 2, 2, 4, 5, 4, 4, 10, 10, 10, 10, 8] [7, 7, 7, 8, 7, 9, 7, 8, 7, 7] [4, 4, 4, 8, 4, 9, 4, 8, 4, 4]
[1, 2, 2, 4, 5, 4, 4, 7, 7, 13, 7, 8] [10, 11, 12, 10, 11, 12, 10, 11, 12, 10] [4, 11, 12, 4, 11, 12, 4, 11, 12, 4]

query ???
select
  array_replace_all(arrow_cast(make_array(1, 2, 2, 4, 5, 4, 4, 7, 7, 10, 7, 8), 'LargeList(Int64)'), column2, column3),
  array_replace_all(column1, 1, column3),
  array_replace_all(column1, column2, 4)
from large_arrays_with_repeating_elements;
----
[1, 4, 4, 4, 5, 4, 4, 7, 7, 10, 7, 8] [4, 2, 4, 3, 2, 2, 4, 3, 2, 3] [1, 4, 1, 3, 4, 4, 1, 3, 4, 3]
[1, 2, 2, 7, 5, 7, 7, 7, 7, 10, 7, 8] [4, 4, 5, 5, 6, 5, 5, 5, 4, 4] [4, 4, 5, 5, 6, 5, 5, 5, 4, 4]
[1, 2, 2, 4, 5, 4, 4, 10, 10, 10, 10, 8] [7, 7, 7, 8, 7, 9, 7, 8, 7, 7] [4, 4, 4, 8, 4, 9, 4, 8, 4, 4]
[1, 2, 2, 4, 5, 4, 4, 7, 7, 13, 7, 8] [10, 11, 12, 10, 11, 12, 10, 11, 12, 10] [4, 11, 12, 4, 11, 12, 4, 11, 12, 4]

# array_replace_all scalar function with columns and scalars #2 (element is list)
query ???
select
  array_replace_all(
    make_array([1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]),
    column2,
    column3
  ),
  array_replace_all(column1, make_array(1, 2, 3), column3),
  array_replace_all(column1, column2, make_array(11, 12, 13))
from nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [10, 11, 12], [10, 11, 12], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[10, 11, 12], [4, 5, 6], [10, 11, 12], [7, 8, 9], [4, 5, 6], [4, 5, 6], [10, 11, 12], [7, 8, 9], [4, 5, 6], [7, 8, 9]] [[1, 2, 3], [11, 12, 13], [1, 2, 3], [7, 8, 9], [11, 12, 13], [11, 12, 13], [1, 2, 3], [7, 8, 9], [11, 12, 13], [7, 8, 9]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [19, 20, 21], [13, 14, 15], [19, 20, 21], [19, 20, 21], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[10, 11, 12], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]] [[11, 12, 13], [11, 12, 13], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [11, 12, 13], [11, 12, 13]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [28, 29, 30], [28, 29, 30], [28, 29, 30], [28, 29, 30], [22, 23, 24]] [[19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]] [[11, 12, 13], [11, 12, 13], [11, 12, 13], [22, 23, 24], [11, 12, 13], [25, 26, 27], [11, 12, 13], [22, 23, 24], [11, 12, 13], [11, 12, 13]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [37, 38, 39], [19, 20, 21], [22, 23, 24]] [[28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]] [[11, 12, 13], [31, 32, 33], [34, 35, 36], [11, 12, 13], [31, 32, 33], [34, 35, 36], [11, 12, 13], [31, 32, 33], [34, 35, 36], [11, 12, 13]]

query ???
select
  array_replace_all(
    arrow_cast(make_array([1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]), 'LargeList(List(Int64))'),
    column2,
    column3
  ),
  array_replace_all(column1, make_array(1, 2, 3), column3),
  array_replace_all(column1, column2, make_array(11, 12, 13))
from nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [10, 11, 12], [10, 11, 12], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[10, 11, 12], [4, 5, 6], [10, 11, 12], [7, 8, 9], [4, 5, 6], [4, 5, 6], [10, 11, 12], [7, 8, 9], [4, 5, 6], [7, 8, 9]] [[1, 2, 3], [11, 12, 13], [1, 2, 3], [7, 8, 9], [11, 12, 13], [11, 12, 13], [1, 2, 3], [7, 8, 9], [11, 12, 13], [7, 8, 9]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [19, 20, 21], [13, 14, 15], [19, 20, 21], [19, 20, 21], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[10, 11, 12], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]] [[11, 12, 13], [11, 12, 13], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [11, 12, 13], [11, 12, 13]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [28, 29, 30], [28, 29, 30], [28, 29, 30], [28, 29, 30], [22, 23, 24]] [[19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]] [[11, 12, 13], [11, 12, 13], [11, 12, 13], [22, 23, 24], [11, 12, 13], [25, 26, 27], [11, 12, 13], [22, 23, 24], [11, 12, 13], [11, 12, 13]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [37, 38, 39], [19, 20, 21], [22, 23, 24]] [[28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]] [[11, 12, 13], [31, 32, 33], [34, 35, 36], [11, 12, 13], [31, 32, 33], [34, 35, 36], [11, 12, 13], [31, 32, 33], [34, 35, 36], [11, 12, 13]]

# array_replace with null handling

statement ok
create table t as values
  (make_array(3, 1, NULL, 3), 3,    4,    2),
  (make_array(3, 1, NULL, 3), NULL, 5,    2),
  (NULL,                            3,    2,    1),
  (make_array(3, 1, 3),             3,    NULL, 1)
;


# ([3, 1, NULL, 3], 3,    4,    2)  => [4, 1, NULL, 4] NULL not matched
# ([3, 1, NULL, 3], NULL, 5,    2)  => [3, 1, NULL, 3] NULL is replaced with 5
# ([NULL],          3,    2,    1)  => NULL
# ([3, 1, 3],       3,    NULL, 1)  => [NULL, 1 3]

query ?III?
select column1, column2, column3, column4, array_replace_n(column1, column2, column3, column4) from t;
----
[3, 1, NULL, 3] 3 4 2 [4, 1, NULL, 4]
[3, 1, NULL, 3] NULL 5 2 [3, 1, 5, 3]
NULL 3 2 1 NULL
[3, 1, 3] 3 NULL 1 [NULL, 1, 3]



statement ok
drop table t;



## array_to_string (aliases: `list_to_string`, `array_join`, `list_join`)

# array_to_string scalar function #1
query TTT
select array_to_string(['h', 'e', 'l', 'l', 'o'], ','), array_to_string([1, 2, 3, 4, 5], '-'), array_to_string([1.0, 2.0, 3.0], '|');
----
h,e,l,l,o 1-2-3-4-5 1|2|3

# array_to_string scalar function #2
query TTT
select array_to_string([1, 1, 1], '1'), array_to_string([[1, 2], [3, 4], [5, 6]], '+'), array_to_string(array_repeat(array_repeat(array_repeat(3, 2), 2), 3), '/\');
----
11111 1+2+3+4+5+6 3/\3/\3/\3/\3/\3/\3/\3/\3/\3/\3/\3

# array_to_string scalar function #3
query T
select array_to_string(make_array(), ',')
----
(empty)

# array to string dictionary
statement ok
CREATE TABLE table1 AS VALUES
  (1, 'foo'),
  (3, 'bar'),
  (1, 'foo'),
  (2, NULL),
  (NULL, 'baz')
  ;

# expect 1-3-1-2 (dictionary values should be repeated)
query T
SELECT array_to_string(array_agg(column1),'-')
FROM (
  SELECT arrow_cast(column1, 'Dictionary(Int32, Int32)') as column1
  FROM table1
);
----
1-3-1-2

# expect foo,bar,foo,baz (dictionary values should be repeated)
query T
SELECT array_to_string(array_agg(column2),',')
FROM (
  SELECT arrow_cast(column2, 'Dictionary(Int64, Utf8)') as column2
  FROM table1
);
----
foo,bar,foo,baz

# Expect only values that are in the group
query I?T
SELECT column1, array_agg(column2), array_to_string(array_agg(column2),',')
FROM (
  SELECT column1, arrow_cast(column2, 'Dictionary(Int32, Utf8)') as column2
  FROM table1
)
GROUP BY column1
ORDER BY column1;
----
1 [foo, foo] foo,foo
2 [NULL] (empty)
3 [bar] bar
NULL [baz] baz

# verify make_array does force to Utf8View
query T
SELECT arrow_typeof(make_array(arrow_cast('a', 'Utf8View'), 'b', 'c', 'd'));
----
List(Field { name: "item", data_type: Utf8View, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} })

# expect a,b,c,d. make_array forces all types to be of a common type (see above)
query T
SELECT array_to_string(make_array(arrow_cast('a', 'Utf8View'), 'b', 'c', 'd'), ',');
----
a,b,c,d

# array_to_string using largeutf8 for second arg
query TTT
select array_to_string(['h', 'e', 'l', 'l', 'o'], arrow_cast(',', 'LargeUtf8')), array_to_string([1, 2, 3, 4, 5], arrow_cast('-', 'LargeUtf8')), array_to_string([1.0, 2.0, 3.0], arrow_cast('|', 'LargeUtf8'));
----
h,e,l,l,o 1-2-3-4-5 1|2|3

# array_to_string using utf8view for second arg
query TTT
select array_to_string(['h', 'e', 'l', 'l', 'o'], arrow_cast(',', 'Utf8View')), array_to_string([1, 2, 3, 4, 5], arrow_cast('-', 'Utf8View')), array_to_string([1.0, 2.0, 3.0], arrow_cast('|', 'Utf8View'));
----
h,e,l,l,o 1-2-3-4-5 1|2|3

statement ok
drop table table1;


## array_union (aliases: `list_union`)

# array_union scalar function #1
query ?
select array_union([1, 2, 3, 4], [5, 6, 3, 4]);
----
[1, 2, 3, 4, 5, 6]

query ?
select array_union(arrow_cast([1, 2, 3, 4], 'LargeList(Int64)'), arrow_cast([5, 6, 3, 4], 'LargeList(Int64)'));
----
[1, 2, 3, 4, 5, 6]

# array_union scalar function #2
query ?
select array_union([1, 2, 3, 4], [5, 6, 7, 8]);
----
[1, 2, 3, 4, 5, 6, 7, 8]

query ?
select array_union(arrow_cast([1, 2, 3, 4], 'LargeList(Int64)'), arrow_cast([5, 6, 7, 8], 'LargeList(Int64)'));
----
[1, 2, 3, 4, 5, 6, 7, 8]

# array_union scalar function #3
query ?
select array_union([1,2,3], []);
----
[1, 2, 3]

query ?
select array_union(arrow_cast([1,2,3], 'LargeList(Int64)'), arrow_cast([], 'LargeList(Int64)'));
----
[1, 2, 3]

# array_union scalar function #4
query ?
select array_union([1, 2, 3, 4], [5, 4]);
----
[1, 2, 3, 4, 5]

query ?
select array_union(arrow_cast([1, 2, 3, 4], 'LargeList(Int64)'), arrow_cast([5, 4], 'LargeList(Int64)'));
----
[1, 2, 3, 4, 5]

# array_union scalar function #5
statement ok
CREATE TABLE arrays_with_repeating_elements_for_union
AS VALUES
  ([1], [2]),
  ([2, 3], [3]),
  ([3], [3, 4])
;

query ?
select array_union(column1, column2) from arrays_with_repeating_elements_for_union;
----
[1, 2]
[2, 3]
[3, 4]

query ?
select array_union(arrow_cast(column1, 'LargeList(Int64)'), arrow_cast(column2, 'LargeList(Int64)')) from arrays_with_repeating_elements_for_union;
----
[1, 2]
[2, 3]
[3, 4]

statement ok
drop table arrays_with_repeating_elements_for_union;

# array_union scalar function #6
query ?
select array_union([], []);
----
[]

query ?
select array_union(arrow_cast([], 'LargeList(Int64)'), arrow_cast([], 'LargeList(Int64)'));
----
[]

# array_union scalar function #7
query ?
select array_union([[null]], []);
----
[[NULL]]

query ?
select array_union(arrow_cast([[null]], 'LargeList(List(Int64))'), arrow_cast([], 'LargeList(Int64)'));
----
[[NULL]]

# array_union scalar function #8
query ?
select array_union([null], [null]);
----
[NULL]

query ?
select array_union(arrow_cast([[null]], 'LargeList(List(Int64))'), arrow_cast([[null]], 'LargeList(List(Int64))'));
----
[[NULL]]

# array_union scalar function #9
query ?
select array_union(null, []);
----
[]

query ?
select array_union(null, arrow_cast([], 'LargeList(Int64)'));
----
[]

# array_union scalar function #10
query ?
select array_union(null, null);
----
NULL

# array_union scalar function #11
query ?
select array_union([1, 1, 2, 2, 3, 3], null);
----
[1, 2, 3]

query ?
select array_union(arrow_cast([1, 1, 2, 2, 3, 3], 'LargeList(Int64)'), null);
----
[1, 2, 3]

# array_union scalar function #12
query ?
select array_union(null, [1, 1, 2, 2, 3, 3]);
----
[1, 2, 3]

query ?
select array_union(null, arrow_cast([1, 1, 2, 2, 3, 3], 'LargeList(Int64)'));
----
[1, 2, 3]

# array_union scalar function #13
query ?
select array_union([1.2, 3.0], [1.2, 3.0, 5.7]);
----
[1.2, 3.0, 5.7]

query ?
select array_union(arrow_cast([1.2, 3.0], 'LargeList(Float64)'), arrow_cast([1.2, 3.0, 5.7], 'LargeList(Float64)'));
----
[1.2, 3.0, 5.7]

# array_union scalar function #14
query ?
select array_union(['hello'], ['hello','datafusion']);
----
[hello, datafusion]

query ?
select array_union(arrow_cast(['hello'], 'LargeList(Utf8)'), arrow_cast(['hello','datafusion'], 'LargeList(Utf8)'));
----
[hello, datafusion]


# list_to_string scalar function #4 (function alias `array_to_string`)
query TTT
select list_to_string(['h', 'e', 'l', 'l', 'o'], ','), list_to_string([1, 2, 3, 4, 5], '-'), list_to_string([1.0, 2.0, 3.0], '|');
----
h,e,l,l,o 1-2-3-4-5 1|2|3

query TTT
select list_to_string(arrow_cast(['h', 'e', 'l', 'l', 'o'], 'LargeList(Utf8)'), ','), list_to_string(arrow_cast([1, 2, 3, 4, 5], 'LargeList(Int64)'), '-'), list_to_string(arrow_cast([1.0, 2.0, 3.0], 'LargeList(Float64)'), '|');
----
h,e,l,l,o 1-2-3-4-5 1|2|3

# array_join scalar function #5 (function alias `array_to_string`)
query TTT
select array_join(['h', 'e', 'l', 'l', 'o'], ','), array_join([1, 2, 3, 4, 5], '-'), array_join([1.0, 2.0, 3.0], '|');
----
h,e,l,l,o 1-2-3-4-5 1|2|3

query TTT
select array_join(arrow_cast(['h', 'e', 'l', 'l', 'o'], 'LargeList(Utf8)'), ','), array_join(arrow_cast([1, 2, 3, 4, 5], 'LargeList(Int64)'), '-'), array_join(arrow_cast([1.0, 2.0, 3.0], 'LargeList(Float64)'), '|');
----
h,e,l,l,o 1-2-3-4-5 1|2|3

# list_join scalar function #6 (function alias `list_join`)
query TTT
select list_join(['h', 'e', 'l', 'l', 'o'], ','), list_join([1, 2, 3, 4, 5], '-'), list_join([1.0, 2.0, 3.0], '|');
----
h,e,l,l,o 1-2-3-4-5 1|2|3

query TTT
select list_join(arrow_cast(['h', 'e', 'l', 'l', 'o'], 'LargeList(Utf8)'), ','), list_join(arrow_cast([1, 2, 3, 4, 5], 'LargeList(Int64)'), '-'), list_join(arrow_cast([1.0, 2.0, 3.0], 'LargeList(Float64)'), '|');
----
h,e,l,l,o 1-2-3-4-5 1|2|3

# array_to_string scalar function with nulls #1
query TTT
select array_to_string(make_array('h', NULL, 'l', NULL, 'o'), ','), array_to_string(make_array(1, NULL, 3, NULL, 5), '-'), array_to_string(make_array(NULL, 2.0, 3.0), '|');
----
h,l,o 1-3-5 2|3

query TTT
select array_to_string(arrow_cast(['h', 'e', 'l', 'l', 'o'], 'LargeList(Utf8)'), ','), array_to_string(arrow_cast([1, 2, 3, 4, 5], 'LargeList(Int64)'), '-'), array_to_string(arrow_cast([1.0, 2.0, 3.0], 'LargeList(Float64)'), '|');
----
h,e,l,l,o 1-2-3-4-5 1|2|3

# array_to_string scalar function with nulls #2
query TTT
select array_to_string(make_array('h', NULL, NULL, NULL, 'o'), ',', '-'), array_to_string(make_array(NULL, 2, NULL, 4, 5), '-', 'nil'), array_to_string(make_array(1.0, NULL, 3.0), '|', '0');
----
h,-,-,-,o nil-2-nil-4-5 1|0|3

query TTT
select array_to_string(arrow_cast(make_array('h', NULL, NULL, NULL, 'o'), 'LargeList(Utf8)'), ',', '-'), array_to_string(arrow_cast(make_array(NULL, 2, NULL, 4, 5), 'LargeList(Int64)'), '-', 'nil'), array_to_string(arrow_cast(make_array(1.0, NULL, 3.0), 'LargeList(Float64)'), '|', '0');
----
h,-,-,-,o nil-2-nil-4-5 1|0|3

# array_to_string with columns #1

# For reference
# select column1, column4 from arrays_values;
# ----
# [NULL, 2, 3, 4, 5, 6, 7, 8, 9, 10] ,
# [11, 12, 13, 14, 15, 16, 17, 18, NULL, 20] .
# [21, 22, 23, NULL, 25, 26, 27, 28, 29, 30] -
# [31, 32, 33, 34, 35, NULL, 37, 38, 39, 40] ok
# NULL @
# [41, 42, 43, 44, 45, 46, 47, 48, 49, 50] $
# [51, 52, NULL, 54, 55, 56, 57, 58, 59, 60] ^
# [61, 62, 63, 64, 65, 66, 67, 68, 69, 70] NULL

query T
select array_to_string(column1, column4) from arrays_values;
----
2,3,4,5,6,7,8,9,10
11.12.13.14.15.16.17.18.20
21-22-23-25-26-27-28-29-30
31ok32ok33ok34ok35ok37ok38ok39ok40
NULL
41$42$43$44$45$46$47$48$49$50
51^52^54^55^56^57^58^59^60
NULL

query T
select array_to_string(column1, column4) from large_arrays_values;
----
2,3,4,5,6,7,8,9,10
11.12.13.14.15.16.17.18.20
21-22-23-25-26-27-28-29-30
31ok32ok33ok34ok35ok37ok38ok39ok40
NULL
41$42$43$44$45$46$47$48$49$50
51^52^54^55^56^57^58^59^60
NULL

query TT
select array_to_string(column1, '_'), array_to_string(make_array(1,2,3), '/') from arrays_values;
----
2_3_4_5_6_7_8_9_10 1/2/3
11_12_13_14_15_16_17_18_20 1/2/3
21_22_23_25_26_27_28_29_30 1/2/3
31_32_33_34_35_37_38_39_40 1/2/3
NULL 1/2/3
41_42_43_44_45_46_47_48_49_50 1/2/3
51_52_54_55_56_57_58_59_60 1/2/3
61_62_63_64_65_66_67_68_69_70 1/2/3

query TT
select array_to_string(column1, '_'), array_to_string(make_array(1,2,3), '/') from large_arrays_values;
----
2_3_4_5_6_7_8_9_10 1/2/3
11_12_13_14_15_16_17_18_20 1/2/3
21_22_23_25_26_27_28_29_30 1/2/3
31_32_33_34_35_37_38_39_40 1/2/3
NULL 1/2/3
41_42_43_44_45_46_47_48_49_50 1/2/3
51_52_54_55_56_57_58_59_60 1/2/3
61_62_63_64_65_66_67_68_69_70 1/2/3

query TT
select array_to_string(column1, '_', '*'), array_to_string(make_array(make_array(1,2,3)), '.') from arrays_values;
----
*_2_3_4_5_6_7_8_9_10 1.2.3
11_12_13_14_15_16_17_18_*_20 1.2.3
21_22_23_*_25_26_27_28_29_30 1.2.3
31_32_33_34_35_*_37_38_39_40 1.2.3
NULL 1.2.3
41_42_43_44_45_46_47_48_49_50 1.2.3
51_52_*_54_55_56_57_58_59_60 1.2.3
61_62_63_64_65_66_67_68_69_70 1.2.3

query TT
select array_to_string(column1, '_', '*'), array_to_string(make_array(make_array(1,2,3)), '.') from large_arrays_values;
----
*_2_3_4_5_6_7_8_9_10 1.2.3
11_12_13_14_15_16_17_18_*_20 1.2.3
21_22_23_*_25_26_27_28_29_30 1.2.3
31_32_33_34_35_*_37_38_39_40 1.2.3
NULL 1.2.3
41_42_43_44_45_46_47_48_49_50 1.2.3
51_52_*_54_55_56_57_58_59_60 1.2.3
61_62_63_64_65_66_67_68_69_70 1.2.3

## cardinality

# cardinality scalar function
query III
select cardinality(make_array(1, 2, 3, 4, 5)), cardinality([1, 3, 5]), cardinality(make_array('h', 'e', 'l', 'l', 'o'));
----
5 3 5

query III
select cardinality(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)')), cardinality(arrow_cast([1, 3, 5], 'LargeList(Int64)')), cardinality(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'));
----
5 3 5

query III
select cardinality(arrow_cast([1, 2, 3, 4, 5], 'FixedSizeList(5, Int64)')), cardinality(arrow_cast([1, 3, 5], 'FixedSizeList(3, Int64)')), cardinality(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'FixedSizeList(5, Utf8)'));
----
5 3 5

# cardinality scalar function #2
query II
select cardinality(make_array([1, 2], [3, 4], [5, 6])), cardinality(array_repeat(array_repeat(array_repeat(3, 3), 2), 3));
----
6 18

query I
select cardinality(arrow_cast(make_array([1, 2], [3, 4], [5, 6]), 'LargeList(List(Int64))'));
----
6

query I
select cardinality(arrow_cast([[1, 2], [3, 4], [5, 6]], 'FixedSizeList(3, List(Int64))'));
----
6

# cardinality scalar function #3
query II
select cardinality(make_array()), cardinality(make_array(make_array()))
----
NULL 0

query II
select cardinality(arrow_cast(make_array(), 'LargeList(Int64)')), cardinality(arrow_cast(make_array(make_array()), 'LargeList(List(Int64))'))
----
NULL 0

#TODO
#https://github.com/apache/datafusion/issues/9158
#query II
#select cardinality(arrow_cast(make_array(), 'FixedSizeList(1, Null)')), cardinality(arrow_cast(make_array(make_array()), 'FixedSizeList(1, List(Int64))'))
#----
#NULL 0

# cardinality with columns
query III
select cardinality(column1), cardinality(column2), cardinality(column3) from arrays;
----
4 3 5
4 3 5
4 3 5
4 3 3
NULL 3 4
4 NULL 1
4 3 NULL

query III
select cardinality(column1), cardinality(column2), cardinality(column3) from large_arrays;
----
4 3 5
4 3 5
4 3 5
4 3 3
NULL 3 4
4 NULL 1
4 3 NULL

query III
select cardinality(column1), cardinality(column2), cardinality(column3) from fixed_size_arrays;
----
4 3 5
4 3 5
4 3 5
4 3 5
NULL 3 5
4 NULL 5
4 3 NULL

## array_remove (aliases: `list_remove`)

# array_remove scalar function #1
query ???
select array_remove(make_array(1, 2, 2, 1, 1), 2), array_remove(make_array(1.0, 2.0, 2.0, 1.0, 1.0), 1.0), array_remove(make_array('h', 'e', 'l', 'l', 'o'), 'l');
----
[1, 2, 1, 1] [2.0, 2.0, 1.0, 1.0] [h, e, l, o]

query ???
select array_remove(arrow_cast(make_array(1, 2, 2, 1, 1), 'LargeList(Int64)'), 2),
       array_remove(arrow_cast(make_array(1.0, 2.0, 2.0, 1.0, 1.0), 'LargeList(Float64)'), 1.0),
       array_remove(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'LargeList(Utf8)'), 'l');
----
[1, 2, 1, 1] [2.0, 2.0, 1.0, 1.0] [h, e, l, o]

query ???
select array_remove(arrow_cast(make_array(1, 2, 2, 1, 1), 'FixedSizeList(5, Int64)'), 2),
       array_remove(arrow_cast(make_array(1.0, 2.0, 2.0, 1.0, 1.0), 'FixedSizeList(5, Float64)'), 1.0),
       array_remove(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'FixedSizeList(5, Utf8)'), 'l');
----
[1, 2, 1, 1] [2.0, 2.0, 1.0, 1.0] [h, e, l, o]

query ???
select
  array_remove(make_array(1, null, 2, 3), 2),
  array_remove(make_array(1.1, null, 2.2, 3.3), 1.1),
  array_remove(make_array('a', null, 'bc'), 'a');
----
[1, NULL, 3] [NULL, 2.2, 3.3] [NULL, bc]

query ???
select
  array_remove(arrow_cast(make_array(1, null, 2, 3), 'LargeList(Int64)'), 2),
  array_remove(arrow_cast(make_array(1.1, null, 2.2, 3.3), 'LargeList(Float64)'), 1.1),
  array_remove(arrow_cast(make_array('a', null, 'bc'), 'LargeList(Utf8)'), 'a');
----
[1, NULL, 3] [NULL, 2.2, 3.3] [NULL, bc]

query ???
select
  array_remove(arrow_cast(make_array(1, null, 2, 3), 'FixedSizeList(4, Int64)'), 2),
  array_remove(arrow_cast(make_array(1.1, null, 2.2, 3.3), 'FixedSizeList(4, Float64)'), 1.1),
  array_remove(arrow_cast(make_array('a', null, 'bc'), 'FixedSizeList(3, Utf8)'), 'a');
----
[1, NULL, 3] [NULL, 2.2, 3.3] [NULL, bc]

#TODO: https://github.com/apache/datafusion/issues/7142
# follow PostgreSQL behavior
#query ?
#select
#  array_remove(NULL, 1)
#----
#NULL

query ??
select
 array_remove(make_array(1, null, 2), null),
 array_remove(make_array(1, null, 2, null), null);
----
[1, 2] [1, 2, NULL]

query ??
select
 array_remove(arrow_cast(make_array(1, null, 2), 'LargeList(Int64)'), null),
 array_remove(arrow_cast(make_array(1, null, 2, null), 'LargeList(Int64)'), null);
----
[1, 2] [1, 2, NULL]

query ??
select
 array_remove(arrow_cast(make_array(1, null, 2), 'FixedSizeList(3, Int64)'), null),
 array_remove(arrow_cast(make_array(1, null, 2, null), 'FixedSizeList(4, Int64)'), null);
----
[1, 2] [1, 2, NULL]

# array_remove scalar function #2 (element is list)
query ??
select array_remove(make_array([1, 2, 3], [4, 5, 6], [5, 5, 5], [4, 5, 6], [7, 8, 9]), [4, 5, 6]), array_remove(make_array([1, 3, 2], [2, 3, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]), [2, 3, 4]);
----
[[1, 2, 3], [5, 5, 5], [4, 5, 6], [7, 8, 9]] [[1, 3, 2], [2, 3, 4], [5, 3, 1], [1, 3, 2]]

query ??
select array_remove(arrow_cast(make_array([1, 2, 3], [4, 5, 6], [5, 5, 5], [4, 5, 6], [7, 8, 9]), 'LargeList(List(Int64))'), [4, 5, 6]),
       array_remove(arrow_cast(make_array([1, 3, 2], [2, 3, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]), 'LargeList(List(Int64))'), [2, 3, 4]);
----
[[1, 2, 3], [5, 5, 5], [4, 5, 6], [7, 8, 9]] [[1, 3, 2], [2, 3, 4], [5, 3, 1], [1, 3, 2]]

query ??
select array_remove(arrow_cast(make_array([1, 2, 3], [4, 5, 6], [5, 5, 5], [4, 5, 6], [7, 8, 9]), 'FixedSizeList(5, List(Int64))'), [4, 5, 6]),
       array_remove(arrow_cast(make_array([1, 3, 2], [2, 3, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]), 'FixedSizeList(5, List(Int64))'), [2, 3, 4]);
----
[[1, 2, 3], [5, 5, 5], [4, 5, 6], [7, 8, 9]] [[1, 3, 2], [2, 3, 4], [5, 3, 1], [1, 3, 2]]

# list_remove scalar function #3 (function alias `array_remove`)
query ???
select list_remove(make_array(1, 2, 2, 1, 1), 2), list_remove(make_array(1.0, 2.0, 2.0, 1.0, 1.0), 1.0), list_remove(make_array('h', 'e', 'l', 'l', 'o'), 'l');
----
[1, 2, 1, 1] [2.0, 2.0, 1.0, 1.0] [h, e, l, o]

query ??
select list_remove(arrow_cast(make_array([1, 2, 3], [4, 5, 6], [5, 5, 5], [4, 5, 6], [7, 8, 9]), 'FixedSizeList(5, List(Int64))'), [4, 5, 6]),
       list_remove(arrow_cast(make_array([1, 3, 2], [2, 3, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]), 'FixedSizeList(5, List(Int64))'), [2, 3, 4]);
----
[[1, 2, 3], [5, 5, 5], [4, 5, 6], [7, 8, 9]] [[1, 3, 2], [2, 3, 4], [5, 3, 1], [1, 3, 2]]

# array_remove scalar function with columns #1
query ?
select array_remove(column1, column2) from arrays_with_repeating_elements;
----
[1, 1, 3, 2, 2, 1, 3, 2, 3]
[4, 5, 5, 6, 5, 5, 5, 4, 4]
[7, 7, 8, 7, 9, 7, 8, 7, 7]
[11, 12, 10, 11, 12, 10, 11, 12, 10]

query ?
select array_remove(column1, column2) from large_arrays_with_repeating_elements;
----
[1, 1, 3, 2, 2, 1, 3, 2, 3]
[4, 5, 5, 6, 5, 5, 5, 4, 4]
[7, 7, 8, 7, 9, 7, 8, 7, 7]
[11, 12, 10, 11, 12, 10, 11, 12, 10]

query ?
select array_remove(column1, column2) from fixed_arrays_with_repeating_elements;
----
[1, 1, 3, 2, 2, 1, 3, 2, 3]
[4, 5, 5, 6, 5, 5, 5, 4, 4]
[7, 7, 8, 7, 9, 7, 8, 7, 7]
[11, 12, 10, 11, 12, 10, 11, 12, 10]

# array_remove scalar function with columns #2 (element is list)
query ?
select array_remove(column1, column2) from nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [1, 2, 3], [7, 8, 9], [4, 5, 6], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]]
[[10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]]
[[19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]]
[[31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]]

query ?
select array_remove(column1, column2) from large_nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [1, 2, 3], [7, 8, 9], [4, 5, 6], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]]
[[10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]]
[[19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]]
[[31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]]

query ?
select array_remove(column1, column2) from fixed_size_nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [1, 2, 3], [7, 8, 9], [4, 5, 6], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]]
[[10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]]
[[19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]]
[[31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]]

# array_remove scalar function with columns and scalars #1
query ??
select array_remove(make_array(1, 2, 2, 4, 5, 4, 4, 7, 7, 10, 7, 8), column2), array_remove(column1, 1) from arrays_with_repeating_elements;
----
[1, 2, 4, 5, 4, 4, 7, 7, 10, 7, 8] [2, 1, 3, 2, 2, 1, 3, 2, 3]
[1, 2, 2, 5, 4, 4, 7, 7, 10, 7, 8] [4, 4, 5, 5, 6, 5, 5, 5, 4, 4]
[1, 2, 2, 4, 5, 4, 4, 7, 10, 7, 8] [7, 7, 7, 8, 7, 9, 7, 8, 7, 7]
[1, 2, 2, 4, 5, 4, 4, 7, 7, 7, 8] [10, 11, 12, 10, 11, 12, 10, 11, 12, 10]

query ??
select array_remove(make_array(1, 2, 2, 4, 5, 4, 4, 7, 7, 10, 7, 8), column2), array_remove(column1, 1) from large_arrays_with_repeating_elements;
----
[1, 2, 4, 5, 4, 4, 7, 7, 10, 7, 8] [2, 1, 3, 2, 2, 1, 3, 2, 3]
[1, 2, 2, 5, 4, 4, 7, 7, 10, 7, 8] [4, 4, 5, 5, 6, 5, 5, 5, 4, 4]
[1, 2, 2, 4, 5, 4, 4, 7, 10, 7, 8] [7, 7, 7, 8, 7, 9, 7, 8, 7, 7]
[1, 2, 2, 4, 5, 4, 4, 7, 7, 7, 8] [10, 11, 12, 10, 11, 12, 10, 11, 12, 10]

query ??
select array_remove(make_array(1, 2, 2, 4, 5, 4, 4, 7, 7, 10, 7, 8), column2), array_remove(column1, 1) from fixed_arrays_with_repeating_elements;
----
[1, 2, 4, 5, 4, 4, 7, 7, 10, 7, 8] [2, 1, 3, 2, 2, 1, 3, 2, 3]
[1, 2, 2, 5, 4, 4, 7, 7, 10, 7, 8] [4, 4, 5, 5, 6, 5, 5, 5, 4, 4]
[1, 2, 2, 4, 5, 4, 4, 7, 10, 7, 8] [7, 7, 7, 8, 7, 9, 7, 8, 7, 7]
[1, 2, 2, 4, 5, 4, 4, 7, 7, 7, 8] [10, 11, 12, 10, 11, 12, 10, 11, 12, 10]

# array_remove scalar function with columns and scalars #2 (element is list)
query ??
select array_remove(make_array([1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]), column2),
       array_remove(column1, make_array(1, 2, 3)) from nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[10, 11, 12], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24]] [[28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]]

query ??
select array_remove(make_array([1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]), column2),
       array_remove(column1, make_array(1, 2, 3)) from large_nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[10, 11, 12], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24]] [[28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]]

query ??
select array_remove(make_array([1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]), column2),
       array_remove(column1, make_array(1, 2, 3)) from fixed_size_nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[10, 11, 12], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24]] [[28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]]

## array_remove_n (aliases: `list_remove_n`)

# array_remove_n scalar function #1
query ???
select array_remove_n(make_array(1, 2, 2, 1, 1), 2, 2), array_remove_n(make_array(1.0, 2.0, 2.0, 1.0, 1.0), 1.0, 2), array_remove_n(make_array('h', 'e', 'l', 'l', 'o'), 'l', 3);
----
[1, 1, 1] [2.0, 2.0, 1.0] [h, e, o]

# array_remove_n scalar function #2 (element is list)
query ??
select array_remove_n(make_array([1, 2, 3], [4, 5, 6], [5, 5, 5], [4, 5, 6], [7, 8, 9]), [4, 5, 6], 2), array_remove_n(make_array([1, 3, 2], [2, 3, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]), [2, 3, 4], 2);
----
[[1, 2, 3], [5, 5, 5], [7, 8, 9]] [[1, 3, 2], [5, 3, 1], [1, 3, 2]]

# list_remove_n scalar function #3 (function alias `array_remove_n`)
query ???
select list_remove_n(make_array(1, 2, 2, 1, 1), 2, 2), list_remove_n(make_array(1.0, 2.0, 2.0, 1.0, 1.0), 1.0, 2), list_remove_n(make_array('h', 'e', 'l', 'l', 'o'), 'l', 3);
----
[1, 1, 1] [2.0, 2.0, 1.0] [h, e, o]

# array_remove_n scalar function with columns #1
query ?
select array_remove_n(column1, column2, column4) from arrays_with_repeating_elements;
----
[1, 1, 3, 1, 3, 2, 3]
[5, 5, 6, 5, 5, 5, 4, 4]
[8, 9, 8, 7, 7]
[11, 12, 11, 12, 11, 12]

# array_remove_n scalar function with columns #2 (element is list)
query ?
select array_remove_n(column1, column2, column4) from nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [1, 2, 3], [7, 8, 9], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]]
[[13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]]
[[22, 23, 24], [25, 26, 27], [22, 23, 24], [19, 20, 21], [19, 20, 21]]
[[31, 32, 33], [34, 35, 36], [31, 32, 33], [34, 35, 36], [31, 32, 33], [34, 35, 36]]

# array_remove_n scalar function with columns and scalars #1
query ???
select array_remove_n(make_array(1, 2, 2, 4, 5, 4, 4, 7, 7, 10, 7, 8), column2, column4), array_remove_n(column1, 1, column4), array_remove_n(column1, column2, 2) from arrays_with_repeating_elements;
----
[1, 4, 5, 4, 4, 7, 7, 10, 7, 8] [2, 3, 2, 2, 3, 2, 3] [1, 1, 3, 2, 1, 3, 2, 3]
[1, 2, 2, 5, 4, 7, 7, 10, 7, 8] [4, 4, 5, 5, 6, 5, 5, 5, 4, 4] [5, 5, 6, 5, 5, 5, 4, 4]
[1, 2, 2, 4, 5, 4, 4, 10, 8] [7, 7, 7, 8, 7, 9, 7, 8, 7, 7] [7, 8, 7, 9, 7, 8, 7, 7]
[1, 2, 2, 4, 5, 4, 4, 7, 7, 7, 8] [10, 11, 12, 10, 11, 12, 10, 11, 12, 10] [11, 12, 11, 12, 10, 11, 12, 10]

# array_remove_n scalar function with columns and scalars #2 (element is list)
query ???
select array_remove_n(make_array([1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]), column2, column4), array_remove_n(column1, make_array(1, 2, 3), column4), array_remove_n(column1, column2, 2) from nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[4, 5, 6], [7, 8, 9], [4, 5, 6], [4, 5, 6], [7, 8, 9], [4, 5, 6], [7, 8, 9]] [[1, 2, 3], [1, 2, 3], [7, 8, 9], [4, 5, 6], [1, 2, 3], [7, 8, 9], [4, 5, 6], [7, 8, 9]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [13, 14, 15], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[10, 11, 12], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]] [[13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [28, 29, 30], [22, 23, 24]] [[19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]] [[19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24]] [[28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]] [[31, 32, 33], [34, 35, 36], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]]

## array_remove_all (aliases: `list_removes`)

#TODO: https://github.com/apache/datafusion/issues/7142
# array_remove_all with NULL elements
#query ?
#select array_remove_all(NULL, 1);
#----
#NULL

query ?
select array_remove_all(make_array(1, 2, 2, 1, 1), NULL);
----
[1, 2, 2, 1, 1]

# array_remove_all scalar function #1
query ???
select array_remove_all(make_array(1, 2, 2, 1, 1), 2), array_remove_all(make_array(1.0, 2.0, 2.0, 1.0, 1.0), 1.0), array_remove_all(make_array('h', 'e', 'l', 'l', 'o'), 'l');
----
[1, 1, 1] [2.0, 2.0] [h, e, o]

query ???
select array_remove_all(arrow_cast(make_array(1, 2, 2, 1, 1), 'FixedSizeList(5, Int64)'), 2), array_remove_all(arrow_cast(make_array(1.0, 2.0, 2.0, 1.0, 1.0), 'FixedSizeList(5, Float64)'), 1.0), array_remove_all(arrow_cast(make_array('h', 'e', 'l', 'l', 'o'), 'FixedSizeList(5, Utf8)'), 'l');
----
[1, 1, 1] [2.0, 2.0] [h, e, o]

# array_remove_all scalar function #2 (element is list)
query ??
select array_remove_all(make_array([1, 2, 3], [4, 5, 6], [5, 5, 5], [4, 5, 6], [7, 8, 9]), [4, 5, 6]), array_remove_all(make_array([1, 3, 2], [2, 3, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]), [2, 3, 4]);
----
[[1, 2, 3], [5, 5, 5], [7, 8, 9]] [[1, 3, 2], [5, 3, 1], [1, 3, 2]]

query ??
select array_remove_all(arrow_cast(make_array([1, 2, 3], [4, 5, 6], [5, 5, 5], [4, 5, 6], [7, 8, 9]), 'FixedSizeList(5, List(Int64))'), [4, 5, 6]),
       array_remove_all(arrow_cast(make_array([1, 3, 2], [2, 3, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]),  'FixedSizeList(5, List(Int64))'), [2, 3, 4]);
----
[[1, 2, 3], [5, 5, 5], [7, 8, 9]] [[1, 3, 2], [5, 3, 1], [1, 3, 2]]

# list_remove_all scalar function #3 (function alias `array_remove_all`)
query ???
select list_remove_all(make_array(1, 2, 2, 1, 1), 2), list_remove_all(make_array(1.0, 2.0, 2.0, 1.0, 1.0), 1.0), list_remove_all(make_array('h', 'e', 'l', 'l', 'o'), 'l');
----
[1, 1, 1] [2.0, 2.0] [h, e, o]

query ??
select list_remove_all(arrow_cast(make_array([1, 2, 3], [4, 5, 6], [5, 5, 5], [4, 5, 6], [7, 8, 9]), 'FixedSizeList(5, List(Int64))'), [4, 5, 6]),
       list_remove_all(arrow_cast(make_array([1, 3, 2], [2, 3, 4], [2, 3, 4], [5, 3, 1], [1, 3, 2]),  'FixedSizeList(5, List(Int64))'), [2, 3, 4]);
----
[[1, 2, 3], [5, 5, 5], [7, 8, 9]] [[1, 3, 2], [5, 3, 1], [1, 3, 2]]

# array_remove_all scalar function with columns #1
query ?
select array_remove_all(column1, column2) from arrays_with_repeating_elements;
----
[1, 1, 3, 1, 3, 3]
[5, 5, 6, 5, 5, 5]
[8, 9, 8]
[11, 12, 11, 12, 11, 12]

query ?
select array_remove_all(column1, column2) from fixed_arrays_with_repeating_elements;
----
[1, 1, 3, 1, 3, 3]
[5, 5, 6, 5, 5, 5]
[8, 9, 8]
[11, 12, 11, 12, 11, 12]

# array_remove_all scalar function with columns #2 (element is list)
query ?
select array_remove_all(column1, column2) from nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [1, 2, 3], [7, 8, 9], [1, 2, 3], [7, 8, 9], [7, 8, 9]]
[[13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15]]
[[22, 23, 24], [25, 26, 27], [22, 23, 24]]
[[31, 32, 33], [34, 35, 36], [31, 32, 33], [34, 35, 36], [31, 32, 33], [34, 35, 36]]

query ?
select array_remove_all(column1, column2) from fixed_size_nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [1, 2, 3], [7, 8, 9], [1, 2, 3], [7, 8, 9], [7, 8, 9]]
[[13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15]]
[[22, 23, 24], [25, 26, 27], [22, 23, 24]]
[[31, 32, 33], [34, 35, 36], [31, 32, 33], [34, 35, 36], [31, 32, 33], [34, 35, 36]]

# array_remove_all scalar function with columns and scalars #1
query ??
select array_remove_all(make_array(1, 2, 2, 4, 5, 4, 4, 7, 7, 10, 7, 8), column2), array_remove_all(column1, 1) from arrays_with_repeating_elements;
----
[1, 4, 5, 4, 4, 7, 7, 10, 7, 8] [2, 3, 2, 2, 3, 2, 3]
[1, 2, 2, 5, 7, 7, 10, 7, 8] [4, 4, 5, 5, 6, 5, 5, 5, 4, 4]
[1, 2, 2, 4, 5, 4, 4, 10, 8] [7, 7, 7, 8, 7, 9, 7, 8, 7, 7]
[1, 2, 2, 4, 5, 4, 4, 7, 7, 7, 8] [10, 11, 12, 10, 11, 12, 10, 11, 12, 10]

query ??
select array_remove_all(make_array(1, 2, 2, 4, 5, 4, 4, 7, 7, 10, 7, 8), column2), array_remove_all(column1, 1) from fixed_arrays_with_repeating_elements;
----
[1, 4, 5, 4, 4, 7, 7, 10, 7, 8] [2, 3, 2, 2, 3, 2, 3]
[1, 2, 2, 5, 7, 7, 10, 7, 8] [4, 4, 5, 5, 6, 5, 5, 5, 4, 4]
[1, 2, 2, 4, 5, 4, 4, 10, 8] [7, 7, 7, 8, 7, 9, 7, 8, 7, 7]
[1, 2, 2, 4, 5, 4, 4, 7, 7, 7, 8] [10, 11, 12, 10, 11, 12, 10, 11, 12, 10]

# array_remove_all scalar function with columns and scalars #2 (element is list)
query ??
select array_remove_all(make_array([1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]), column2), array_remove_all(column1, make_array(1, 2, 3)) from nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[4, 5, 6], [7, 8, 9], [4, 5, 6], [4, 5, 6], [7, 8, 9], [4, 5, 6], [7, 8, 9]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [13, 14, 15], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[10, 11, 12], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [28, 29, 30], [22, 23, 24]] [[19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24]] [[28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]]

query ??
select array_remove_all(make_array([1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]), column2),
       array_remove_all(column1, make_array(1, 2, 3)) from fixed_size_nested_arrays_with_repeating_elements;
----
[[1, 2, 3], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[4, 5, 6], [7, 8, 9], [4, 5, 6], [4, 5, 6], [7, 8, 9], [4, 5, 6], [7, 8, 9]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [13, 14, 15], [19, 20, 21], [19, 20, 21], [28, 29, 30], [19, 20, 21], [22, 23, 24]] [[10, 11, 12], [10, 11, 12], [13, 14, 15], [13, 14, 15], [16, 17, 18], [13, 14, 15], [13, 14, 15], [13, 14, 15], [10, 11, 12], [10, 11, 12]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [28, 29, 30], [22, 23, 24]] [[19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24], [19, 20, 21], [25, 26, 27], [19, 20, 21], [22, 23, 24], [19, 20, 21], [19, 20, 21]]
[[1, 2, 3], [4, 5, 6], [4, 5, 6], [10, 11, 12], [13, 14, 15], [10, 11, 12], [10, 11, 12], [19, 20, 21], [19, 20, 21], [19, 20, 21], [22, 23, 24]] [[28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30], [31, 32, 33], [34, 35, 36], [28, 29, 30]]

## trim_array (deprecated)

## array_length (aliases: `list_length`)

# array_length scalar function #1
query III
select array_length(make_array(1, 2, 3, 4, 5)), array_length(make_array(1, 2, 3)), array_length(make_array([1, 2], [3, 4], [5, 6]));
----
5 3 3

query III
select array_length(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)')), array_length(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)')), array_length(arrow_cast(make_array([1, 2], [3, 4], [5, 6]), 'LargeList(List(Int64))'));
----
5 3 3

# array_length scalar function #2
query III
select array_length(make_array(1, 2, 3, 4, 5), 1), array_length(make_array(1, 2, 3), 1), array_length(make_array([1, 2], [3, 4], [5, 6]), 1);
----
5 3 3

query III
select array_length(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 1), array_length(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)'), 1), array_length(arrow_cast(make_array([1, 2], [3, 4], [5, 6]), 'LargeList(List(Int64))'), 1);
----
5 3 3

# array_length scalar function #3
query III
select array_length(make_array(1, 2, 3, 4, 5), 2), array_length(make_array(1, 2, 3), 2), array_length(make_array([1, 2], [3, 4], [5, 6]), 2);
----
NULL NULL 2

query III
select array_length(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)'), 2), array_length(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)'), 2), array_length(arrow_cast(make_array([1, 2], [3, 4], [5, 6]), 'LargeList(List(Int64))'), 2);
----
NULL NULL 2

# array_length scalar function #4
query II
select array_length(array_repeat(array_repeat(array_repeat(3, 5), 2), 3), 1), array_length(array_repeat(array_repeat(array_repeat(3, 5), 2), 3), 2);
----
3 2

query II
select array_length(arrow_cast(array_repeat(array_repeat(array_repeat(3, 5), 2), 3), 'LargeList(List(List(Int64)))'), 1), array_length(arrow_cast(array_repeat(array_repeat(array_repeat(3, 5), 2), 3), 'LargeList(List(List(Int64)))'), 2);
----
3 2

# array_length scalar function #5
query III
select array_length(make_array()), array_length(make_array(), 1), array_length(make_array(), 2)
----
0 0 NULL

# array_length scalar function #6 nested array
query III
select array_length([[1, 2, 3, 4], [5, 6, 7, 8]]), array_length([[1, 2, 3, 4], [5, 6, 7, 8]], 1), array_length([[1, 2, 3, 4], [5, 6, 7, 8]], 2);
----
2 2 4

# list_length scalar function #7 (function alias `array_length`)
query IIII
select list_length(make_array(1, 2, 3, 4, 5)), list_length(make_array(1, 2, 3)), list_length(make_array([1, 2], [3, 4], [5, 6])), array_length([[1, 2, 3, 4], [5, 6, 7, 8]], 3);
----
5 3 3 NULL

query III
select list_length(arrow_cast(make_array(1, 2, 3, 4, 5), 'LargeList(Int64)')), list_length(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)')), list_length(arrow_cast(make_array([1, 2], [3, 4], [5, 6]), 'LargeList(List(Int64))'));
----
5 3 3

# array_length with columns
query I
select array_length(column1, column3) from arrays_values;
----
10
NULL
NULL
NULL
NULL
NULL
NULL
NULL

query I
select array_length(arrow_cast(column1, 'LargeList(Int64)'), column3) from arrays_values;
----
10
NULL
NULL
NULL
NULL
NULL
NULL
NULL

# array_length with columns and scalars
query II
select array_length(array[array[1, 2], array[3, 4]], column3), array_length(column1, 1) from arrays_values;
----
2 10
2 10
NULL 10
NULL 10
NULL NULL
NULL 10
NULL 10
NULL 10

query II
select array_length(arrow_cast(array[array[1, 2], array[3, 4]], 'LargeList(List(Int64))'), column3), array_length(arrow_cast(column1, 'LargeList(Int64)'), 1) from arrays_values;
----
2 10
2 10
NULL 10
NULL 10
NULL NULL
NULL 10
NULL 10
NULL 10

query RRR
select array_distance([2], [3]), list_distance([1], [2]), list_distance([1], [-2]);
----
1 1 3

query error
select list_distance([1], [1, 2]);

query R
select array_distance([[1, 1]], [1, 2]);
----
1

query R
select array_distance([[1, 1]], [[1, 2]]);
----
1

query R
select array_distance([[1, 1]], [[1, 2]]);
----
1

query RR
select array_distance([1, 1, 0, 0], [2, 2, 1, 1]), list_distance([1, 2, 3], [1, 2, 3]);
----
2 0

query RR
select array_distance([1.0, 1, 0, 0], [2, 2.0, 1, 1]), list_distance([1, 2.0, 3], [1, 2, 3]);
----
2 0

query R
select list_distance([1, 1, NULL, 0], [2, 2, NULL, NULL]);
----
NULL

query R
select list_distance([NULL, NULL], [NULL, NULL]);
----
NULL

query R
select list_distance([1.0, 2.0, 3.0], [1.0, 2.0, 3.5]) AS distance;
----
0.5

query R
select list_distance([1, 2, 3], [1, 2, 3]) AS distance;
----
0

# array_distance with columns
query RRR
select array_distance(column1, column2), array_distance(column1, column3), array_distance(column1, column4) from arrays_distance_table;
----
0 0.374165738677 NULL
5.196152422707 6.063827174318 NULL
10.392304845413 11.778794505381 NULL
15.58845726812 15.935494971917 NULL

query RRR
select array_distance(column1, column2), array_distance(column1, column3), array_distance(column1, column4) from large_arrays_distance_table;
----
0 0.374165738677 NULL
5.196152422707 6.063827174318 NULL
10.392304845413 11.778794505381 NULL
15.58845726812 15.935494971917 NULL

query RRR
select array_distance(column1, column2), array_distance(column1, column3), array_distance(column1, column4) from fixed_size_arrays_distance_table;
----
0 0.374165738677 NULL
5.196152422707 6.063827174318 NULL
10.392304845413 11.778794505381 NULL
15.58845726812 15.935494971917 NULL


## array_dims (aliases: `list_dims`)

# array dims error
query error
select array_dims(1);

# array_dims scalar function
query ???
select array_dims(make_array(1, 2, 3)), array_dims(make_array([1, 2], [3, 4])), array_dims(make_array([[[[1], [2]]]]));
----
[3] [2, 2] [1, 1, 1, 2, 1]

query ???
select array_dims(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)')), array_dims(arrow_cast(make_array([1, 2], [3, 4]), 'LargeList(List(Int64))')), array_dims(arrow_cast(make_array([[[[1], [2]]]]), 'LargeList(List(List(List(List(Int64)))))'));
----
[3] [2, 2] [1, 1, 1, 2, 1]

query ???
select array_dims(arrow_cast(make_array(1, 2, 3), 'FixedSizeList(3, Int64)')), array_dims(arrow_cast(make_array([1, 2], [3, 4]), 'FixedSizeList(2, List(Int64))')), array_dims(arrow_cast(make_array([[[[1], [2]]]]), 'FixedSizeList(1, List(List(List(List(Int64)))))'));
----
[3] [2, 2] [1, 1, 1, 2, 1]

# array_dims scalar function #2
query ??
select array_dims(array_repeat(array_repeat(array_repeat(2, 3), 2), 1)), array_dims(array_repeat(array_repeat(array_repeat(3, 4), 5), 2));
----
[1, 2, 3] [2, 5, 4]

query ??
select array_dims(arrow_cast(array_repeat(array_repeat(array_repeat(2, 3), 2), 1), 'LargeList(List(List(Int64)))')), array_dims(arrow_cast(array_repeat(array_repeat(array_repeat(3, 4), 5), 2), 'LargeList(List(List(Int64)))'));
----
[1, 2, 3] [2, 5, 4]

query ??
select array_dims(arrow_cast(array_repeat(array_repeat(array_repeat(2, 3), 2), 1), 'FixedSizeList(1, List(List(Int64)))')), array_dims(arrow_cast(array_repeat(array_repeat(array_repeat(3, 4), 5), 2), 'FixedSizeList(2, List(List(Int64)))'));
----
[1, 2, 3] [2, 5, 4]

# array_dims scalar function #3
query ??
select array_dims(make_array()), array_dims(make_array(make_array()))
----
NULL [1, 0]

query ??
select array_dims(arrow_cast(make_array(), 'LargeList(Int64)')), array_dims(arrow_cast(make_array(make_array()), 'LargeList(List(Int64))'))
----
NULL [1, 0]

# list_dims scalar function #4 (function alias `array_dims`)
query ???
select list_dims(make_array(1, 2, 3)), list_dims(make_array([1, 2], [3, 4])), list_dims(make_array([[[[1], [2]]]]));
----
[3] [2, 2] [1, 1, 1, 2, 1]

query ???
select list_dims(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)')), list_dims(arrow_cast(make_array([1, 2], [3, 4]), 'LargeList(List(Int64))')), list_dims(arrow_cast(make_array([[[[1], [2]]]]), 'LargeList(List(List(List(List(Int64)))))'));
----
[3] [2, 2] [1, 1, 1, 2, 1]

query ???
select list_dims(arrow_cast(make_array(1, 2, 3), 'FixedSizeList(3, Int64)')), list_dims(arrow_cast(make_array([1, 2], [3, 4]), 'FixedSizeList(2, List(Int64))')), list_dims(arrow_cast(make_array([[[[1], [2]]]]), 'FixedSizeList(1, List(List(List(List(Int64)))))'));
----
[3] [2, 2] [1, 1, 1, 2, 1]

# array_dims with columns
query ???
select array_dims(column1), array_dims(column2), array_dims(column3) from arrays;
----
[2, 2] [3] [5]
[2, 2] [3] [5]
[2, 2] [3] [5]
[2, 2] [3] [3]
NULL [3] [4]
[2, 2] NULL [1]
[2, 2] [3] NULL

query ???
select array_dims(column1), array_dims(column2), array_dims(column3) from large_arrays;
----
[2, 2] [3] [5]
[2, 2] [3] [5]
[2, 2] [3] [5]
[2, 2] [3] [3]
NULL [3] [4]
[2, 2] NULL [1]
[2, 2] [3] NULL

query ???
select array_dims(column1), array_dims(column2), array_dims(column3) from fixed_size_arrays;
----
[2, 2] [3] [5]
[2, 2] [3] [5]
[2, 2] [3] [5]
[2, 2] [3] [5]
NULL [3] [5]
[2, 2] NULL [5]
[2, 2] [3] NULL


## array_ndims (aliases: `list_ndims`)

# array_ndims scalar function #1

#follow PostgreSQL
query I
select
  array_ndims(null);
----
NULL

query I
select
  array_ndims([2, 3]);
----
1

statement ok
CREATE TABLE array_ndims_table
AS VALUES
  ([1], [1, 2, 3], [[7]], [[[[[10]]]]]),
  ([2], [4, 5], [[8]], [[[[[10]]]]]),
  (NUll, [6, 7], [[9]], [[[[[10]]]]]),
  ([3], [6], [[9]], [[[[[10]]]]])
;

statement ok
CREATE TABLE large_array_ndims_table
AS SELECT
  column1,
  arrow_cast(column2, 'LargeList(Int64)') as column2,
  arrow_cast(column3, 'LargeList(List(Int64))') as column3,
  arrow_cast(column4, 'LargeList(List(List(List(List(Int64)))))') as column4
FROM array_ndims_table;

statement ok
CREATE TABLE fixed_array_ndims_table
AS VALUES
  (arrow_cast([1], 'FixedSizeList(1, Int64)'), arrow_cast([1, 2, 3], 'FixedSizeList(3, Int64)'), arrow_cast([[7]], 'FixedSizeList(1, List(Int64))'), arrow_cast([[[[[10]]]]], 'FixedSizeList(1, List(List(List(List(Int64)))))')),
  (arrow_cast([2], 'FixedSizeList(1, Int64)'), arrow_cast([4, 5, 6], 'FixedSizeList(3, Int64)'), arrow_cast([[8]], 'FixedSizeList(1, List(Int64))'), arrow_cast([[[[[10]]]]], 'FixedSizeList(1, List(List(List(List(Int64)))))')),
  (null, arrow_cast([6, 7, 8], 'FixedSizeList(3, Int64)'), arrow_cast([[9]], 'FixedSizeList(1, List(Int64))'), arrow_cast([[[[[10]]]]], 'FixedSizeList(1, List(List(List(List(Int64)))))')),
  (arrow_cast([3], 'FixedSizeList(1, Int64)'), arrow_cast([6, 7, 8], 'FixedSizeList(3, Int64)'), arrow_cast([[9]], 'FixedSizeList(1, List(Int64))'), arrow_cast([[[[[10]]]]], 'FixedSizeList(1, List(List(List(List(Int64)))))'))
;

query IIII
select
  array_ndims(column1),
  array_ndims(column2),
  array_ndims(column3),
  array_ndims(column4)
from array_ndims_table;
----
1 1 2 5
1 1 2 5
NULL 1 2 5
1 1 2 5

query IIII
select
  array_ndims(column1),
  array_ndims(column2),
  array_ndims(column3),
  array_ndims(column4)
from large_array_ndims_table;
----
1 1 2 5
1 1 2 5
NULL 1 2 5
1 1 2 5

query IIII
select
  array_ndims(column1),
  array_ndims(column2),
  array_ndims(column3),
  array_ndims(column4)
from fixed_array_ndims_table;
----
1 1 2 5
1 1 2 5
NULL 1 2 5
1 1 2 5



statement ok
drop table array_ndims_table;

statement ok
drop table large_array_ndims_table

query I
select array_ndims(arrow_cast([null], 'List(List(List(Int64)))'));
----
3

# array_ndims scalar function #2
query II
select array_ndims(array_repeat(array_repeat(array_repeat(1, 3), 2), 1)), array_ndims([[[[[[[[[[[[[[[[[[[[[1]]]]]]]]]]]]]]]]]]]]]);
----
3 21

# array_ndims scalar function #3
query II
select array_ndims(make_array()), array_ndims(make_array(make_array()))
----
1 2

query II
select array_ndims(arrow_cast(make_array(), 'LargeList(Int64)')), array_ndims(arrow_cast(make_array(make_array()), 'LargeList(List(Int64))'))
----
1 2

# list_ndims scalar function #4 (function alias `array_ndims`)
query III
select list_ndims(make_array(1, 2, 3)), list_ndims(make_array([1, 2], [3, 4])), list_ndims(make_array([[[[1], [2]]]]));
----
1 2 5

query III
select list_ndims(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)')), list_ndims(arrow_cast(make_array([1, 2], [3, 4]), 'LargeList(List(Int64))')), list_ndims(arrow_cast(make_array([[[[1], [2]]]]), 'LargeList(List(List(List(List(Int64)))))'));
----
1 2 5

query II
select list_ndims(make_array()), list_ndims(make_array(make_array()))
----
1 2

query II
select list_ndims(arrow_cast(make_array(), 'LargeList(Int64)')), list_ndims(arrow_cast(make_array(make_array()), 'LargeList(List(Int64))'))
----
1 2

# array_ndims with columns
query III
select array_ndims(column1), array_ndims(column2), array_ndims(column3) from arrays;
----
2 1 1
2 1 1
2 1 1
2 1 1
NULL 1 1
2 NULL 1
2 1 NULL

query III
select array_ndims(column1), array_ndims(column2), array_ndims(column3) from large_arrays;
----
2 1 1
2 1 1
2 1 1
2 1 1
NULL 1 1
2 NULL 1
2 1 NULL

## array_has/array_has_all/array_has_any

# If lhs is empty, return false
query B
select array_has([], 1);
----
false

# If rhs is Null, we returns Null
query BBB
select array_has([], null),
       array_has([1, 2, 3], null),
       array_has([null, 1], null);
----
NULL NULL NULL

# Always return false if not contained even if list has null elements
query BB
select array_has([1, null, 2], 3),
       array_has([null, null, null], 3);
----
false false

#TODO: array_has_all and array_has_any cannot handle NULL
#query BBBB
#select array_has_any([], null),
#       array_has_any([1, 2, 3], null),
#       array_has_all([], null),
#       array_has_all([1, 2, 3], null);
#----
#false false false false

query BBBBBBBBBBBB
select array_has(make_array(1,2), 1),
       array_has(make_array(1,2,NULL), 1),
       array_has(make_array([2,3], [3,4]), make_array(2,3)),
       array_has(make_array([[1], [2,3]], [[4,5], [6]]), make_array([1], [2,3])),
       array_has(make_array([[1], [2,3]], [[4,5], [6]]), make_array([4,5], [6])),
       array_has(make_array([[1], [2,3]], [[4,5], [6]]), make_array([1])),
       array_has(make_array([[[1]]]), make_array([[1]])),
       array_has(make_array([[[1]]], [[[1], [2]]]), make_array([[2]])),
       array_has(make_array([[[1]]], [[[1], [2]]]), make_array([[1], [2]])),
       list_has(make_array(1,2,3), 4),
       array_contains(make_array(1,2,3), 3),
       list_contains(make_array(1,2,3), 0)
;
----
true true true true true false true false true false true false

query BBBBBBBBBBBB
select array_has(arrow_cast(make_array(1,2), 'LargeList(Int64)'), 1),
       array_has(arrow_cast(make_array(1,2,NULL), 'LargeList(Int64)'), 1),
       array_has(arrow_cast(make_array([2,3], [3,4]), 'LargeList(List(Int64))'), make_array(2,3)),
       array_has(arrow_cast(make_array([[1], [2,3]], [[4,5], [6]]), 'LargeList(List(List(Int64)))'), make_array([1], [2,3])),
       array_has(arrow_cast(make_array([[1], [2,3]], [[4,5], [6]]), 'LargeList(List(List(Int64)))'), make_array([4,5], [6])),
       array_has(arrow_cast(make_array([[1], [2,3]], [[4,5], [6]]), 'LargeList(List(List(Int64)))'), make_array([1])),
       array_has(arrow_cast(make_array([[[1]]]), 'LargeList(List(List(List(Int64))))'), make_array([[1]])),
       array_has(arrow_cast(make_array([[[1]]], [[[1], [2]]]), 'LargeList(List(List(List(Int64))))'), make_array([[2]])),
       array_has(arrow_cast(make_array([[[1]]], [[[1], [2]]]), 'LargeList(List(List(List(Int64))))'), make_array([[1], [2]])),
       list_has(arrow_cast(make_array(1,2,3), 'LargeList(Int64)'), 4),
       array_contains(arrow_cast(make_array(1,2,3), 'LargeList(Int64)'), 3),
       list_contains(arrow_cast(make_array(1,2,3), 'LargeList(Int64)'), 0)
;
----
true true true true true false true false true false true false

query BBBBBBBBBBBB
select array_has(arrow_cast(make_array(1,2), 'FixedSizeList(2, Int64)'), 1),
       array_has(arrow_cast(make_array(1,2,NULL), 'FixedSizeList(3, Int64)'), 1),
       array_has(arrow_cast(make_array([2,3], [3,4]), 'FixedSizeList(2, List(Int64))'), make_array(2,3)),
       array_has(arrow_cast(make_array([[1], [2,3]], [[4,5], [6]]), 'FixedSizeList(2, List(List(Int64)))'), make_array([1], [2,3])),
       array_has(arrow_cast(make_array([[1], [2,3]], [[4,5], [6]]), 'FixedSizeList(2, List(List(Int64)))'), make_array([4,5], [6])),
       array_has(arrow_cast(make_array([[1], [2,3]], [[4,5], [6]]), 'FixedSizeList(2, List(List(Int64)))'), make_array([1])),
       array_has(arrow_cast(make_array([[[1]]]), 'FixedSizeList(1, List(List(List(Int64))))'), make_array([[1]])),
       array_has(arrow_cast(make_array([[[1]]], [[[1], [2]]]), 'FixedSizeList(2, List(List(List(Int64))))'), make_array([[2]])),
       array_has(arrow_cast(make_array([[[1]]], [[[1], [2]]]), 'FixedSizeList(2, List(List(List(Int64))))'), make_array([[1], [2]])),
       list_has(arrow_cast(make_array(1,2,3), 'FixedSizeList(3, Int64)'), 4),
       array_contains(arrow_cast(make_array(1,2,3), 'FixedSizeList(3, Int64)'), 3),
       list_contains(arrow_cast(make_array(1,2,3), 'FixedSizeList(3, Int64)'), 0)
;
----
true true true true true false true false true false true false

query BBB
select array_has(column1, column2),
       array_has_all(column3, column4),
       array_has_any(column5, column6)
from array_has_table_1D;
----
true true true
false false false

query BBB
select array_has(arrow_cast(column1, 'LargeList(Int64)'), column2),
       array_has_all(arrow_cast(column3, 'LargeList(Int64)'), arrow_cast(column4, 'LargeList(Int64)')),
       array_has_any(arrow_cast(column5, 'LargeList(Int64)'), arrow_cast(column6, 'LargeList(Int64)'))
from array_has_table_1D;
----
true true true
false false false

query B
select array_has(column1, column2)
from array_has_table_null;
----
true
true
false
false
false

query B
select array_has(column1, column2)
from fixed_size_array_has_table_1D;
----
true
false

#TODO: array_has_all and array_has_any cannot handle FixedSizeList
#query BB
#select array_has_all(column3, column4),
#       array_has_any(column5, column6)
#from fixed_size_array_has_table_1D;
#----
#true true
#false false

query BBB
select array_has(column1, column2),
       array_has_all(column3, column4),
       array_has_any(column5, column6)
from array_has_table_1D_Float;
----
true true false
false false true

query BBB
select array_has(arrow_cast(column1, 'LargeList(Float64)'), column2),
       array_has_all(arrow_cast(column3, 'LargeList(Float64)'), arrow_cast(column4, 'LargeList(Float64)')),
       array_has_any(arrow_cast(column5, 'LargeList(Float64)'), arrow_cast(column6, 'LargeList(Float64)'))
from array_has_table_1D_Float;
----
true true false
false false true

query B
select array_has(column1, column2)
from fixed_size_array_has_table_1D_Float;
----
true
false

#TODO: array_has_all and array_has_any cannot handle FixedSizeList
#query BB
#select array_has_all(column3, column4),
#       array_has_any(column5, column6)
#from fixed_size_array_has_table_1D_Float;
#----
#true true
#false true

query BBB
select array_has(column1, column2),
       array_has_all(column3, column4),
       array_has_any(column5, column6)
from array_has_table_1D_Boolean;
----
false true true
true true true

query BBB
select array_has(arrow_cast(column1, 'LargeList(Boolean)'), column2),
       array_has_all(arrow_cast(column3, 'LargeList(Boolean)'), arrow_cast(column4, 'LargeList(Boolean)')),
       array_has_any(arrow_cast(column5, 'LargeList(Boolean)'), arrow_cast(column6, 'LargeList(Boolean)'))
from array_has_table_1D_Boolean;
----
false true true
true true true

query B
select array_has(column1, column2)
from fixed_size_array_has_table_1D_Boolean;
----
false
true

#TODO: array_has_all and array_has_any cannot handle FixedSizeList
#query BB
#select array_has_all(column3, column4),
#       array_has_any(column5, column6)
#from fixed_size_array_has_table_1D_Boolean;
#----
#true true
#true true

query BBB
select array_has(column1, column2),
       array_has_all(column3, column4),
       array_has_any(column5, column6)
from array_has_table_1D_UTF8;
----
true true false
false false true

query BBB
select array_has(arrow_cast(column1, 'LargeList(Utf8)'), column2),
       array_has_all(arrow_cast(column3, 'LargeList(Utf8)'), arrow_cast(column4, 'LargeList(Utf8)')),
       array_has_any(arrow_cast(column5, 'LargeList(Utf8)'), arrow_cast(column6, 'LargeList(Utf8)'))
from array_has_table_1D_UTF8;
----
true true false
false false true

query B
select array_has(column1, column2)
from fixed_size_array_has_table_1D_UTF8;
----
true
false

query BB
select array_has(column1, column2),
       array_has_all(column3, column4)
from array_has_table_2D;
----
false true
true false

query BB
select array_has(arrow_cast(column1, 'LargeList(List(Int64))'), column2),
       array_has_all(arrow_cast(column3, 'LargeList(List(Int64))'), arrow_cast(column4, 'LargeList(List(Int64))'))
from array_has_table_2D;
----
false true
true false

query B
select array_has(arrow_cast(column1, 'LargeList(List(Int64))'), column2)
from fixed_size_array_has_table_2D;
----
false
false

#TODO: array_has_all and array_has_any cannot handle FixedSizeList
#query B
#select array_has_all(arrow_cast(column3, 'LargeList(List(Int64))'), arrow_cast(column4, 'LargeList(List(Int64))'))
#from fixed_size_array_has_table_2D;
#----
#true
#false

query B
select array_has_all(column1, column2)
from array_has_table_2D_float;
----
true
false

query B
select array_has_all(arrow_cast(column1, 'LargeList(List(Float64))'), arrow_cast(column2, 'LargeList(List(Float64))'))
from array_has_table_2D_float;
----
true
false

#TODO: array_has_all and array_has_any cannot handle FixedSizeList
#query B
#select array_has_all(column1, column2)
#from fixed_size_array_has_table_2D_float;
#----
#false
#false

query B
select array_has(column1, column2) from array_has_table_3D;
----
false
true
false
false
true
false
true

query B
select array_has(arrow_cast(column1, 'LargeList(List(List(Int64)))'), column2) from array_has_table_3D;
----
false
true
false
false
true
false
true

query B
select array_has(column1, column2) from fixed_size_array_has_table_3D;
----
false
false
false
false
true
true
true

query BBBB
select array_has(column1, make_array(5, 6)),
       array_has(column1, make_array(7, NULL)),
       array_has(column2, 5.5),
       array_has(column3, 'o')
from arrays;
----
false false false true
true false true false
true false false true
false true false false
NULL NULL false false
false false NULL false
false false false NULL

query BBBB
select array_has(arrow_cast(column1, 'LargeList(List(Int64))'), make_array(5, 6)),
       array_has(arrow_cast(column1, 'LargeList(List(Int64))'), make_array(7, NULL)),
       array_has(arrow_cast(column2, 'LargeList(Float64)'), 5.5),
       array_has(arrow_cast(column3, 'LargeList(Utf8)'), 'o')
from arrays;
----
false false false true
true false true false
true false false true
false true false false
NULL NULL false false
false false NULL false
false false false NULL

query BBBB
select array_has(column1, make_array(5, 6)),
       array_has(column1, make_array(7, NULL)),
       array_has(column2, 5.5),
       array_has(column3, 'o')
from fixed_size_arrays;
----
false false false true
true false true false
true false false true
false true false false
false false false false
false false false false
false false false false

query BBBB
select array_has_all(make_array(1,2,3), []),
       array_has_any(make_array(1,2,3), []),
       array_has_all(make_array('aa','bb','cc'), []),
       array_has_any(make_array('aa','bb','cc'), [])
;
----
true false true false

query BBBBBBBBBBBBB
select array_has_all(make_array(1,2,3), make_array(1,3)),
       array_has_all(make_array(1,2,3), make_array(1,4)),
       array_has_all(make_array([1,2], [3,4]), make_array([1,2])),
       array_has_all(make_array([1,2], [3,4]), make_array([1,3])),
       array_has_all(make_array([1,2], [3,4]), make_array([1,2], [3,4], [5,6])),
       array_has_all(make_array([[1,2,3]]), make_array([[1]])),
       array_has_all(make_array([[1,2,3]]), make_array([[1,2,3]])),
       array_has_any(make_array(1,2,3), make_array(1,10,100)),
       array_has_any(make_array(1,2,3), make_array(10,100)),
       array_has_any(make_array([1,2], [3,4]), make_array([1,10], [10,4])),
       array_has_any(make_array([1,2], [3,4]), make_array([10,20], [3,4])),
       array_has_any(make_array([[1,2,3]]), make_array([[1,2,3], [4,5,6]])),
       array_has_any(make_array([[1,2,3]]), make_array([[1,2,3]], [[4,5,6]]))
;
----
true false true false false false true true false false true false true

query BBBBBBBBBBBBB
select array_has_all(arrow_cast(make_array(1,2,3), 'LargeList(Int64)'), arrow_cast(make_array(1,3), 'LargeList(Int64)')),
       array_has_all(arrow_cast(make_array(1,2,3),'LargeList(Int64)'), arrow_cast(make_array(1,4), 'LargeList(Int64)')),
       array_has_all(arrow_cast(make_array([1,2], [3,4]), 'LargeList(List(Int64))'), arrow_cast(make_array([1,2]), 'LargeList(List(Int64))')),
       array_has_all(arrow_cast(make_array([1,2], [3,4]), 'LargeList(List(Int64))'), arrow_cast(make_array([1,3]), 'LargeList(List(Int64))')),
       array_has_all(arrow_cast(make_array([1,2], [3,4]), 'LargeList(List(Int64))'), arrow_cast(make_array([1,2], [3,4], [5,6]), 'LargeList(List(Int64))')),
       array_has_all(arrow_cast(make_array([[1,2,3]]), 'LargeList(List(List(Int64)))'), arrow_cast(make_array([[1]]), 'LargeList(List(List(Int64)))')),
       array_has_all(arrow_cast(make_array([[1,2,3]]), 'LargeList(List(List(Int64)))'), arrow_cast(make_array([[1,2,3]]), 'LargeList(List(List(Int64)))')),
       array_has_any(arrow_cast(make_array(1,2,3),'LargeList(Int64)'), arrow_cast(make_array(1,10,100), 'LargeList(Int64)')),
       array_has_any(arrow_cast(make_array(1,2,3),'LargeList(Int64)'), arrow_cast(make_array(10,100),'LargeList(Int64)')),
       array_has_any(arrow_cast(make_array([1,2], [3,4]), 'LargeList(List(Int64))'), arrow_cast(make_array([1,10], [10,4]), 'LargeList(List(Int64))')),
       array_has_any(arrow_cast(make_array([1,2], [3,4]), 'LargeList(List(Int64))'), arrow_cast(make_array([10,20], [3,4]), 'LargeList(List(Int64))')),
       array_has_any(arrow_cast(make_array([[1,2,3]]), 'LargeList(List(List(Int64)))'), arrow_cast(make_array([[1,2,3], [4,5,6]]), 'LargeList(List(List(Int64)))')),
       array_has_any(arrow_cast(make_array([[1,2,3]]), 'LargeList(List(List(Int64)))'), arrow_cast(make_array([[1,2,3]], [[4,5,6]]), 'LargeList(List(List(Int64)))'))
;
----
true false true false false false true true false false true false true

#TODO: array_has_all and array_has_any cannot handle FixedSizeList
#query BBBBBBBBBBBBB
#select array_has_all(arrow_cast(make_array(1,2,3), 'FixedSizeList(3, Int64)'), arrow_cast(make_array(1, 3), 'FixedSizeList(2, Int64)')),
#       array_has_all(arrow_cast(make_array(1,2,3),'FixedSizeList(3, Int64)'), arrow_cast(make_array(1, 4), 'FixedSizeList(2, Int64)')),
#       array_has_all(arrow_cast(make_array([1,2], [3,4]), 'FixedSizeList(2, List(Int64))'), arrow_cast(make_array([1,2]), 'FixedSizeList(1, List(Int64))')),
#       array_has_all(arrow_cast(make_array([1,2], [3,4]), 'FixedSizeList(2, List(Int64))'), arrow_cast(make_array([1,3]), 'FixedSizeList(1, List(Int64))')),
#       array_has_all(arrow_cast(make_array([1,2], [3,4]), 'FixedSizeList(2, List(Int64))'), arrow_cast(make_array([1,2], [3,4], [5,6]), 'FixedSizeList(3, List(Int64))')),
#       array_has_all(arrow_cast(make_array([[1,2,3]]), 'FixedSizeList(1, List(List(Int64)))'), arrow_cast(make_array([[1]]), 'FixedSizeList(1, List(List(Int64)))')),
#       array_has_all(arrow_cast(make_array([[1,2,3]]), 'FixedSizeList(1, List(List(Int64)))'), arrow_cast(make_array([[1,2,3]]), 'FixedSizeList(1, List(List(Int64)))')),
#       array_has_any(arrow_cast(make_array(1,2,3),'FixedSizeList(3, Int64)'), arrow_cast(make_array(1,10,100), 'FixedSizeList(3, Int64)')),
#       array_has_any(arrow_cast(make_array(1,2,3),'FixedSizeList(3, Int64)'), arrow_cast(make_array(10, 100),'FixedSizeList(2, Int64)')),
#       array_has_any(arrow_cast(make_array([1,2], [3,4]), 'FixedSizeList(2, List(Int64))'), arrow_cast(make_array([1,10], [10,4]), 'FixedSizeList(2, List(Int64))')),
#       array_has_any(arrow_cast(make_array([1,2], [3,4]), 'FixedSizeList(2, List(Int64))'), arrow_cast(make_array([10,20], [3,4]), 'FixedSizeList(2, List(Int64))')),
#       array_has_any(arrow_cast(make_array([[1,2,3]]), 'FixedSizeList(1, List(List(Int64)))'), arrow_cast(make_array([[1,2,3], [4,5,6]]), 'FixedSizeList(1, List(List(Int64)))')),
#       array_has_any(arrow_cast(make_array([[1,2,3]]), 'FixedSizeList(1, List(List(Int64)))'), arrow_cast(make_array([[1,2,3]], [[4,5,6]]), 'FixedSizeList(2, List(List(Int64)))'))
#;
#----
#true false true false false false true true false false true false true

# rewrite various array_has operations to InList where the haystack is a literal list
# NB that `col in (a, b, c)` is simplified to OR if there are <= 3 elements, so we make 4-element haystack lists

query I
with test AS (SELECT substr(md5(i::text)::text, 1, 32) as needle FROM generate_series(1, 100000) t(i))
select count(*) from test WHERE needle IN ('7f4b18de3cfeb9b4ac78c381ee2ad278', 'a', 'b', 'c');
----
1

query TT
explain with test AS (SELECT substr(md5(i::text)::text, 1, 32) as needle FROM generate_series(1, 100000) t(i))
select count(*) from test WHERE needle IN ('7f4b18de3cfeb9b4ac78c381ee2ad278', 'a', 'b', 'c');
----
logical_plan
01)Projection: count(Int64(1)) AS count(*)
02)--Aggregate: groupBy=[[]], aggr=[[count(Int64(1))]]
03)----SubqueryAlias: test
04)------SubqueryAlias: t
05)--------Projection: 
06)----------Filter: substr(CAST(md5(CAST(tmp_table.value AS Utf8)) AS Utf8), Int64(1), Int64(32)) IN ([Utf8View("7f4b18de3cfeb9b4ac78c381ee2ad278"), Utf8View("a"), Utf8View("b"), Utf8View("c")])
07)------------TableScan: tmp_table projection=[value]
physical_plan
01)ProjectionExec: expr=[count(Int64(1))@0 as count(*)]
02)--AggregateExec: mode=Final, gby=[], aggr=[count(Int64(1))]
03)----CoalescePartitionsExec
04)------AggregateExec: mode=Partial, gby=[], aggr=[count(Int64(1))]
05)--------ProjectionExec: expr=[]
06)----------CoalesceBatchesExec: target_batch_size=8192
07)------------FilterExec: substr(md5(CAST(value@0 AS Utf8)), 1, 32) IN ([Literal { value: Utf8View("7f4b18de3cfeb9b4ac78c381ee2ad278") }, Literal { value: Utf8View("a") }, Literal { value: Utf8View("b") }, Literal { value: Utf8View("c") }])
08)--------------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
09)----------------LazyMemoryExec: partitions=1, batch_generators=[generate_series: start=1, end=100000, batch_size=8192]

query I
with test AS (SELECT substr(md5(i::text)::text, 1, 32) as needle FROM generate_series(1, 100000) t(i))
select count(*) from test WHERE needle = ANY(['7f4b18de3cfeb9b4ac78c381ee2ad278', 'a', 'b', 'c']);
----
1

query TT
explain with test AS (SELECT substr(md5(i::text)::text, 1, 32) as needle FROM generate_series(1, 100000) t(i))
select count(*) from test WHERE needle = ANY(['7f4b18de3cfeb9b4ac78c381ee2ad278', 'a', 'b', 'c']);
----
logical_plan
01)Projection: count(Int64(1)) AS count(*)
02)--Aggregate: groupBy=[[]], aggr=[[count(Int64(1))]]
03)----SubqueryAlias: test
04)------SubqueryAlias: t
05)--------Projection: 
06)----------Filter: substr(CAST(md5(CAST(tmp_table.value AS Utf8)) AS Utf8), Int64(1), Int64(32)) IN ([Utf8View("7f4b18de3cfeb9b4ac78c381ee2ad278"), Utf8View("a"), Utf8View("b"), Utf8View("c")])
07)------------TableScan: tmp_table projection=[value]
physical_plan
01)ProjectionExec: expr=[count(Int64(1))@0 as count(*)]
02)--AggregateExec: mode=Final, gby=[], aggr=[count(Int64(1))]
03)----CoalescePartitionsExec
04)------AggregateExec: mode=Partial, gby=[], aggr=[count(Int64(1))]
05)--------ProjectionExec: expr=[]
06)----------CoalesceBatchesExec: target_batch_size=8192
07)------------FilterExec: substr(md5(CAST(value@0 AS Utf8)), 1, 32) IN ([Literal { value: Utf8View("7f4b18de3cfeb9b4ac78c381ee2ad278") }, Literal { value: Utf8View("a") }, Literal { value: Utf8View("b") }, Literal { value: Utf8View("c") }])
08)--------------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
09)----------------LazyMemoryExec: partitions=1, batch_generators=[generate_series: start=1, end=100000, batch_size=8192]

query I
with test AS (SELECT substr(md5(i::text)::text, 1, 32) as needle FROM generate_series(1, 100000) t(i))
select count(*) from test WHERE array_has(['7f4b18de3cfeb9b4ac78c381ee2ad278', 'a', 'b', 'c'], needle);
----
1

query TT
explain with test AS (SELECT substr(md5(i::text)::text, 1, 32) as needle FROM generate_series(1, 100000) t(i))
select count(*) from test WHERE array_has(['7f4b18de3cfeb9b4ac78c381ee2ad278', 'a', 'b', 'c'], needle);
----
logical_plan
01)Projection: count(Int64(1)) AS count(*)
02)--Aggregate: groupBy=[[]], aggr=[[count(Int64(1))]]
03)----SubqueryAlias: test
04)------SubqueryAlias: t
05)--------Projection: 
06)----------Filter: substr(CAST(md5(CAST(tmp_table.value AS Utf8)) AS Utf8), Int64(1), Int64(32)) IN ([Utf8View("7f4b18de3cfeb9b4ac78c381ee2ad278"), Utf8View("a"), Utf8View("b"), Utf8View("c")])
07)------------TableScan: tmp_table projection=[value]
physical_plan
01)ProjectionExec: expr=[count(Int64(1))@0 as count(*)]
02)--AggregateExec: mode=Final, gby=[], aggr=[count(Int64(1))]
03)----CoalescePartitionsExec
04)------AggregateExec: mode=Partial, gby=[], aggr=[count(Int64(1))]
05)--------ProjectionExec: expr=[]
06)----------CoalesceBatchesExec: target_batch_size=8192
07)------------FilterExec: substr(md5(CAST(value@0 AS Utf8)), 1, 32) IN ([Literal { value: Utf8View("7f4b18de3cfeb9b4ac78c381ee2ad278") }, Literal { value: Utf8View("a") }, Literal { value: Utf8View("b") }, Literal { value: Utf8View("c") }])
08)--------------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
09)----------------LazyMemoryExec: partitions=1, batch_generators=[generate_series: start=1, end=100000, batch_size=8192]

# FIXME: due to rewrite below not working, this is _extremely_ slow to evaluate
# query I
# with test AS (SELECT substr(md5(i)::text, 1, 32) as needle FROM generate_series(1, 100000) t(i))
# select count(*) from test WHERE array_has(arrow_cast(['7f4b18de3cfeb9b4ac78c381ee2ad278', 'a', 'b', 'c'], 'LargeList(Utf8View)'), needle);
# ----
# 1

# FIXME: array_has with large list haystack not currently rewritten to InList
query TT
explain with test AS (SELECT substr(md5(i::text)::text, 1, 32) as needle FROM generate_series(1, 100000) t(i))
select count(*) from test WHERE array_has(arrow_cast(['7f4b18de3cfeb9b4ac78c381ee2ad278', 'a', 'b', 'c'], 'LargeList(Utf8View)'), needle);
----
logical_plan
01)Projection: count(Int64(1)) AS count(*)
02)--Aggregate: groupBy=[[]], aggr=[[count(Int64(1))]]
03)----SubqueryAlias: test
04)------SubqueryAlias: t
05)--------Projection: 
06)----------Filter: array_has(LargeList([7f4b18de3cfeb9b4ac78c381ee2ad278, a, b, c]), substr(CAST(md5(CAST(tmp_table.value AS Utf8)) AS Utf8), Int64(1), Int64(32)))
07)------------TableScan: tmp_table projection=[value]
physical_plan
01)ProjectionExec: expr=[count(Int64(1))@0 as count(*)]
02)--AggregateExec: mode=Final, gby=[], aggr=[count(Int64(1))]
03)----CoalescePartitionsExec
04)------AggregateExec: mode=Partial, gby=[], aggr=[count(Int64(1))]
05)--------ProjectionExec: expr=[]
06)----------CoalesceBatchesExec: target_batch_size=8192
07)------------FilterExec: array_has([7f4b18de3cfeb9b4ac78c381ee2ad278, a, b, c], substr(md5(CAST(value@0 AS Utf8)), 1, 32))
08)--------------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
09)----------------LazyMemoryExec: partitions=1, batch_generators=[generate_series: start=1, end=100000, batch_size=8192]

query I
with test AS (SELECT substr(md5(i::text)::text, 1, 32) as needle FROM generate_series(1, 100000) t(i))
select count(*) from test WHERE array_has(arrow_cast(['7f4b18de3cfeb9b4ac78c381ee2ad278', 'a', 'b', 'c'], 'FixedSizeList(4, Utf8View)'), needle);
----
1

query TT
explain with test AS (SELECT substr(md5(i::text)::text, 1, 32) as needle FROM generate_series(1, 100000) t(i))
select count(*) from test WHERE array_has(arrow_cast(['7f4b18de3cfeb9b4ac78c381ee2ad278', 'a', 'b', 'c'], 'FixedSizeList(4, Utf8View)'), needle);
----
logical_plan
01)Projection: count(Int64(1)) AS count(*)
02)--Aggregate: groupBy=[[]], aggr=[[count(Int64(1))]]
03)----SubqueryAlias: test
04)------SubqueryAlias: t
05)--------Projection: 
06)----------Filter: substr(CAST(md5(CAST(tmp_table.value AS Utf8)) AS Utf8), Int64(1), Int64(32)) IN ([Utf8View("7f4b18de3cfeb9b4ac78c381ee2ad278"), Utf8View("a"), Utf8View("b"), Utf8View("c")])
07)------------TableScan: tmp_table projection=[value]
physical_plan
01)ProjectionExec: expr=[count(Int64(1))@0 as count(*)]
02)--AggregateExec: mode=Final, gby=[], aggr=[count(Int64(1))]
03)----CoalescePartitionsExec
04)------AggregateExec: mode=Partial, gby=[], aggr=[count(Int64(1))]
05)--------ProjectionExec: expr=[]
06)----------CoalesceBatchesExec: target_batch_size=8192
07)------------FilterExec: substr(md5(CAST(value@0 AS Utf8)), 1, 32) IN ([Literal { value: Utf8View("7f4b18de3cfeb9b4ac78c381ee2ad278") }, Literal { value: Utf8View("a") }, Literal { value: Utf8View("b") }, Literal { value: Utf8View("c") }])
08)--------------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
09)----------------LazyMemoryExec: partitions=1, batch_generators=[generate_series: start=1, end=100000, batch_size=8192]

query I
with test AS (SELECT substr(md5(i::text)::text, 1, 32) as needle FROM generate_series(1, 100000) t(i))
select count(*) from test WHERE array_has([needle], needle);
----
100000

# The optimizer does not currently eliminate the filter;
# Instead, it's rewritten as `IS NULL OR NOT NULL` due to SQL null semantics
query TT
explain with test AS (SELECT substr(md5(i::text)::text, 1, 32) as needle FROM generate_series(1, 100000) t(i))
select count(*) from test WHERE array_has([needle], needle);
----
logical_plan
01)Projection: count(Int64(1)) AS count(*)
02)--Aggregate: groupBy=[[]], aggr=[[count(Int64(1))]]
03)----SubqueryAlias: test
04)------SubqueryAlias: t
05)--------Projection: 
06)----------Filter: substr(CAST(md5(CAST(tmp_table.value AS Utf8)) AS Utf8), Int64(1), Int64(32)) IS NOT NULL OR Boolean(NULL)
07)------------TableScan: tmp_table projection=[value]
physical_plan
01)ProjectionExec: expr=[count(Int64(1))@0 as count(*)]
02)--AggregateExec: mode=Final, gby=[], aggr=[count(Int64(1))]
03)----CoalescePartitionsExec
04)------AggregateExec: mode=Partial, gby=[], aggr=[count(Int64(1))]
05)--------ProjectionExec: expr=[]
06)----------CoalesceBatchesExec: target_batch_size=8192
07)------------FilterExec: substr(md5(CAST(value@0 AS Utf8)), 1, 32) IS NOT NULL OR NULL
08)--------------RepartitionExec: partitioning=RoundRobinBatch(4), input_partitions=1
09)----------------LazyMemoryExec: partitions=1, batch_generators=[generate_series: start=1, end=100000, batch_size=8192]

# any operator
query ?
select column3 from arrays where 'L'=any(column3);
----
[L, o, r, e, m]

query I
select count(*) from arrays where 'L'=any(column3);
----
1

query I
select count(*) from arrays where 'X'=any(column3);
----
0

query error DataFusion error: Error during planning: Unsupported AnyOp: '>', only '=' is supported
select count(*) from arrays where 'X'>any(column3);

## array_distinct

#TODO: https://github.com/apache/datafusion/issues/7142
#query ?
#select array_distinct(null);
#----
#NULL

# test with empty row, the row that does not match the condition has row count 0
statement ok
create table t1(a int, b int) as values (100, 1), (101, 2), (102, 3), (101, 2);

# rowsort is to ensure the order of group by is deterministic, array_sort has no effect here, since the sum() always returns single row.
query ? rowsort
select array_distinct([sum(a)]) from t1 where a > 100 group by b;
----
[102]
[202]

statement ok
drop table t1;

query ?
select array_distinct(a) from values ([1, 2, 3]), (null), ([1, 3, 1]) as X(a);
----
[1, 2, 3]
NULL
[1, 3]

query ?
select array_distinct(arrow_cast(null, 'LargeList(Int64)'));
----
NULL

query ?
select array_distinct([]);
----
[]

query ?
select array_distinct([[], []]);
----
[[]]

query ?
select array_distinct(column1)
from array_distinct_table_1D;
----
[1, 2, 3]
[1, 2, 3, 4, 5]
[3, 5]

query ?
select array_distinct(column1)
from array_distinct_table_1D_UTF8;
----
[a, bc, def]
[a, bc, def, defg]
[defg]

query ?
select array_distinct(column1)
from array_distinct_table_2D;
----
[[1, 2], [3, 4], [5, 6]]
[[1, 2], [3, 4], [5, 6], [7, 8], [9, 10]]
[NULL, [5, 6]]

query ?
select array_distinct(column1)
from array_distinct_table_1D_large;
----
[1, 2, 3]
[1, 2, 3, 4, 5]
[3, 5]

query ?
select array_distinct(column1)
from array_distinct_table_1D_fixed;
----
[1, 2, 3]
[1, 2, 3, 4, 5]
[3, 5]

query ?
select array_distinct(column1)
from array_distinct_table_1D_UTF8_fixed;
----
[a, bc, def]
[a, bc, def, defg]
[defg]

query ?
select array_distinct(column1)
from array_distinct_table_2D_fixed;
----
[[1, 2], [3, 4], [5, 6]]
[[1, 2], [3, 4], [5, 6], [7, 8], [9, 10]]
[NULL, [5, 6]]

query ???
select array_intersect(column1, column2),
       array_intersect(column3, column4),
       array_intersect(column5, column6)
from array_intersect_table_1D;
----
[1] [1, 3] [1, 3]
[11] [11, 33] [11, 33]

query ???
select array_intersect(column1, column2),
       array_intersect(column3, column4),
       array_intersect(column5, column6)
from large_array_intersect_table_1D;
----
[1] [1, 3] [1, 3]
[11] [11, 33] [11, 33]

query ???
select array_intersect(column1, column2),
       array_intersect(column3, column4),
       array_intersect(column5, column6)
from array_intersect_table_1D_Float;
----
[1.0] [1.0, 3.0] []
[] [2.0] [1.11]

query ???
select array_intersect(column1, column2),
       array_intersect(column3, column4),
       array_intersect(column5, column6)
from array_intersect_table_1D_Boolean;
----
[] [false, true] [false]
[false] [true] [true]

query ???
select array_intersect(column1, column2),
       array_intersect(column3, column4),
       array_intersect(column5, column6)
from large_array_intersect_table_1D_Boolean;
----
[] [false, true] [false]
[false] [true] [true]

query ???
select array_intersect(column1, column2),
       array_intersect(column3, column4),
       array_intersect(column5, column6)
from array_intersect_table_1D_UTF8;
----
[bc] [arrow, rust] []
[] [arrow, datafusion, rust] [arrow, rust]

query ???
select array_intersect(column1, column2),
       array_intersect(column3, column4),
       array_intersect(column5, column6)
from large_array_intersect_table_1D_UTF8;
----
[bc] [arrow, rust] []
[] [arrow, datafusion, rust] [arrow, rust]

query ??
select array_intersect(column1, column2),
       array_intersect(column3, column4)
from array_intersect_table_2D;
----
[] [[4, 5], [6, 7]]
[[3, 4]] [[5, 6, 7], [8, 9, 10]]

query ??
select array_intersect(column1, column2),
       array_intersect(column3, column4)
from large_array_intersect_table_2D;
----
[] [[4, 5], [6, 7]]
[[3, 4]] [[5, 6, 7], [8, 9, 10]]


query ?
select array_intersect(column1, column2)
from array_intersect_table_2D_float;
----
[[1.1, 2.2], [3.3]]
[[1.1, 2.2], [3.3]]

query ?
select array_intersect(column1, column2)
from large_array_intersect_table_2D_float;
----
[[1.1, 2.2], [3.3]]
[[1.1, 2.2], [3.3]]

query ?
select array_intersect(column1, column2)
from array_intersect_table_3D;
----
[]
[[[1, 2]]]

query ?
select array_intersect(column1, column2)
from large_array_intersect_table_3D;
----
[]
[[[1, 2]]]

query ??????
SELECT  array_intersect(make_array(1,2,3), make_array(2,3,4)),
        array_intersect(make_array(1,3,5), make_array(2,4,6)),
        array_intersect(make_array('aa','bb','cc'), make_array('cc','aa','dd')),
        array_intersect(make_array(true, false), make_array(true)),
        array_intersect(make_array(1.1, 2.2, 3.3), make_array(2.2, 3.3, 4.4)),
        array_intersect(make_array([1, 1], [2, 2], [3, 3]), make_array([2, 2], [3, 3], [4, 4]))
;
----
[2, 3] [] [aa, cc] [true] [2.2, 3.3] [[2, 2], [3, 3]]

query ??????
SELECT  array_intersect(arrow_cast(make_array(1,2,3), 'LargeList(Int64)'), arrow_cast(make_array(2,3,4), 'LargeList(Int64)')),
        array_intersect(arrow_cast(make_array(1,3,5), 'LargeList(Int64)'), arrow_cast(make_array(2,4,6), 'LargeList(Int64)')),
        array_intersect(arrow_cast(make_array('aa','bb','cc'), 'LargeList(Utf8)'), arrow_cast(make_array('cc','aa','dd'), 'LargeList(Utf8)')),
        array_intersect(arrow_cast(make_array(true, false), 'LargeList(Boolean)'), arrow_cast(make_array(true), 'LargeList(Boolean)')),
        array_intersect(arrow_cast(make_array(1.1, 2.2, 3.3), 'LargeList(Float64)'), arrow_cast(make_array(2.2, 3.3, 4.4), 'LargeList(Float64)')),
        array_intersect(arrow_cast(make_array([1, 1], [2, 2], [3, 3]), 'LargeList(List(Int64))'), arrow_cast(make_array([2, 2], [3, 3], [4, 4]), 'LargeList(List(Int64))'))
;
----
[2, 3] [] [aa, cc] [true] [2.2, 3.3] [[2, 2], [3, 3]]

query ?
select array_intersect([], []);
----
[]

query ?
select array_intersect(arrow_cast([], 'LargeList(Int64)'), arrow_cast([], 'LargeList(Int64)'));
----
[]

query ?
select array_intersect([1, 1, 2, 2, 3, 3], null);
----
[]

query ?
select array_intersect(arrow_cast([1, 1, 2, 2, 3, 3], 'LargeList(Int64)'), null);
----
[]

query ?
select array_intersect(null, [1, 1, 2, 2, 3, 3]);
----
NULL

query ?
select array_intersect(null, arrow_cast([1, 1, 2, 2, 3, 3], 'LargeList(Int64)'));
----
NULL

query ?
select array_intersect([], null);
----
[]

query ?
select array_intersect([[1,2,3]], [[]]);
----
[]

query ?
select array_intersect([[null]], [[]]);
----
[]

query ?
select array_intersect(arrow_cast([], 'LargeList(Int64)'), null);
----
[]

query ?
select array_intersect(null, []);
----
NULL

query ?
select array_intersect(null, arrow_cast([], 'LargeList(Int64)'));
----
NULL

query ?
select array_intersect(null, null);
----
NULL

query ??????
SELECT  list_intersect(make_array(1,2,3), make_array(2,3,4)),
        list_intersect(make_array(1,3,5), make_array(2,4,6)),
        list_intersect(make_array('aa','bb','cc'), make_array('cc','aa','dd')),
        list_intersect(make_array(true, false), make_array(true)),
        list_intersect(make_array(1.1, 2.2, 3.3), make_array(2.2, 3.3, 4.4)),
        list_intersect(make_array([1, 1], [2, 2], [3, 3]), make_array([2, 2], [3, 3], [4, 4]))
;
----
[2, 3] [] [aa, cc] [true] [2.2, 3.3] [[2, 2], [3, 3]]

query ??????
SELECT  list_intersect(arrow_cast(make_array(1,2,3), 'LargeList(Int64)'), arrow_cast(make_array(2,3,4), 'LargeList(Int64)')),
        list_intersect(arrow_cast(make_array(1,3,5), 'LargeList(Int64)'), arrow_cast(make_array(2,4,6), 'LargeList(Int64)')),
        list_intersect(arrow_cast(make_array('aa','bb','cc'), 'LargeList(Utf8)'), arrow_cast(make_array('cc','aa','dd'), 'LargeList(Utf8)')),
        list_intersect(arrow_cast(make_array(true, false), 'LargeList(Boolean)'), arrow_cast(make_array(true), 'LargeList(Boolean)')),
        list_intersect(arrow_cast(make_array(1.1, 2.2, 3.3), 'LargeList(Float64)'), arrow_cast(make_array(2.2, 3.3, 4.4), 'LargeList(Float64)')),
        list_intersect(arrow_cast(make_array([1, 1], [2, 2], [3, 3]), 'LargeList(List(Int64))'), arrow_cast(make_array([2, 2], [3, 3], [4, 4]), 'LargeList(List(Int64))'))
;
----
[2, 3] [] [aa, cc] [true] [2.2, 3.3] [[2, 2], [3, 3]]

query BBBB
select list_has_all(make_array(1,2,3), make_array(4,5,6)),
       list_has_all(make_array(1,2,3), make_array(1,2)),
       list_has_any(make_array(1,2,3), make_array(4,5,6)),
        list_has_any(make_array(1,2,3), make_array(1,2,4))
;
----
false true false true

query BBBB
select arrays_overlap(make_array(1,2,3), make_array(4,5,6)),
        arrays_overlap(make_array(1,2,3), make_array(1,2,4)),
        arrays_overlap(make_array(['aa']), make_array(['aa'],['bb'])),
        arrays_overlap(make_array('aa',NULL), make_array('bb',NULL))
;
----
false true true true

query ???
select range(column2),
       range(column1, column2),
       range(column1, column2, column3)
from arrays_range;
----
[0, 1, 2, 3, 4, 5, 6, 7, 8, 9] [3, 4, 5, 6, 7, 8, 9] [3, 5, 7, 9]
[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12] [4, 5, 6, 7, 8, 9, 10, 11, 12] [4, 7, 10]

query ???????????
select range(5),
       range(2, 5),
       range(2, 10, 3),
       range(10, 2, -3),
       range(1, 5, -1),
       range(1, -5, 1),
       range(1, -5, -1),
       range(DATE '1992-09-01', DATE '1993-03-01', INTERVAL '1' MONTH),
       range(DATE '1993-02-01', DATE '1993-01-01', INTERVAL '-1' DAY),
       range(DATE '1989-04-01', DATE '1993-03-01', INTERVAL '1' YEAR),
       range(DATE '1993-03-01', DATE '1989-04-01', INTERVAL '1' YEAR)
;
----
[0, 1, 2, 3, 4] [2, 3, 4] [2, 5, 8] [10, 7, 4] [] [] [1, 0, -1, -2, -3, -4] [1992-09-01, 1992-10-01, 1992-11-01, 1992-12-01, 1993-01-01, 1993-02-01] [1993-02-01, 1993-01-31, 1993-01-30, 1993-01-29, 1993-01-28, 1993-01-27, 1993-01-26, 1993-01-25, 1993-01-24, 1993-01-23, 1993-01-22, 1993-01-21, 1993-01-20, 1993-01-19, 1993-01-18, 1993-01-17, 1993-01-16, 1993-01-15, 1993-01-14, 1993-01-13, 1993-01-12, 1993-01-11, 1993-01-10, 1993-01-09, 1993-01-08, 1993-01-07, 1993-01-06, 1993-01-05, 1993-01-04, 1993-01-03, 1993-01-02] [1989-04-01, 1990-04-01, 1991-04-01] []

# Test range with zero step
query error DataFusion error: Execution error: step can't be 0 for function range\(start \[, stop, step\]\)
select range(1, 1, 0);

# Test range with big steps
query ????
select
  range(-9223372036854775808, -9223372036854775808, -9223372036854775808) as c1,
  range(9223372036854775807, 9223372036854775807, 9223372036854775807) as c2,
  range(0, -9223372036854775808, -9223372036854775808) as c3,
  range(0, 9223372036854775807, 9223372036854775807) as c4;
----
[] [] [0] [0]

# Test range for other edge cases
query ????????
select
  range(9223372036854775807, 9223372036854775807, -1) as c1,
  range(9223372036854775807, 9223372036854775806, -1) as c2,
  range(9223372036854775807, 9223372036854775807, 1) as c3,
  range(9223372036854775806, 9223372036854775807, 1) as c4,
  range(-9223372036854775808, -9223372036854775808, -1) as c5,
  range(-9223372036854775807, -9223372036854775808, -1) as c6,
  range(-9223372036854775808, -9223372036854775808, 1) as c7,
  range(-9223372036854775808, -9223372036854775807, 1) as c8;
----
[] [9223372036854775807] [] [9223372036854775806] [] [-9223372036854775807] [] [-9223372036854775808]

# Test range(start, stop, step) with NULL values
query ?
select range(start, stop, step) from
  (values (1), (NULL)) as start_values(start),
  (values (10), (NULL)) as stop_values(stop),
  (values (3), (NULL)) as step_values(step)
where start is null or stop is null or step is null
----
NULL
NULL
NULL
NULL
NULL
NULL
NULL

# Test range(start, stop) with NULL values
query ?
select range(start, stop) from
  (values (1), (NULL)) as start_values(start),
  (values (10), (NULL)) as stop_values(stop)
where start is null or stop is null
----
NULL
NULL
NULL

# Test range(stop) with NULL value
query ?
select range(NULL)
----
NULL

## should return NULL
query ?
select range(DATE '1992-09-01', NULL, INTERVAL '1' YEAR);
----
NULL

## should return NULL
query ?
select range(TIMESTAMP '1992-09-01', NULL, INTERVAL '1' YEAR);
----
NULL

query ?
select range(DATE '1992-09-01', DATE '1993-03-01', NULL);
----
NULL

query ?
select range(TIMESTAMP '1992-09-01', TIMESTAMP '1993-03-01', NULL);
----
NULL

query ?
select range(NULL, DATE '1993-03-01', INTERVAL '1' YEAR);
----
NULL

query ?
select generate_series(NULL::Date, DATE '1993-03-01', INTERVAL '1' YEAR);
----
NULL

query ?
select generate_series(DATE '1993-03-01', NULL::Date, INTERVAL '1' YEAR);
----
NULL

query ?
select generate_series(DATE '1993-02-01', DATE '1993-03-01', NULL::Interval);
----
NULL

query ?
select range(NULL, TIMESTAMP '1993-03-01', INTERVAL '1' YEAR);
----
NULL

query ?
select range(NULL, NULL, NULL);
----
NULL

query ?
select range(NULL::timestamp, NULL::timestamp, NULL);
----
NULL

query ?
select range(DATE '1989-04-01', DATE '1993-03-01', INTERVAL '-1' YEAR)
----
[]

query ?
select range(TIMESTAMP '1989-04-01', TIMESTAMP '1993-03-01', INTERVAL '-1' YEAR)
----
[]

query ?
select range(DATE '1993-03-01', DATE '1989-04-01', INTERVAL '1' YEAR)
----
[]

query ?
select range(TIMESTAMP '1993-03-01', TIMESTAMP '1989-04-01', INTERVAL '1' YEAR)
----
[]

query error DataFusion error: Execution error: Cannot generate date range less than 1 day\.
select range(DATE '1993-03-01', DATE '1993-03-01', INTERVAL '1' HOUR)

query ?
select range(TIMESTAMP '1993-03-01', TIMESTAMP '1993-03-01', INTERVAL '1' HOUR)
----
[]

query ?????????
select generate_series(5),
       generate_series(2, 5),
       generate_series(2, 10, 3),
       generate_series(1, 5, 1),
       generate_series(5, 1, -1),
       generate_series(10, 2, -3),
       generate_series(DATE '1992-09-01', DATE '1993-03-01', INTERVAL '1' MONTH),
       generate_series(DATE '1993-02-01', DATE '1993-01-01', INTERVAL '-1' DAY),
       generate_series(DATE '1989-04-01', DATE '1993-03-01', INTERVAL '1' YEAR)
;
----
[0, 1, 2, 3, 4, 5] [2, 3, 4, 5] [2, 5, 8] [1, 2, 3, 4, 5] [5, 4, 3, 2, 1] [10, 7, 4] [1992-09-01, 1992-10-01, 1992-11-01, 1992-12-01, 1993-01-01, 1993-02-01, 1993-03-01] [1993-02-01, 1993-01-31, 1993-01-30, 1993-01-29, 1993-01-28, 1993-01-27, 1993-01-26, 1993-01-25, 1993-01-24, 1993-01-23, 1993-01-22, 1993-01-21, 1993-01-20, 1993-01-19, 1993-01-18, 1993-01-17, 1993-01-16, 1993-01-15, 1993-01-14, 1993-01-13, 1993-01-12, 1993-01-11, 1993-01-10, 1993-01-09, 1993-01-08, 1993-01-07, 1993-01-06, 1993-01-05, 1993-01-04, 1993-01-03, 1993-01-02, 1993-01-01] [1989-04-01, 1990-04-01, 1991-04-01, 1992-04-01]

query ?
select generate_series('2021-01-01'::timestamp, '2021-01-01T15:00:00'::timestamp, INTERVAL '1' HOUR);
----
[2021-01-01T00:00:00, 2021-01-01T01:00:00, 2021-01-01T02:00:00, 2021-01-01T03:00:00, 2021-01-01T04:00:00, 2021-01-01T05:00:00, 2021-01-01T06:00:00, 2021-01-01T07:00:00, 2021-01-01T08:00:00, 2021-01-01T09:00:00, 2021-01-01T10:00:00, 2021-01-01T11:00:00, 2021-01-01T12:00:00, 2021-01-01T13:00:00, 2021-01-01T14:00:00, 2021-01-01T15:00:00]

query ?
select generate_series('2021-01-01T00:00:00EST'::timestamp, '2021-01-01T15:00:00-12:00'::timestamp, INTERVAL '1' HOUR);
----
[2021-01-01T05:00:00, 2021-01-01T06:00:00, 2021-01-01T07:00:00, 2021-01-01T08:00:00, 2021-01-01T09:00:00, 2021-01-01T10:00:00, 2021-01-01T11:00:00, 2021-01-01T12:00:00, 2021-01-01T13:00:00, 2021-01-01T14:00:00, 2021-01-01T15:00:00, 2021-01-01T16:00:00, 2021-01-01T17:00:00, 2021-01-01T18:00:00, 2021-01-01T19:00:00, 2021-01-01T20:00:00, 2021-01-01T21:00:00, 2021-01-01T22:00:00, 2021-01-01T23:00:00, 2021-01-02T00:00:00, 2021-01-02T01:00:00, 2021-01-02T02:00:00, 2021-01-02T03:00:00]

query ?
select generate_series(arrow_cast('2021-01-01T00:00:00', 'Timestamp(Nanosecond, Some("-05:00"))'), arrow_cast('2021-01-01T15:00:00', 'Timestamp(Nanosecond, Some("+05:00"))'), INTERVAL '1' HOUR);
----
[2021-01-01T00:00:00-05:00, 2021-01-01T01:00:00-05:00, 2021-01-01T02:00:00-05:00, 2021-01-01T03:00:00-05:00, 2021-01-01T04:00:00-05:00, 2021-01-01T05:00:00-05:00]

## -5500000000 ns is -5.5 sec
query ?
select generate_series(arrow_cast('2021-01-01T00:00:00', 'Timestamp(Nanosecond, Some("-05:00"))'), arrow_cast('2021-01-01T06:00:00', 'Timestamp(Nanosecond, Some("-05:00"))'), INTERVAL '1 HOUR 30 MINUTE -5500000000 NANOSECOND');
----
[2021-01-01T00:00:00-05:00, 2021-01-01T01:29:54.500-05:00, 2021-01-01T02:59:49-05:00, 2021-01-01T04:29:43.500-05:00, 2021-01-01T05:59:38-05:00]

## mixing types for timestamps is not supported
query error DataFusion error: Internal error: Unexpected argument type for GENERATE_SERIES : Date32
select generate_series(arrow_cast('2021-01-01T00:00:00', 'Timestamp(Nanosecond, Some("-05:00"))'), DATE '2021-01-02', INTERVAL '1' HOUR);


## should return NULL
query ?
select generate_series(DATE '1992-09-01', NULL, INTERVAL '1' YEAR);
----
NULL

## should return NULL
query ?
select generate_series(TIMESTAMP '1992-09-01', NULL, INTERVAL '1' YEAR);
----
NULL

query ?
select generate_series(DATE '1992-09-01', DATE '1993-03-01', NULL);
----
NULL

query ?
select generate_series(TIMESTAMP '1992-09-01', DATE '1993-03-01', NULL);
----
NULL

query ?
select generate_series(NULL, DATE '1993-03-01', INTERVAL '1' YEAR);
----
NULL

query ?
select generate_series(NULL::Date, DATE '1993-03-01', INTERVAL '1' YEAR);
----
NULL

query ?
select generate_series(DATE '1993-03-01', NULL::Date, INTERVAL '1' YEAR);
----
NULL

query ?
select generate_series(DATE '1993-02-01', DATE '1993-03-01', NULL::Interval);
----
NULL

query ?
select generate_series(NULL, TIMESTAMP '1993-03-01', INTERVAL '1' YEAR);
----
NULL

query ?
select generate_series(NULL, NULL, NULL);
----
NULL

query ?
select generate_series(NULL::timestamp, NULL::timestamp, NULL);
----
NULL

query ?
select generate_series(DATE '1989-04-01', DATE '1993-03-01', INTERVAL '-1' YEAR)
----
[]

query ?
select generate_series(TIMESTAMP '1989-04-01', TIMESTAMP '1993-03-01', INTERVAL '-1' YEAR)
----
[]

query ?
select generate_series(DATE '1993-03-01', DATE '1989-04-01', INTERVAL '1' YEAR)
----
[]

query ?
select generate_series(TIMESTAMP '1993-03-01', TIMESTAMP '1989-04-01', INTERVAL '1' YEAR)
----
[]

query error DataFusion error: Execution error: Cannot generate date range less than 1 day.
select generate_series(DATE '2000-01-01', DATE '2000-01-03', INTERVAL '1' HOUR)

query error DataFusion error: Execution error: Cannot generate date range less than 1 day.
select generate_series(DATE '2000-01-01', DATE '2000-01-03', INTERVAL '-1' HOUR)

query ?
select generate_series(TIMESTAMP '2000-01-01', TIMESTAMP '2000-01-02', INTERVAL '1' HOUR)
----
[2000-01-01T00:00:00, 2000-01-01T01:00:00, 2000-01-01T02:00:00, 2000-01-01T03:00:00, 2000-01-01T04:00:00, 2000-01-01T05:00:00, 2000-01-01T06:00:00, 2000-01-01T07:00:00, 2000-01-01T08:00:00, 2000-01-01T09:00:00, 2000-01-01T10:00:00, 2000-01-01T11:00:00, 2000-01-01T12:00:00, 2000-01-01T13:00:00, 2000-01-01T14:00:00, 2000-01-01T15:00:00, 2000-01-01T16:00:00, 2000-01-01T17:00:00, 2000-01-01T18:00:00, 2000-01-01T19:00:00, 2000-01-01T20:00:00, 2000-01-01T21:00:00, 2000-01-01T22:00:00, 2000-01-01T23:00:00, 2000-01-02T00:00:00]

query ?
select generate_series(TIMESTAMP '2000-01-02', TIMESTAMP '2000-01-01', INTERVAL '-1' HOUR)
----
[2000-01-02T00:00:00, 2000-01-01T23:00:00, 2000-01-01T22:00:00, 2000-01-01T21:00:00, 2000-01-01T20:00:00, 2000-01-01T19:00:00, 2000-01-01T18:00:00, 2000-01-01T17:00:00, 2000-01-01T16:00:00, 2000-01-01T15:00:00, 2000-01-01T14:00:00, 2000-01-01T13:00:00, 2000-01-01T12:00:00, 2000-01-01T11:00:00, 2000-01-01T10:00:00, 2000-01-01T09:00:00, 2000-01-01T08:00:00, 2000-01-01T07:00:00, 2000-01-01T06:00:00, 2000-01-01T05:00:00, 2000-01-01T04:00:00, 2000-01-01T03:00:00, 2000-01-01T02:00:00, 2000-01-01T01:00:00, 2000-01-01T00:00:00]

# Test generate_series with small intervals
query ?
select generate_series('2000-01-01T00:00:00.000000001Z'::timestamp, '2000-01-01T00:00:00.00000001Z'::timestamp, INTERVAL '1' NANOSECONDS)
----
[2000-01-01T00:00:00.000000001, 2000-01-01T00:00:00.000000002, 2000-01-01T00:00:00.000000003, 2000-01-01T00:00:00.000000004, 2000-01-01T00:00:00.000000005, 2000-01-01T00:00:00.000000006, 2000-01-01T00:00:00.000000007, 2000-01-01T00:00:00.000000008, 2000-01-01T00:00:00.000000009, 2000-01-01T00:00:00.000000010]

# Test generate_series with zero step
query error DataFusion error: Execution error: step can't be 0 for function generate_series\(start \[, stop, step\]\)
select generate_series(1, 1, 0);

# Test generate_series with zero step
query error DataFusion error: Execution error: Interval argument to GENERATE_SERIES must not be 0
select generate_series(TIMESTAMP '2000-01-02', TIMESTAMP '2000-01-01', INTERVAL '0' MINUTE);

# Test generate_series with big steps
query ????
select
  generate_series(-9223372036854775808, -9223372036854775808, -9223372036854775808) as c1,
  generate_series(9223372036854775807, 9223372036854775807, 9223372036854775807) as c2,
  generate_series(0, -9223372036854775808, -9223372036854775808) as c3,
  generate_series(0, 9223372036854775807, 9223372036854775807) as c4;
----
[-9223372036854775808] [9223372036854775807] [0, -9223372036854775808] [0, 9223372036854775807]


# Test generate_series for other edge cases
query ????
select
  generate_series(9223372036854775807, 9223372036854775807, -1) as c1,
  generate_series(9223372036854775807, 9223372036854775807, 1) as c2,
  generate_series(-9223372036854775808, -9223372036854775808, -1) as c3,
  generate_series(-9223372036854775808, -9223372036854775808, 1) as c4;
----
[9223372036854775807] [9223372036854775807] [-9223372036854775808] [-9223372036854775808]

# Test generate_series(start, stop, step) with NULL values
query ?
select generate_series(start, stop, step) from
  (values (1), (NULL)) as start_values(start),
  (values (10), (NULL)) as stop_values(stop),
  (values (3), (NULL)) as step_values(step)
where start is null or stop is null or step is null
----
NULL
NULL
NULL
NULL
NULL
NULL
NULL

# Test generate_series(start, stop) with NULL values
query ?
select generate_series(start, stop) from
  (values (1), (NULL)) as start_values(start),
  (values (10), (NULL)) as stop_values(stop)
where start is null or stop is null
----
NULL
NULL
NULL

# Test generate_series(stop) with NULL value
query ?
select generate_series(NULL)
----
NULL

# Test generate_series with a table of date values
statement ok
CREATE TABLE date_table(
  start DATE,
  stop DATE,
  step INTERVAL
) AS VALUES
  (DATE '1992-01-01', DATE '1993-01-02', INTERVAL '1' MONTH),
  (DATE '1993-02-01', DATE '1993-01-01', INTERVAL '-1' DAY),
  (DATE '1989-04-01', DATE '1993-03-01', INTERVAL '1' YEAR);

query ?
select generate_series(start, stop, step) from date_table;
----
[1992-01-01, 1992-02-01, 1992-03-01, 1992-04-01, 1992-05-01, 1992-06-01, 1992-07-01, 1992-08-01, 1992-09-01, 1992-10-01, 1992-11-01, 1992-12-01, 1993-01-01]
[1993-02-01, 1993-01-31, 1993-01-30, 1993-01-29, 1993-01-28, 1993-01-27, 1993-01-26, 1993-01-25, 1993-01-24, 1993-01-23, 1993-01-22, 1993-01-21, 1993-01-20, 1993-01-19, 1993-01-18, 1993-01-17, 1993-01-16, 1993-01-15, 1993-01-14, 1993-01-13, 1993-01-12, 1993-01-11, 1993-01-10, 1993-01-09, 1993-01-08, 1993-01-07, 1993-01-06, 1993-01-05, 1993-01-04, 1993-01-03, 1993-01-02, 1993-01-01]
[1989-04-01, 1990-04-01, 1991-04-01, 1992-04-01]

query ?
select generate_series(start, stop, INTERVAL '1 year') from date_table;
----
[1992-01-01, 1993-01-01]
[]
[1989-04-01, 1990-04-01, 1991-04-01, 1992-04-01]

query ?
select generate_series(start, '1993-03-01'::date, INTERVAL '1 year') from date_table;
----
[1992-01-01, 1993-01-01]
[1993-02-01]
[1989-04-01, 1990-04-01, 1991-04-01, 1992-04-01]

# Test generate_series with a table of timestamp values
statement ok
CREATE TABLE timestamp_table(
  start TIMESTAMP,
  stop TIMESTAMP,
  step INTERVAL
) AS VALUES
  (TIMESTAMP '1992-01-01T00:00:00', TIMESTAMP '1993-01-02T00:00:00', INTERVAL '1' MONTH),
  (TIMESTAMP '1993-02-01T00:00:00', TIMESTAMP '1993-01-01T00:00:00', INTERVAL '-1' DAY),
  (TIMESTAMP '1989-04-01T00:00:00', TIMESTAMP '1993-03-01T00:00:00', INTERVAL '1' YEAR);

query ?
select generate_series(start, stop, step) from timestamp_table;
----
[1992-01-01T00:00:00, 1992-02-01T00:00:00, 1992-03-01T00:00:00, 1992-04-01T00:00:00, 1992-05-01T00:00:00, 1992-06-01T00:00:00, 1992-07-01T00:00:00, 1992-08-01T00:00:00, 1992-09-01T00:00:00, 1992-10-01T00:00:00, 1992-11-01T00:00:00, 1992-12-01T00:00:00, 1993-01-01T00:00:00]
[1993-02-01T00:00:00, 1993-01-31T00:00:00, 1993-01-30T00:00:00, 1993-01-29T00:00:00, 1993-01-28T00:00:00, 1993-01-27T00:00:00, 1993-01-26T00:00:00, 1993-01-25T00:00:00, 1993-01-24T00:00:00, 1993-01-23T00:00:00, 1993-01-22T00:00:00, 1993-01-21T00:00:00, 1993-01-20T00:00:00, 1993-01-19T00:00:00, 1993-01-18T00:00:00, 1993-01-17T00:00:00, 1993-01-16T00:00:00, 1993-01-15T00:00:00, 1993-01-14T00:00:00, 1993-01-13T00:00:00, 1993-01-12T00:00:00, 1993-01-11T00:00:00, 1993-01-10T00:00:00, 1993-01-09T00:00:00, 1993-01-08T00:00:00, 1993-01-07T00:00:00, 1993-01-06T00:00:00, 1993-01-05T00:00:00, 1993-01-04T00:00:00, 1993-01-03T00:00:00, 1993-01-02T00:00:00, 1993-01-01T00:00:00]
[1989-04-01T00:00:00, 1990-04-01T00:00:00, 1991-04-01T00:00:00, 1992-04-01T00:00:00]

query ?
select generate_series(start, stop, INTERVAL '1 year') from timestamp_table;
----
[1992-01-01T00:00:00, 1993-01-01T00:00:00]
[]
[1989-04-01T00:00:00, 1990-04-01T00:00:00, 1991-04-01T00:00:00, 1992-04-01T00:00:00]

query ?
select generate_series(start, '1993-03-01T00:00:00'::timestamp, INTERVAL '1 year') from timestamp_table;
----
[1992-01-01T00:00:00, 1993-01-01T00:00:00]
[1993-02-01T00:00:00]
[1989-04-01T00:00:00, 1990-04-01T00:00:00, 1991-04-01T00:00:00, 1992-04-01T00:00:00]

# https://github.com/apache/datafusion/issues/11922
query ?
select generate_series(start, '1993-03-01T00:00:00'::timestamp, INTERVAL '1 year') from timestamp_table;
----
[1992-01-01T00:00:00, 1993-01-01T00:00:00]
[1993-02-01T00:00:00]
[1989-04-01T00:00:00, 1990-04-01T00:00:00, 1991-04-01T00:00:00, 1992-04-01T00:00:00]

## array_except

statement ok
CREATE TABLE array_except_table
AS VALUES
  ([1, 2, 2, 3], [2, 3, 4]),
  ([2, 3, 3], [3]),
  ([3], [3, 3, 4]),
  (null, [3, 4]),
  ([1, 2], null),
  (null, null)
;

query ?
select array_except(column1, column2) from array_except_table;
----
[1]
[2]
[]
NULL
[1, 2]
NULL

statement ok
drop table array_except_table;

statement ok
CREATE TABLE array_except_nested_list_table
AS VALUES
  ([[1, 2], [3]], [[2], [3], [4, 5]]),
  ([[1, 2], [3]], [[2], [1, 2]]),
  ([[1, 2], [3]], null),
  (null, [[1], [2, 3], [4, 5, 6]]),
  ([[1], [2, 3], [4, 5, 6]], [[2, 3], [4, 5, 6], [1]])
;

query ?
select array_except(column1, column2) from array_except_nested_list_table;
----
[[1, 2]]
[[3]]
[[1, 2], [3]]
NULL
[]

statement ok
drop table array_except_nested_list_table;

statement ok
CREATE TABLE array_except_table_float
AS VALUES
  ([1.1, 2.2, 3.3], [2.2]),
  ([1.1, 2.2, 3.3], [4.4]),
  ([1.1, 2.2, 3.3], [3.3, 2.2, 1.1])
;

query ?
select array_except(column1, column2) from array_except_table_float;
----
[1.1, 3.3]
[1.1, 2.2, 3.3]
[]

statement ok
drop table array_except_table_float;

statement ok
CREATE TABLE array_except_table_ut8
AS VALUES
  (['a', 'b', 'c'], ['a']),
  (['a', 'bc', 'def'], ['g', 'def']),
  (['a', 'bc', 'def'], null),
  (null, ['a'])
;

query ?
select array_except(column1, column2) from array_except_table_ut8;
----
[b, c]
[a, bc]
[a, bc, def]
NULL

statement ok
drop table array_except_table_ut8;

statement ok
CREATE TABLE array_except_table_bool
AS VALUES
  ([true, false, false], [false]),
  ([true, true, true], [false]),
  ([false, false, false], [true]),
  ([true, false], null),
  (null, [true, false])
;

query ?
select array_except(column1, column2) from array_except_table_bool;
----
[true]
[true]
[false]
[true, false]
NULL

statement ok
drop table array_except_table_bool;

query ?
select array_except([], null);
----
[]

query ?
select array_except([], []);
----
[]

query ?
select array_except(null, []);
----
NULL

query ?
select array_except(null, null)
----
NULL

### Array operators tests


## array concatenate operator

# array concatenate operator with scalars #1 (like array_concat scalar function)
query ??
select make_array(1, 2, 3) || make_array(4, 5, 6) || make_array(7, 8, 9), make_array([1], [2]) || make_array([3], [4]);
----
[1, 2, 3, 4, 5, 6, 7, 8, 9] [[1], [2], [3], [4]]

# array concatenate operator with scalars #2 (like array_append scalar function)
query ???
select make_array(1, 2, 3) || 4, make_array(1.0, 2.0, 3.0) || 4.0, make_array('h', 'e', 'l', 'l') || 'o';
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

# array concatenate operator with scalars #3 (like array_prepend scalar function)
query ???
select 1 || make_array(2, 3, 4), 1.0 || make_array(2.0, 3.0, 4.0), 'h' || make_array('e', 'l', 'l', 'o');
----
[1, 2, 3, 4] [1.0, 2.0, 3.0, 4.0] [h, e, l, l, o]

# array concatenate operator with scalars #4 (mixed)
query ?
select 0 || [1,2,3] || 4 || [5] || [6,7];
----
[0, 1, 2, 3, 4, 5, 6, 7]

# array concatenate operator with nd-list #5 (mixed)
query ?
select 0 || [1,2,3] || [[4,5]] || [[6,7,8]] || [9,10];
----
[[0, 1, 2, 3], [4, 5], [6, 7, 8], [9, 10]]

# array concatenate operator non-valid cases
## concat 2D with scalar is not valid
query error
select 0 || [1,2,3] || [[4,5]] || [[6,7,8]] || [9,10] || 11;

## concat scalar with 2D is not valid
query error
select 0 || [[1,2,3]];

# array concatenate operator with column

statement ok
CREATE TABLE array_concat_operator_table
AS VALUES
  (0, [1, 2, 2, 3], 4, [5, 6, 5]),
  (-1, [4, 5, 6], 7, [8, 1, 1])
;

query ?
select column1 || column2 || column3 || column4 from array_concat_operator_table;
----
[0, 1, 2, 2, 3, 4, 5, 6, 5]
[-1, 4, 5, 6, 7, 8, 1, 1]

statement ok
drop table array_concat_operator_table;

## array containment operator

# array containment operator with scalars #1 (at arrow)
query BBBBBBB
select make_array(1,2,3) @> make_array(1,3),
       make_array(1,2,3) @> make_array(1,4),
       make_array([1,2], [3,4]) @> make_array([1,2]),
       make_array([1,2], [3,4]) @> make_array([1,3]),
       make_array([1,2], [3,4]) @> make_array([1,2], [3,4], [5,6]),
       make_array([[1,2,3]]) @> make_array([[1]]),
       make_array([[1,2,3]]) @> make_array([[1,2,3]]);
----
true false true false false false true

# Make sure it is rewritten to function array_has_all()
query TT
explain select [1,2,3] @> [1,3];
----
logical_plan
01)Projection: Boolean(true) AS array_has_all(make_array(Int64(1),Int64(2),Int64(3)),make_array(Int64(1),Int64(3)))
02)--EmptyRelation
physical_plan
01)ProjectionExec: expr=[true as array_has_all(make_array(Int64(1),Int64(2),Int64(3)),make_array(Int64(1),Int64(3)))]
02)--PlaceholderRowExec

# array containment operator with scalars #2 (arrow at)
query BBBBBBB
select make_array(1,3) <@ make_array(1,2,3),
       make_array(1,4) <@ make_array(1,2,3),
       make_array([1,2]) <@ make_array([1,2], [3,4]),
       make_array([1,3]) <@ make_array([1,2], [3,4]),
       make_array([1,2], [3,4], [5,6]) <@ make_array([1,2], [3,4]),
       make_array([[1]]) <@ make_array([[1,2,3]]),
       make_array([[1,2,3]]) <@ make_array([[1,2,3]]);
----
true false true false false false true

# Make sure it is rewritten to function array_has_all()
query TT
explain select [1,3] <@ [1,2,3];
----
logical_plan
01)Projection: Boolean(true) AS array_has_all(make_array(Int64(1),Int64(2),Int64(3)),make_array(Int64(1),Int64(3)))
02)--EmptyRelation
physical_plan
01)ProjectionExec: expr=[true as array_has_all(make_array(Int64(1),Int64(2),Int64(3)),make_array(Int64(1),Int64(3)))]
02)--PlaceholderRowExec

### Array casting tests


## make_array

# make_array scalar function #1
query ?
select make_array(1, 2.0)
----
[1.0, 2.0]

# make_array scalar function #2
query ?
select make_array(null, 1.0)
----
[NULL, 1.0]

# make_array scalar function #3
query ?
select make_array(1, 2.0, null, 3)
----
[1.0, 2.0, NULL, 3.0]

# make_array scalar function #4
query ?
select make_array(1.0, '2', null)
----
[1.0, 2.0, NULL]

### FixedSizeListArray

statement ok
CREATE EXTERNAL TABLE fixed_size_list_array STORED AS PARQUET LOCATION '../core/tests/data/fixed_size_list_array.parquet';

query T
select arrow_typeof(f0) from fixed_size_list_array;
----
FixedSizeList(Field { name: "item", data_type: Int64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }, 2)
FixedSizeList(Field { name: "item", data_type: Int64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }, 2)

query ?
select * from fixed_size_list_array;
----
[1, 2]
[3, 4]

query ?
select f0 from fixed_size_list_array;
----
[1, 2]
[3, 4]

query ?
select arrow_cast(f0, 'List(Int64)') from fixed_size_list_array;
----
[1, 2]
[3, 4]

query ?
select make_array(arrow_cast(f0, 'List(Int64)')) from fixed_size_list_array
----
[[1, 2]]
[[3, 4]]

query T
select arrow_typeof(make_array(arrow_cast(f0, 'List(Int64)'))) from fixed_size_list_array
----
List(Field { name: "item", data_type: List(Field { name: "item", data_type: Int64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }), nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} })
List(Field { name: "item", data_type: List(Field { name: "item", data_type: Int64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }), nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} })

query ?
select make_array(f0) from fixed_size_list_array
----
[[1, 2]]
[[3, 4]]

query T
select arrow_typeof(make_array(f0)) from fixed_size_list_array
----
List(Field { name: "item", data_type: FixedSizeList(Field { name: "item", data_type: Int64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }, 2), nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} })
List(Field { name: "item", data_type: FixedSizeList(Field { name: "item", data_type: Int64, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }, 2), nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} })

query ?
select array_concat(column1, [7]) from arrays_values_v2;
----
[NULL, 2, 3, 7]
[7]
[9, NULL, 10, 7]
[NULL, 1, 7]
[11, 12, 7]
[7]

# flatten

query ?
select flatten(NULL);
----
NULL

# flatten with scalar values #1
query ???
select flatten(make_array(1, 2, 1, 3, 2)),
       flatten(make_array([1], [2, 3], [null], make_array(4, null, 5))),
       flatten(make_array([[1.1]], [[2.2]], [[3.3], [4.4]]));
----
[1, 2, 1, 3, 2] [1, 2, 3, NULL, 4, NULL, 5] [[1.1], [2.2], [3.3], [4.4]]

query ???
select flatten(arrow_cast(make_array(1, 2, 1, 3, 2), 'LargeList(Int64)')),
       flatten(arrow_cast(make_array([1], [2, 3], [null], make_array(4, null, 5)), 'LargeList(LargeList(Int64))')),
       flatten(arrow_cast(make_array([[1.1]], [[2.2]], [[3.3], [4.4]]), 'LargeList(LargeList(LargeList(Float64)))'));
----
[1, 2, 1, 3, 2] [1, 2, 3, NULL, 4, NULL, 5] [[1.1], [2.2], [3.3], [4.4]]

query ???
select flatten(arrow_cast(make_array(1, 2, 1, 3, 2), 'FixedSizeList(5, Int64)')),
       flatten(arrow_cast(make_array([1], [2, 3], [null], make_array(4, null, 5)), 'FixedSizeList(4, List(Int64))')),
       flatten(arrow_cast(make_array([[1.1], [2.2]], [[3.3], [4.4]]), 'FixedSizeList(2, List(List(Float64)))'));
----
[1, 2, 1, 3, 2] [1, 2, 3, NULL, 4, NULL, 5] [[1.1], [2.2], [3.3], [4.4]]

# flatten with column values
query ????
select flatten(column1),
       flatten(column2),
       flatten(column3),
       flatten(column4)
from flatten_table;
----
[1, 2, 3] [[1, 2, 3], [4, 5], [6]] [[[1]], [[2, 3]]] [1.0, 2.1, 2.2, 3.2, 3.3, 3.4]
[1, 2, 3, 4, 5, 6] [[8]] [[[1, 2]], [[3]]] [1.0, 2.0, 3.0, 4.0, 5.0, 6.0]

query ????
select flatten(column1),
       flatten(column2),
       flatten(column3),
       flatten(column4)
from large_flatten_table;
----
[1, 2, 3] [[1, 2, 3], [4, 5], [6]] [[[1]], [[2, 3]]] [1.0, 2.1, 2.2, 3.2, 3.3, 3.4]
[1, 2, 3, 4, 5, 6] [[8]] [[[1, 2]], [[3]]] [1.0, 2.0, 3.0, 4.0, 5.0, 6.0]

query ????
select flatten(column1),
       flatten(column2),
       flatten(column3),
       flatten(column4)
from fixed_size_flatten_table;
----
[1, 2, 3] [[1, 2, 3], [4, 5], [6]] [[[1]], [[2, 3]]] [1.0, 2.1, 2.2, 3.2, 3.3, 3.4]
[1, 2, 3, 4, 5, 6] [[8], [9, 10], [11, 12, 13]] [[[1, 2]], [[3]]] [1.0, 2.0, 3.0, 4.0, 5.0, 6.0]

# flatten with different inner list type
query ??????
select flatten(arrow_cast(make_array([1, 2], [3, 4]), 'List(FixedSizeList(2, Int64))')),
       flatten(arrow_cast(make_array([[1, 2]], [[3, 4]]), 'List(FixedSizeList(1, List(Int64)))')),
       flatten(arrow_cast(make_array([1, 2], [3, 4]), 'LargeList(List(Int64))')),
       flatten(arrow_cast(make_array([[1, 2]], [[3, 4]]), 'LargeList(List(List(Int64)))')),
       flatten(arrow_cast(make_array([1, 2], [3, 4]), 'LargeList(FixedSizeList(2, Int64))')),
       flatten(arrow_cast(make_array([[1, 2]], [[3, 4]]), 'LargeList(FixedSizeList(1, List(Int64)))'))
----
[1, 2, 3, 4] [[1, 2], [3, 4]] [1, 2, 3, 4] [[1, 2], [3, 4]] [1, 2, 3, 4] [[1, 2], [3, 4]]

## empty (aliases: `array_empty`, `list_empty`)
# empty scalar function #1
query B
select empty(make_array(1));
----
false

query B
select empty(arrow_cast(make_array(1), 'LargeList(Int64)'));
----
false

query B
select empty(arrow_cast(make_array(1), 'FixedSizeList(1, Int64)'));
----
false

# empty scalar function #2
query B
select empty(make_array());
----
true

query B
select empty(arrow_cast(make_array(), 'LargeList(Int64)'));
----
true

#TODO: https://github.com/apache/datafusion/issues/9158
#query B
#select empty(arrow_cast(make_array(), 'FixedSizeList(0, Null)'));
#----
#true

# empty scalar function #3
query B
select empty(make_array(NULL));
----
false

query B
select empty(arrow_cast(make_array(NULL), 'LargeList(Int64)'));
----
false

query B
select empty(arrow_cast(make_array(NULL), 'FixedSizeList(1, Int64)'));
----
false

#TODO: https://github.com/apache/datafusion/issues/7142
# empty scalar function #4
#query B
#select empty(NULL);
#----
#NULL

# empty scalar function #5
query B
select empty(column1) from arrays;
----
false
false
false
false
NULL
false
false

query B
select empty(arrow_cast(column1, 'LargeList(List(Int64))')) from arrays;
----
false
false
false
false
NULL
false
false

query B
select empty(column1) from fixed_size_arrays;
----
false
false
false
false
NULL
false
false

## array_empty (aliases: `empty`, `list_empty`)
# array_empty scalar function #1
query B
select array_empty(make_array(1));
----
false

query B
select array_empty(arrow_cast(make_array(1), 'LargeList(Int64)'));
----
false

# array_empty scalar function #2
query B
select array_empty(make_array());
----
true

query B
select array_empty(arrow_cast(make_array(), 'LargeList(Int64)'));
----
true

# array_empty scalar function #3
query B
select array_empty(make_array(NULL));
----
false

query B
select array_empty(arrow_cast(make_array(NULL), 'LargeList(Int64)'));
----
false

## list_empty (aliases: `empty`, `array_empty`)
# list_empty scalar function #1
query B
select list_empty(make_array(1));
----
false

query B
select list_empty(arrow_cast(make_array(1), 'LargeList(Int64)'));
----
false

# list_empty scalar function #2
query B
select list_empty(make_array());
----
true

query B
select list_empty(arrow_cast(make_array(), 'LargeList(Int64)'));
----
true

# list_empty scalar function #3
query B
select list_empty(make_array(NULL));
----
false

query B
select list_empty(arrow_cast(make_array(NULL), 'LargeList(Int64)'));
----
false

# string_to_array scalar function
query ?
SELECT string_to_array('abcxxxdef', 'xxx')
----
[abc, def]

query ?
SELECT string_to_array('abc', '')
----
[abc]

query ?
SELECT string_to_array('abc', NULL)
----
[a, b, c]

query ?
SELECT string_to_array('abc def', ' ', 'def')
----
[abc, NULL]

query ?
select string_to_array(e, ',') from values;
----
[Lorem]
[ipsum]
[dolor]
[sit]
[amet]
[, ]
[consectetur]
[adipiscing]
NULL

# karge string tests for string_to_array

# string_to_array scalar function
query ?
SELECT string_to_array(arrow_cast('abcxxxdef', 'LargeUtf8'), 'xxx')
----
[abc, def]

# string_to_array scalar function
query ?
SELECT string_to_array(arrow_cast('abcxxxdef', 'LargeUtf8'), arrow_cast('xxx', 'LargeUtf8'))
----
[abc, def]

query ?
SELECT string_to_array(arrow_cast('abc', 'LargeUtf8'), NULL)
----
[a, b, c]

query ?
select string_to_array(arrow_cast(e, 'LargeUtf8'), ',') from values;
----
[Lorem]
[ipsum]
[dolor]
[sit]
[amet]
[, ]
[consectetur]
[adipiscing]
NULL

query ?
select string_to_array(arrow_cast(e, 'LargeUtf8'), ',', arrow_cast('Lorem', 'LargeUtf8')) from values;
----
[NULL]
[ipsum]
[dolor]
[sit]
[amet]
[, ]
[consectetur]
[adipiscing]
NULL

# string view tests for string_to_array

# string_to_array scalar function
query ?
SELECT string_to_array(arrow_cast('abcxxxdef', 'Utf8View'), 'xxx')
----
[abc, def]

query ?
SELECT string_to_array(arrow_cast('abc', 'Utf8View'), NULL)
----
[a, b, c]

query ?
select string_to_array(arrow_cast(e, 'Utf8View'), ',') from values;
----
[Lorem]
[ipsum]
[dolor]
[sit]
[amet]
[, ]
[consectetur]
[adipiscing]
NULL

# test string_to_array aliases

query ?
select string_to_list(e, 'm') from values;
----
[Lore, ]
[ipsu, ]
[dolor]
[sit]
[a, et]
[,]
[consectetur]
[adipiscing]
NULL

# array_resize scalar function #1
query ?
select array_resize(make_array(1, 2, 3), 1);
----
[1]

query ?
select array_resize(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)'), 1);
----
[1]

# array_resize scalar function #2
query ?
select array_resize(make_array(1, 2, 3), 5);
----
[1, 2, 3, NULL, NULL]

query ?
select array_resize(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)'), 5);
----
[1, 2, 3, NULL, NULL]

# array_resize scalar function #3
query ?
select array_resize(make_array(1, 2, 3), 5, 4);
----
[1, 2, 3, 4, 4]

query ?
select array_resize(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)'), 5, 4);
----
[1, 2, 3, 4, 4]

# array_resize scalar function #4
query error
select array_resize(make_array(1, 2, 3), -5, 2);

# array_resize scalar function #5
query ?
select array_resize(make_array(1.1, 2.2, 3.3), 10, 9.9);
----
[1.1, 2.2, 3.3, 9.9, 9.9, 9.9, 9.9, 9.9, 9.9, 9.9]

query ?
select array_resize(arrow_cast(make_array(1.1, 2.2, 3.3), 'LargeList(Float64)'), 10, 9.9);
----
[1.1, 2.2, 3.3, 9.9, 9.9, 9.9, 9.9, 9.9, 9.9, 9.9]

# array_resize scalar function #5
query ?
select array_resize(column1, column2, column3) from arrays_values;
----
[NULL]
[11, 12, 13, 14, 15, 16, 17, 18, NULL, 20, 2, 2]
[21, 22, 23, NULL, 25, 26, 27, 28, 29, 30, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
[31, 32, 33, 34, 35, NULL, 37, 38, 39, 40, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4]
NULL
[]
[51, 52, NULL, 54, 55, 56, 57, 58, 59, 60, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL]
[61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7]

query ?
select array_resize(arrow_cast(column1, 'LargeList(Int64)'), column2, column3) from arrays_values;
----
[NULL]
[11, 12, 13, 14, 15, 16, 17, 18, NULL, 20, 2, 2]
[21, 22, 23, NULL, 25, 26, 27, 28, 29, 30, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
[31, 32, 33, 34, 35, NULL, 37, 38, 39, 40, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4]
NULL
[]
[51, 52, NULL, 54, 55, 56, 57, 58, 59, 60, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL]
[61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7]

# array_resize scalar function #5
query ?
select array_resize([[1], [2], [3]], 10, [5]);
----
[[1], [2], [3], [5], [5], [5], [5], [5], [5], [5]]

query ?
select array_resize(arrow_cast([[1], [2], [3]], 'LargeList(List(Int64))'), 10, [5]);
----
[[1], [2], [3], [5], [5], [5], [5], [5], [5], [5]]

# array_resize null value
query ?
select array_resize(arrow_cast(NULL, 'List(Int8)'), 1);
----
NULL

statement ok
CREATE TABLE array_resize_values
AS VALUES
  (make_array(1, NULL, 3, 4, 5, 6, 7, 8, 9, 10), 2, 1),
  (make_array(11, 12, NULL, 14, 15, 16, 17, 18, 19, 20), 5, 2),
  (make_array(21, 22, 23, 24, NULL, 26, 27, 28, 29, 30), 8, 3),
  (make_array(31, 32, 33, 34, 35, 36, NULL, 38, 39, 40), 12, 4),
  (NULL, 3, 0),
  (make_array(41, 42, 43, 44, 45, 46, 47, 48, 49, 50), NULL, 6),
  (make_array(51, 52, 53, 54, 55, NULL, 57, 58, 59, 60), 13, NULL),
  (make_array(61, 62, 63, 64, 65, 66, 67, 68, 69, 70), 15, 7)
;

# array_resize columnar test #1
query ?
select array_resize(column1, column2, column3) from array_resize_values;
----
[1, NULL]
[11, 12, NULL, 14, 15]
[21, 22, 23, 24, NULL, 26, 27, 28]
[31, 32, 33, 34, 35, 36, NULL, 38, 39, 40, 4, 4]
NULL
[]
[51, 52, 53, 54, 55, NULL, 57, 58, 59, 60, NULL, NULL, NULL]
[61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 7, 7, 7, 7, 7]

# array_resize columnar test #2
query ?
select array_resize(arrow_cast(column1, 'LargeList(Int64)'), column2, column3) from array_resize_values;
----
[1, NULL]
[11, 12, NULL, 14, 15]
[21, 22, 23, 24, NULL, 26, 27, 28]
[31, 32, 33, 34, 35, 36, NULL, 38, 39, 40, 4, 4]
NULL
[]
[51, 52, 53, 54, 55, NULL, 57, 58, 59, 60, NULL, NULL, NULL]
[61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 7, 7, 7, 7, 7]

## array_reverse
query ??
select array_reverse(make_array(1, 2, 3)), array_reverse(make_array(1));
----
[3, 2, 1] [1]

query ??
select array_reverse(arrow_cast(make_array(1, 2, 3), 'LargeList(Int64)')), array_reverse(arrow_cast(make_array(1), 'LargeList(Int64)'));
----
[3, 2, 1] [1]

#TODO: support after FixedSizeList type coercion
#query ??
#select array_reverse(arrow_cast(make_array(1, 2, 3), 'FixedSizeList(3, Int64)')), array_reverse(arrow_cast(make_array(1), 'FixedSizeList(1, Int64)'));
#----
#[3, 2, 1] [1]

query ??
select array_reverse(NULL), array_reverse([]);
----
NULL []

query ??
select array_reverse(column1), column1 from arrays_values;
----
[10, 9, 8, 7, 6, 5, 4, 3, 2, NULL] [NULL, 2, 3, 4, 5, 6, 7, 8, 9, 10]
[20, NULL, 18, 17, 16, 15, 14, 13, 12, 11] [11, 12, 13, 14, 15, 16, 17, 18, NULL, 20]
[30, 29, 28, 27, 26, 25, NULL, 23, 22, 21] [21, 22, 23, NULL, 25, 26, 27, 28, 29, 30]
[40, 39, 38, 37, NULL, 35, 34, 33, 32, 31] [31, 32, 33, 34, 35, NULL, 37, 38, 39, 40]
NULL NULL
[50, 49, 48, 47, 46, 45, 44, 43, 42, 41] [41, 42, 43, 44, 45, 46, 47, 48, 49, 50]
[60, 59, 58, 57, 56, 55, 54, NULL, 52, 51] [51, 52, NULL, 54, 55, 56, 57, 58, 59, 60]
[70, 69, 68, 67, 66, 65, 64, 63, 62, 61] [61, 62, 63, 64, 65, 66, 67, 68, 69, 70]


# Test defining a table with array columns
statement ok
create table test_create_array_table(
  a int[],
  b text[],
  -- two-dimensional array
  c int[][],
  d int
);

query I
insert into test_create_array_table values
  ([1, 2, 3], ['a', 'b', 'c'], [[4,6], [6,7,8]], 1);
----
1

query ???I
select * from test_create_array_table;
----
[1, 2, 3] [a, b, c] [[4, 6], [6, 7, 8]] 1

query T
select arrow_typeof(a) from test_create_array_table;
----
List(Field { name: "item", data_type: Int32, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} })

query T
select arrow_typeof(c) from test_create_array_table;
----
List(Field { name: "item", data_type: List(Field { name: "item", data_type: Int32, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }), nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} })

# Test casting to array types
# issue: https://github.com/apache/datafusion/issues/9440
query ??T
select [1,2,3]::int[], [['1']]::int[][], arrow_typeof([]::text[]);
----
[1, 2, 3] [[1]] List(Field { name: "item", data_type: Utf8, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} })

# test empty arrays return length
# issue: https://github.com/apache/datafusion/pull/12459
statement ok
create table values_all_empty (a int[]) as values ([]), ([]);

query B
select array_has(a, 1) from values_all_empty;
----
false
false

# Test create table with fixed sized array
statement ok
create table fixed_size_col_table (a int[3]) as values ([1,2,3]), ([4,5,6]);

query T
select arrow_typeof(a) from fixed_size_col_table;
----
FixedSizeList(Field { name: "item", data_type: Int32, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }, 3)
FixedSizeList(Field { name: "item", data_type: Int32, nullable: true, dict_id: 0, dict_is_ordered: false, metadata: {} }, 3)

statement error
create table varying_fixed_size_col_table (a int[3]) as values ([1,2,3]), ([4,5]);

### Delete tables

statement ok
drop table values;

statement ok
drop table values_without_nulls;

statement ok
drop table nested_arrays;

statement ok
drop table large_nested_arrays;

statement ok
drop table fixed_size_nested_arrays;

statement ok
drop table arrays;

statement ok
drop table large_arrays;

statement ok
drop table fixed_size_arrays;

statement ok
drop table slices;

statement ok
drop table fixed_slices;

statement ok
drop table arrayspop;

statement ok
drop table large_arrayspop;

statement ok
drop table arrays_values;

statement ok
drop table arrays_values_v2;

statement ok
drop table large_arrays_values_v2;

statement ok
drop table array_has_table_1D;

statement ok
drop table array_has_table_1D_Float;

statement ok
drop table array_has_table_1D_Boolean;

statement ok
drop table array_has_table_1D_UTF8;

statement ok
drop table array_has_table_2D;

statement ok
drop table array_has_table_2D_float;

statement ok
drop table array_has_table_3D;

statement ok
drop table array_intersect_table_1D;

statement ok
drop table large_array_intersect_table_1D;

statement ok
drop table array_intersect_table_1D_Float;

statement ok
drop table large_array_intersect_table_1D_Float;

statement ok
drop table array_intersect_table_1D_Boolean;

statement ok
drop table large_array_intersect_table_1D_Boolean;

statement ok
drop table array_intersect_table_1D_UTF8;

statement ok
drop table large_array_intersect_table_1D_UTF8;

statement ok
drop table array_intersect_table_2D;

statement ok
drop table large_array_intersect_table_2D;

statement ok
drop table array_intersect_table_2D_float;

statement ok
drop table large_array_intersect_table_2D_float;

statement ok
drop table array_intersect_table_3D;

statement ok
drop table large_array_intersect_table_3D;

statement ok
drop table fixed_size_array_has_table_1D;

statement ok
drop table fixed_size_array_has_table_1D_Float;

statement ok
drop table fixed_size_array_has_table_1D_Boolean;

statement ok
drop table fixed_size_array_has_table_1D_UTF8;

statement ok
drop table fixed_size_array_has_table_2D;

statement ok
drop table fixed_size_array_has_table_2D_float;

statement ok
drop table fixed_size_array_has_table_3D;

statement ok
drop table arrays_range;

statement ok
drop table arrays_with_repeating_elements;

statement ok
drop table large_arrays_with_repeating_elements;

statement ok
drop table fixed_arrays_with_repeating_elements;

statement ok
drop table nested_arrays_with_repeating_elements;

statement ok
drop table large_nested_arrays_with_repeating_elements;

statement ok
drop table fixed_size_nested_arrays_with_repeating_elements;

statement ok
drop table flatten_table;

statement ok
drop table large_flatten_table;

statement ok
drop table fixed_size_flatten_table;

statement ok
drop table arrays_values_without_nulls;

statement ok
drop table large_arrays_values_without_nulls;

statement ok
drop table fixed_size_arrays_values_without_nulls;

statement ok
drop table test_create_array_table;

statement ok
drop table values_all_empty;

statement ok
drop table fixed_size_col_table;
