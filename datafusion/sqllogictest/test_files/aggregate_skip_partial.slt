# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# The main goal of these tests is to verify correctness of transforming
# input values to state by accumulators, supporting `convert_to_state`.


# Setup test data table
statement ok
CREATE EXTERNAL TABLE aggregate_test_100 (
  c1  VARCHAR NOT NULL,
  c2  TINYINT NOT NULL,
  c3  SMALLINT NOT NULL,
  c4  SMALLINT,
  c5  INT,
  c6  BIGINT NOT NULL,
  c7  SMALLINT NOT NULL,
  c8  INT NOT NULL,
  c9  INT UNSIGNED NOT NULL,
  c10 BIGINT UNSIGNED NOT NULL,
  c11 FLOAT NOT NULL,
  c12 DOUBLE NOT NULL,
  c13 VARCHAR NOT NULL
)
STORED AS CSV
LOCATION '../../testing/data/csv/aggregate_test_100.csv'
OPTIONS ('format.has_header' 'true');

# Table to test `bool_and()`, `bool_or()` aggregate functions
statement ok
CREATE TABLE aggregate_test_100_bool (
  v1 VARCHAR NOT NULL,
  v2 BOOLEAN,
  v3 BOOLEAN
);

statement ok
INSERT INTO aggregate_test_100_bool
SELECT
  c1 as v1,
  CASE WHEN c2 > 3 THEN TRUE WHEN c2 > 1 THEN FALSE ELSE NULL END as v2,
  CASE WHEN c1='a' OR c1='b' THEN TRUE WHEN c1='c' OR c1='d' THEN FALSE ELSE NULL END as v3
FROM aggregate_test_100;

# Prepare settings to skip partial aggregation from the beginning
statement ok
set datafusion.execution.skip_partial_aggregation_probe_rows_threshold = 0;

statement ok
set datafusion.execution.skip_partial_aggregation_probe_ratio_threshold = 0.0;

statement ok
set datafusion.execution.target_partitions = 2;

statement ok
set datafusion.execution.batch_size = 1;

statement ok
set datafusion.sql_parser.dialect = 'Postgres';

# Grouping by unique fields allows to check all accumulators
query ITIIII
SELECT c5, c1,
       COUNT(),
       COUNT(CASE WHEN c1 = 'a' THEN c5 ELSE NULL END),
       COUNT() FILTER (WHERE c1 = 'b'),
       COUNT(CASE WHEN c1 = 'a' THEN c5 ELSE NULL END) FILTER (WHERE c1 = 'b')
FROM aggregate_test_100
GROUP BY 1, 2 ORDER BY 1 LIMIT 5;
----
-2141999138 c 1 0 0 0
-2141451704 a 1 1 0 0
-2138770630 b 1 0 1 0
-2117946883 d 1 0 0 0
-2098805236 c 1 0 0 0

query ITIIII
SELECT c5, c1,
       MIN(c5),
       MIN(CASE WHEN c1 = 'a' THEN c5 ELSE NULL END),
       MIN(c5) FILTER (WHERE c1 = 'b'),
       MIN(CASE WHEN c1 = 'a' THEN c5 ELSE NULL END) FILTER (WHERE c1 = 'b')
FROM aggregate_test_100
GROUP BY 1, 2 ORDER BY 1 LIMIT 5;
----
-2141999138 c -2141999138 NULL NULL NULL
-2141451704 a -2141451704 -2141451704 NULL NULL
-2138770630 b -2138770630 NULL -2138770630 NULL
-2117946883 d -2117946883 NULL NULL NULL
-2098805236 c -2098805236 NULL NULL NULL

query ITIIII
SELECT c5, c1,
       MAX(c5),
       MAX(CASE WHEN c1 = 'a' THEN c5 ELSE NULL END),
       MAX(c5) FILTER (WHERE c1 = 'b'),
       MAX(CASE WHEN c1 = 'a' THEN c5 ELSE NULL END) FILTER (WHERE c1 = 'b')
FROM aggregate_test_100
GROUP BY 1, 2 ORDER BY 1 LIMIT 5;
----
-2141999138 c -2141999138 NULL NULL NULL
-2141451704 a -2141451704 -2141451704 NULL NULL
-2138770630 b -2138770630 NULL -2138770630 NULL
-2117946883 d -2117946883 NULL NULL NULL
-2098805236 c -2098805236 NULL NULL NULL

query ITIIII
SELECT c5, c1,
       SUM(c5),
       SUM(CASE WHEN c1 = 'a' THEN c5 ELSE NULL END),
       SUM(c5) FILTER (WHERE c1 = 'b'),
       SUM(CASE WHEN c1 = 'a' THEN c5 ELSE NULL END) FILTER (WHERE c1 = 'b')
FROM aggregate_test_100
GROUP BY 1, 2 ORDER BY 1 LIMIT 5;
----
-2141999138 c -2141999138 NULL NULL NULL
-2141451704 a -2141451704 -2141451704 NULL NULL
-2138770630 b -2138770630 NULL -2138770630 NULL
-2117946883 d -2117946883 NULL NULL NULL
-2098805236 c -2098805236 NULL NULL NULL

query ITIIII
SELECT c5, c1,
       MEDIAN(c5),
       MEDIAN(CASE WHEN c1 = 'a' THEN c5 ELSE NULL END),
       MEDIAN(c5) FILTER (WHERE c1 = 'b'),
       MEDIAN(CASE WHEN c1 = 'a' THEN c5 ELSE NULL END) FILTER (WHERE c1 = 'b')
FROM aggregate_test_100
GROUP BY 1, 2 ORDER BY 1 LIMIT 5;
----
-2141999138 c -2141999138 NULL NULL NULL
-2141451704 a -2141451704 -2141451704 NULL NULL
-2138770630 b -2138770630 NULL -2138770630 NULL
-2117946883 d -2117946883 NULL NULL NULL
-2098805236 c -2098805236 NULL NULL NULL

query ITIIII
SELECT c5, c1,
       APPROX_MEDIAN(c5),
       APPROX_MEDIAN(CASE WHEN c1 = 'a' THEN c5 ELSE NULL END),
       APPROX_MEDIAN(c5) FILTER (WHERE c1 = 'b'),
       APPROX_MEDIAN(CASE WHEN c1 = 'a' THEN c5 ELSE NULL END) FILTER (WHERE c1 = 'b')
FROM aggregate_test_100
GROUP BY 1, 2 ORDER BY 1 LIMIT 5;
----
-2141999138 c -2141999138 NULL NULL NULL
-2141451704 a -2141451704 -2141451704 NULL NULL
-2138770630 b -2138770630 NULL -2138770630 NULL
-2117946883 d -2117946883 NULL NULL NULL
-2098805236 c -2098805236 NULL NULL NULL

query ITIIII
SELECT c5, c1,
       APPROX_DISTINCT(c5),
       APPROX_DISTINCT(CASE WHEN c1 = 'a' THEN c5 ELSE NULL END),
       APPROX_DISTINCT(c5) FILTER (WHERE c1 = 'b'),
       APPROX_DISTINCT(CASE WHEN c1 = 'a' THEN c5 ELSE NULL END) FILTER (WHERE c1 = 'b')
FROM aggregate_test_100
GROUP BY 1, 2 ORDER BY 1 LIMIT 5;
----
-2141999138 c 1 0 0 0
-2141451704 a 1 1 0 0
-2138770630 b 1 0 1 0
-2117946883 d 1 0 0 0
-2098805236 c 1 0 0 0

# FIXME: add bool_and(v3) column when issue fixed
# ISSUE https://github.com/apache/datafusion/issues/11846
query TBBB rowsort
select v1, bool_or(v2), bool_and(v2), bool_or(v3)
from aggregate_test_100_bool
group by v1
----
a true false true
b true false true
c true false false
d true false false
e true false NULL

query TBBB rowsort
select v1,
      bool_or(v2) FILTER (WHERE v1 = 'a' OR v1 = 'c' OR v1 = 'e'),
      bool_or(v2) FILTER (WHERE v2 = false),
      bool_or(v2) FILTER (WHERE v2 = NULL)
from aggregate_test_100_bool
group by v1
----
a true false NULL
b NULL false NULL
c true false NULL
d NULL false NULL
e true false NULL

# Prepare settings to always skip aggregation after couple of batches
statement ok
set datafusion.execution.skip_partial_aggregation_probe_rows_threshold = 10;

statement ok
set datafusion.execution.skip_partial_aggregation_probe_ratio_threshold = 0.0;

statement ok
set datafusion.execution.target_partitions = 2;

statement ok
set datafusion.execution.batch_size = 4;

# Inserting into nullable table with batch_size specified above
# to prevent creation on single in-memory batch

statement ok
CREATE TABLE aggregate_test_100_null (
  c2  TINYINT NOT NULL,
  c5  INT NOT NULL,
  c3  SMALLINT,
  c11 FLOAT
);

statement ok
INSERT INTO aggregate_test_100_null
SELECT
  c2,
  c5,
  CASE WHEN c1 = 'e' THEN NULL ELSE c3 END as c3,
  CASE WHEN c1 = 'a' THEN NULL ELSE c11 END as c11
FROM aggregate_test_100;

# Test count varchar / int / float
query IIII
SELECT c2, count(c1), count(c5), count(c11) FROM aggregate_test_100 GROUP BY c2 ORDER BY c2;
----
1 22 22 22
2 22 22 22
3 19 19 19
4 23 23 23
5 14 14 14

# Test min / max for int / float
query IIIRR
SELECT c2, min(c5), max(c5), min(c11), max(c11) FROM aggregate_test_100 GROUP BY c2 ORDER BY c2;
----
1 -1991133944 2143473091 0.064453244 0.89651865
2 -2138770630 2053379412 0.055064857 0.8315913
3 -2141999138 2030965207 0.034291923 0.9488028
4 -1885422396 2064155045 0.028003037 0.7459874
5 -2117946883 2025611582 0.12559289 0.87989986

# Test sum for int / float
query IIR
SELECT c2, sum(c5), sum(c11) FROM aggregate_test_100 GROUP BY c2 ORDER BY c2;
----
1 -438598674 12.153253793716
2 -8259865364 9.577824473381
3 1956035476 9.590891361237
4 16155718643 9.531112968922
5 6449337880 7.074412226677

# Test median for int / float
query IIR
SELECT c2, median(c5), median(c11) FROM aggregate_test_100 GROUP BY c2 ORDER BY c2;
----
1 23971150 0.5922606
2 -562486880 0.43422085
3 240273900 0.40199697
4 762932956 0.48515016
5 604973998 0.49842384

# Test approx_median for int / float
query IIR
SELECT c2, approx_median(c5), approx_median(c11) FROM aggregate_test_100 GROUP BY c2 ORDER BY c2;
----
1 191655437 0.59926736
2 -587831330 0.43230486
3 240273900 0.40199697
4 762932956 0.48515016
5 593204320 0.5156586

# Test approx_distinct for varchar / int
query III
SELECT c2, approx_distinct(c1), approx_distinct(c5) FROM aggregate_test_100 GROUP BY c2 ORDER BY c2;
----
1 5 22
2 5 22
3 5 19
4 5 23
5 5 14

# Test approx_distinct for varchar(with Utf8View) / int
statement ok
CREATE TABLE aggregate_test_100_utf8view AS SELECT
      arrow_cast(c1, 'Utf8View') as c1,
      c2,
      c5
FROM aggregate_test_100;

# Test approx_distinct for varchar(with Utf8View) / int
query III
SELECT c2, approx_distinct(c1), approx_distinct(c5) FROM aggregate_test_100_utf8view GROUP BY c2 ORDER BY c2;
----
1 5 22
2 5 22
3 5 19
4 5 23
5 5 14

statement ok
DROP TABLE aggregate_test_100_utf8view;

# Test count with nullable fields
query III
SELECT c2, count(c3), count(c11) FROM aggregate_test_100_null GROUP BY c2 ORDER BY c2;
----
1 19 17
2 17 19
3 15 13
4 16 19
5 12 11

# Test min / max with nullable fields
query IIIRR
SELECT c2, min(c3), max(c3), min(c11), max(c11) FROM aggregate_test_100_null GROUP BY c2 ORDER BY c2;
----
1 -99 125 0.064453244 0.89651865
2 -117 122 0.09683716 0.8315913
3 -101 123 0.034291923 0.94669616
4 -117 123 0.028003037 0.7085086
5 -101 118 0.12559289 0.87989986

# Test sum with nullable fields
query IIR
SELECT c2, sum(c3), sum(c11) FROM aggregate_test_100 GROUP BY c2 ORDER BY c2;
----
1 367 12.153253793716
2 184 9.577824473381
3 395 9.590891361237
4 29 9.531112968922
5 -194 7.074412226677

# Test median with nullable fields
query IIR
SELECT c2, median(c3), median(c11) FROM aggregate_test_100_null GROUP BY c2 ORDER BY c2;
----
1 12 0.6067944
2 1 0.46076488
3 14 0.40154034
4 -17 0.48515016
5 -35 0.5536642

# Test approx_median with nullable fields
query IIR
SELECT c2, approx_median(c3), approx_median(c11) FROM aggregate_test_100_null GROUP BY c2 ORDER BY c2;
----
1 12 0.6067944
2 1 0.46076488
3 14 0.40154034
4 -7 0.48515016
5 -39 0.5536642

# Test approx_distinct with nullable fields
query II
SELECT c2, approx_distinct(c3) FROM aggregate_test_100_null GROUP BY c2 ORDER BY c2;
----
1 19
2 16
3 13
4 16
5 12

# Test avg for tinyint / float
query TRR
SELECT
  c1,
  avg(c2),
  avg(c11)
FROM aggregate_test_100 GROUP BY c1 ORDER BY c1;
----
a 2.857142857143 0.438223421574
b 3.263157894737 0.496481208425
c 2.666666666667 0.425241138254
d 2.444444444444 0.541519476308
e 3 0.505440263521

# FIXME: add bool_and(v3) column when issue fixed
# ISSUE https://github.com/apache/datafusion/issues/11846
query TBBB rowsort
select v1, bool_or(v2), bool_and(v2), bool_or(v3)
from aggregate_test_100_bool
group by v1
----
a true false true
b true false true
c true false false
d true false false
e true false NULL

query TBBB rowsort
select v1,
      bool_or(v2) FILTER (WHERE v1 = 'a' OR v1 = 'c' OR v1 = 'e'),
      bool_or(v2) FILTER (WHERE v2 = false),
      bool_or(v2) FILTER (WHERE v2 = NULL)
from aggregate_test_100_bool
group by v1
----
a true false NULL
b NULL false NULL
c true false NULL
d NULL false NULL
e true false NULL

# Enabling PG dialect for filtered aggregates tests
statement ok
set datafusion.sql_parser.dialect = 'Postgres';

# Test count with filter
query III
SELECT
  c2,
  count(c3) FILTER (WHERE c3 > 0),
  count(c3) FILTER (WHERE c11 > 10)
FROM aggregate_test_100 GROUP BY c2 ORDER BY c2;
----
1 13 0
2 13 0
3 13 0
4 13 0
5 5 0

# Test min / max with filter
query III
SELECT
  c2,
  min(c3) FILTER (WHERE c3 > 0),
  max(c3) FILTER (WHERE c3 < 0)
FROM aggregate_test_100 GROUP BY c2 ORDER BY c2;
----
1 12 -5
2 1 -29
3 13 -2
4 3 -38
5 36 -5

# Test sum with filter
query II
SELECT
  c2,
  sum(c3) FILTER (WHERE c1 != 'e' AND c3 > 0)
FROM aggregate_test_100 GROUP BY c2 ORDER BY c2;
----
1 612
2 565
3 466
4 417
5 284

# Test approx_distinct with filter
query III
SELECT
  c2,
  approx_distinct(c3) FILTER (WHERE c3 > 0),
  approx_distinct(c3) FILTER (WHERE c11 > 10)
FROM aggregate_test_100 GROUP BY c2 ORDER BY c2;
----
1 13 0
2 12 0
3 11 0
4 13 0
5 5 0

# Test median with filter
query III
SELECT
  c2,
  median(c3) FILTER (WHERE c3 > 0),
  median(c3) FILTER (WHERE c3 < 0)
FROM aggregate_test_100 GROUP BY c2 ORDER BY c2;
----
1 57 -56
2 52 -60
3 71 -74
4 65 -69
5 64 -59

# Test approx_median with filter
query III
SELECT
  c2,
  approx_median(c3) FILTER (WHERE c3 > 0),
  approx_median(c3) FILTER (WHERE c3 < 0)
FROM aggregate_test_100 GROUP BY c2 ORDER BY c2;
----
1 57 -56
2 52 -60
3 71 -76
4 65 -64
5 64 -59

# Test count with nullable fields and filter
query III
SELECT c2,
       COUNT(c3) FILTER (WHERE c5 > 0),
       COUNT(c11) FILTER(WHERE c5 > 0)
FROM aggregate_test_100_null GROUP BY c2 ORDER BY c2;
----
1 11 6
2 6 6
3 8 6
4 11 14
5 8 7

# Test avg for tinyint / float
query TRR
SELECT
  c1,
  avg(c2) FILTER (WHERE c2 != 5),
  avg(c11) FILTER (WHERE c2 != 5)
FROM aggregate_test_100 GROUP BY c1 ORDER BY c1;
----
a 2.5 0.449071887467
b 2.642857142857 0.445486298629
c 2.421052631579 0.422882117723
d 2.125 0.518706191331
e 2.789473684211 0.536785323369

# Test count with nullable fields and nullable filter
query III
SELECT c2,
       COUNT(c3) FILTER (WHERE c11 > 0.5),
       COUNT(c11) FILTER(WHERE c3 > 0)
FROM aggregate_test_100_null GROUP BY c2 ORDER BY c2;
----
1 10 9
2 7 8
3 3 6
4 3 7
5 6 3

# Test min / max with nullable fields and filter
query IIIRR
SELECT c2,
       MIN(c3) FILTER (WHERE c5 > 0),
       MAX(c3) FILTER (WHERE c5 > 0),
       MIN(c11) FILTER (WHERE c5 < 0),
       MAX(c11) FILTER (WHERE c5 < 0)
FROM aggregate_test_100_null GROUP BY c2 ORDER BY c2;
----
1 -99 103 0.2578469 0.89651865
2 -48 93 0.09683716 0.8315913
3 -76 123 0.034291923 0.94669616
4 -117 123 0.06563997 0.57360977
5 -94 68 0.12559289 0.75173044

# Test min / max with nullable fields and nullable filter
query III
SELECT c2,
       MIN(c3) FILTER (WHERE c11 > 0.5),
       MAX(c3) FILTER (WHERE c11 > 0.5)
FROM aggregate_test_100_null GROUP BY c2 ORDER BY c2;
----
1 -99 125
2 -106 122
3 -76 73
4 -117 47
5 -82 118

# Test sum with nullable field and nullable / non-nullable filters
query IIIRR
SELECT c2,
       SUM(c3) FILTER (WHERE c5 > 0),
       SUM(c3) FILTER (WHERE c11 < 0.5),
       SUM(c11) FILTER (WHERE c5 < 0),
       SUM(c11) FILTER (WHERE c3 > 0)
FROM aggregate_test_100_null GROUP BY c2 ORDER BY c2;
----
1 -3 77 7.214695632458 5.085060358047
2 100 77 6.197732746601 3.150197088718
3 109 211 2.80575042963 2.80632930994
4 -171 56 2.10740506649 1.939846396446
5 -86 -76 1.8741710186 1.600569307804

# Test approx_distinct with nullable fields and filter
query II
SELECT c2,
       approx_distinct(c3) FILTER (WHERE c5 > 0)
FROM aggregate_test_100_null GROUP BY c2 ORDER BY c2;
----
1 11
2 6
3 6
4 11
5 8

# Test approx_distinct with nullable fields and nullable filter
query II
SELECT c2,
       approx_distinct(c3) FILTER (WHERE c11 > 0.5)
FROM aggregate_test_100_null GROUP BY c2 ORDER BY c2;
----
1 10
2 6
3 3
4 3
5 6

# Test median with nullable fields and filter
query IIR
SELECT c2,
       median(c3) FILTER (WHERE c5 > 0),
       median(c11) FILTER (WHERE c5 < 0)
FROM aggregate_test_100_null GROUP BY c2 ORDER BY c2;
----
1 -5 0.6623719
2 15 0.52930677
3 13 0.32792538
4 -38 0.49774808
5 -18 0.49842384

# Test min / max with nullable fields and nullable filter
query II
SELECT c2,
       median(c3) FILTER (WHERE c11 > 0.5)
FROM aggregate_test_100_null GROUP BY c2 ORDER BY c2;
----
1 33
2 -29
3 22
4 -90
5 -22

# Test approx_median with nullable fields and filter
query IIR
SELECT c2,
       approx_median(c3) FILTER (WHERE c5 > 0),
       approx_median(c11) FILTER (WHERE c5 < 0)
FROM aggregate_test_100_null GROUP BY c2 ORDER BY c2;
----
1 -5 0.6623719
2 12 0.52930677
3 13 0.32792538
4 -38 0.49774808
5 -21 0.47652745

# Test approx_median with nullable fields and nullable filter
query II
SELECT c2,
       approx_median(c3) FILTER (WHERE c11 > 0.5)
FROM aggregate_test_100_null GROUP BY c2 ORDER BY c2;
----
1 35
2 -29
3 22
4 -90
5 -32

statement ok
DROP TABLE aggregate_test_100_null;

# Test for aggregate functions with different intermediate types
# Need more than 10 values to trigger skipping
statement ok
CREATE TABLE decimal_table(i int, d decimal(10,3)) as
VALUES (1, 1.1), (2, 2.2), (3, 3.3), (2, 4.4), (1, 5.5);

statement ok
CREATE TABLE t(id int) as values (1), (2), (3), (4), (5), (6), (7), (8), (9), (10);

query IR
SELECT i, sum(d)
FROM decimal_table CROSS JOIN t
GROUP BY i
ORDER BY i;
----
1 66
2 66
3 33

statement ok
DROP TABLE decimal_table;

# Extra tests for 'bool_*()' edge cases
statement ok
set datafusion.execution.skip_partial_aggregation_probe_rows_threshold = 0;

statement ok
set datafusion.execution.skip_partial_aggregation_probe_ratio_threshold = 0.0;

statement ok
set datafusion.execution.target_partitions = 1;

statement ok
set datafusion.execution.batch_size = 1;

statement ok
create table bool_aggregate_functions (
  c1 boolean not null,
  c2 boolean not null,
  c3 boolean not null,
  c4 boolean not null,
  c5 boolean,
  c6 boolean,
  c7 boolean,
  c8 boolean
)
as values
  (true, true, false, false, true, true, null, null),
  (true, false, true, false, false, null, false, null),
  (true, true, false, false, null, true, false, null);

query BBBBBBBB
SELECT bool_and(c1), bool_and(c2), bool_and(c3), bool_and(c4), bool_and(c5), bool_and(c6), bool_and(c7), bool_and(c8) FROM bool_aggregate_functions
----
true false false false false true false NULL

statement ok
set datafusion.execution.skip_partial_aggregation_probe_rows_threshold = 2;

query BBBBBBBB
SELECT bool_and(c1), bool_and(c2), bool_and(c3), bool_and(c4), bool_and(c5), bool_and(c6), bool_and(c7), bool_and(c8) FROM bool_aggregate_functions
----
true false false false false true false NULL

statement ok
DROP TABLE aggregate_test_100_bool
