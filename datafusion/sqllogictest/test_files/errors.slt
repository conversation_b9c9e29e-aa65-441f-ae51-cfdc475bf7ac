# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.


# create aggregate_test_100 table
statement ok
CREATE EXTERNAL TABLE aggregate_test_100 (
  c1  VARCHAR NOT NULL,
  c2  TINYINT NOT NULL,
  c3  SMALLINT NOT NULL,
  c4  SMALLINT,
  c5  INT,
  c6  BIGINT NOT NULL,
  c7  SMALLINT NOT NULL,
  c8  INT NOT NULL,
  c9  BIGINT UNSIGNED NOT NULL,
  c10 VARCHAR NOT NULL,
  c11 FLOAT NOT NULL,
  c12 DOUBLE NOT NULL,
  c13 VARCHAR NOT NULL
)
STORED AS CSV
LOCATION '../../testing/data/csv/aggregate_test_100.csv'
OPTIONS ('format.has_header' 'true');

# csv_query_error
statement error
SELECT sin(c1) FROM aggregate_test_100

# cast_expressions_error
statement error DataFusion error: Arrow error: Cast error: Cannot cast string 'c' to value of Int32 type
SELECT CAST(c1 AS INT) FROM aggregate_test_100

# aggregation_with_bad_arguments
query error
SELECT COUNT(DISTINCT) FROM aggregate_test_100

# query_cte_incorrect
statement error Error during planning: table 'datafusion\.public\.t' not found
WITH t AS (SELECT * FROM t) SELECT * from u

statement error Error during planning: table 'datafusion\.public\.u' not found
WITH t AS (SELECT * FROM u), u AS (SELECT 1) SELECT * from u

statement error Error during planning: table 'datafusion\.public\.u' not found
WITH t AS (WITH u as (SELECT 1) SELECT 1) SELECT * from u

# select_wildcard_without_table
statement error Error during planning: SELECT \* with no tables specified is not valid
SELECT * 

# invalid_qualified_table_references
statement error Error during planning: table 'datafusion\.nonexistentschema\.aggregate_test_100' not found
SELECT COUNT(*) FROM nonexistentschema.aggregate_test_100

statement error Error during planning: table 'nonexistentcatalog\.public\.aggregate_test_100' not found
SELECT COUNT(*) FROM nonexistentcatalog.public.aggregate_test_100

statement error DataFusion error: Error during planning: Unsupported compound identifier 'way\.too\.many\.namespaces\.as\.ident\.prefixes\.aggregate_test_100'\. Expected 1, 2 or 3 parts, got 8
SELECT COUNT(*) FROM way.too.many.namespaces.as.ident.prefixes.aggregate_test_100



#
# Wrong scalar function signature
#

# error message for wrong function signature (Variadic: arbitrary number of args all from some common types)
statement error
SELECT concat();

# error message for wrong function signature (Uniform: t args all from some common types)
statement error
SELECT nullif(1);

# error message for wrong function signature (Exact: exact number of args of an exact type)
statement error
SELECT pi(3.14);

# error message for wrong function signature (Any: fixed number of args of arbitrary types)
statement error
SELECT arrow_typeof(1, 1);

# error message for wrong function signature (OneOf: fixed number of args of arbitrary types)
statement error
SELECT power(1, 2, 3);

#
# Wrong window/aggregate function signature
#

# AggregateFunction with wrong number of arguments
query error
select avg(c1, c12) from aggregate_test_100;

# AggregateFunction with wrong argument type
statement error DataFusion error: Error during planning: Failed to coerce arguments to satisfy a call to 'regr_slope' function: coercion from
select regr_slope(1, '2');

# WindowFunction using AggregateFunction wrong signature
statement error DataFusion error: Error during planning: Failed to coerce arguments to satisfy a call to 'regr_slope' function: coercion from
select
c9,
regr_slope(c11, '2') over () as min1
from aggregate_test_100
order by c9

# WindowFunction wrong signature
statement error DataFusion error: Error during planning: Failed to coerce arguments to satisfy a call to 'nth_value' function: coercion from \[Int32, Int64, Int64\] to the signature OneOf\(\[Any\(0\), Any\(1\), Any\(2\)\]\) failed
select
c9,
nth_value(c5, 2, 3) over (order by c9) as nv1
from aggregate_test_100
order by c9


# nth_value with wrong name
statement error DataFusion error: Error during planning: Invalid function 'nth_vlue'.\nDid you mean 'nth_value'?
SELECT
   NTH_VLUE(c4, 2) OVER()
   FROM aggregate_test_100
   ORDER BY c9
   LIMIT 5;

# first_value with wrong name
statement error DataFusion error: Error during planning: Invalid function 'frst_value'.\nDid you mean 'first_value'?
SELECT
   FRST_VALUE(c4, 2) OVER()
   FROM aggregate_test_100
   ORDER BY c9
   LIMIT 5;


query error DataFusion error: Arrow error: Cast error: Cannot cast string 'foo' to value of Int64 type
create table foo as values (1), ('foo');

query error user-defined coercion failed
select 1 group by substr('');

# Error in filter should be reported
query error Divide by zero
SELECT c2 from aggregate_test_100 where CASE WHEN true THEN 1 / 0 ELSE 0 END = 1;


statement error DataFusion error: Error during planning: Inconsistent data length across values list: got 4 values in row 0 but expected 2
create table records (timestamp timestamp, value float) as values (
    '2021-01-01 00:00:00', 1.0,
    '2021-01-01 00:00:00', 2.0
);

statement ok
CREATE TABLE tab0(col0 INTEGER, col1 INTEGER, col2 INTEGER);

statement ok
INSERT INTO tab0 VALUES(83,0,38);

query error DataFusion error: Arrow error: Divide by zero error
SELECT DISTINCT - 84 FROM tab0 AS cor0 WHERE NOT + 96 / + col1 <= NULL GROUP BY col1, col0;

statement ok
create table a(timestamp int, birthday int, ts int, tokens int, amp int, staamp int);

query error DataFusion error: Schema error: No field named timetamp\. Did you mean 'a\.timestamp'\?\.
select timetamp from a;

query error DataFusion error: Schema error: No field named dadsada\. Valid fields are a\.timestamp, a\.birthday, a\.ts, a\.tokens, a\.amp, a\.staamp\.
select dadsada from a;

query error DataFusion error: Schema error: No field named ammp\. Did you mean 'a\.amp'\?\.
select ammp from a;

statement ok
drop table a;
