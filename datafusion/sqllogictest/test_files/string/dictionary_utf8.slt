# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

include ./init_data.slt.part

# --------------------------------------
# Setup test tables with different physical string types
# and repeat tests in `string_query.slt.part`
# --------------------------------------
statement ok
create table test_basic_operator as
select
    arrow_cast(column1, 'Dictionary(Int32, Utf8)') as ascii_1,
    arrow_cast(column2, 'Dictionary(Int32, Utf8)') as ascii_2,
    arrow_cast(column3, 'Dictionary(Int32, Utf8)') as unicode_1,
    arrow_cast(column4, 'Dictionary(Int32, Utf8)') as unicode_2
from test_source;

statement ok
create table test_substr as
select arrow_cast(col1, 'Dictionary(Int32, Utf8)') as c1 from test_substr_base;

statement ok
create table test_datetime as
select
  arrow_cast(column1, 'Dictionary(Int32, Utf8)') as ts,
  arrow_cast(column2, 'Dictionary(Int32, Utf8)') as d,
  arrow_cast(column3, 'Dictionary(Int32, Utf8)') as t
from test_datetime_base;


statement ok
drop table test_source

query T
SELECT arrow_cast('', 'Dictionary(Int32, Utf8)');
----
(empty)


#
# common test for string-like functions and operators
#
include ./string_query.slt.part

#
# Clean up
#
statement ok
drop table test_basic_operator;

statement ok
drop table test_substr_base;

statement ok
drop table test_datetime_base;
