# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at

#   http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# Portions of this file are derived from DuckDB and are licensed
# under the MIT License (see below).

# Copyright 2018-2025 Stichting DuckDB Foundation

# Permission is hereby granted, free of charge, to any person
# obtaining a copy of this software and associated documentation
# files (the "Software"), to deal in the Software without restriction,
# including without limitation the rights to use, copy, modify, merge,
# publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so,
# subject to the following conditions:

# The above copyright notice and this permission notice shall be included
# in all copies or substantial portions of the Software.

# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
# THE SOFTWARE.

statement ok
CREATE TABLE t1 (x INT, y INT);

statement ok
INSERT INTO t1 VALUES (3, 3), (3, 3), (1, 1);

statement ok
CREATE TABLE t2 (y INT, z INT);

statement ok
INSERT INTO t2 VALUES (2, 2), (4, 4);


# Test binding
query I
SELECT t1.x FROM t1 UNION BY NAME SELECT x FROM t1 ORDER BY x;
----
1
3

query I
SELECT t1.x FROM t1 UNION ALL BY NAME SELECT x FROM t1 ORDER BY x;
----
1
1
3
3
3
3

query I
SELECT x FROM t1 UNION BY NAME SELECT x FROM t1 ORDER BY x;
----
1
3

query I
SELECT x FROM t1 UNION ALL BY NAME SELECT x FROM t1 ORDER BY x;
----
1
1
3
3
3
3

query II
(SELECT x FROM t1 UNION ALL SELECT x FROM t1) UNION BY NAME SELECT 5 ORDER BY x;
----
1 NULL
3 NULL
NULL 5

query II
(SELECT x FROM t1 UNION ALL SELECT x FROM t1) UNION ALL BY NAME SELECT 5 ORDER BY x;
----
1 NULL
1 NULL
3 NULL
3 NULL
3 NULL
3 NULL
NULL 5

query II
(SELECT x FROM t1 UNION ALL SELECT y FROM t1) UNION BY NAME SELECT 5 ORDER BY x;
----
1 NULL
3 NULL
NULL 5

query II
(SELECT x FROM t1 UNION ALL SELECT y FROM t1) UNION ALL BY NAME SELECT 5 ORDER BY x;
----
1 NULL
1 NULL
3 NULL
3 NULL
3 NULL
3 NULL
NULL 5


# Ambiguous name

statement error DataFusion error: Schema error: No field named x. Valid fields are a, b.
SELECT x AS a FROM t1 UNION BY NAME SELECT x AS b FROM t1 ORDER BY x;

query II
(SELECT y FROM t1 UNION ALL SELECT x FROM t1) UNION BY NAME (SELECT z FROM t2 UNION ALL SELECT y FROM t2) ORDER BY y, z;
----
1 NULL
3 NULL
NULL 2
NULL 4

query II
(SELECT y FROM t1 UNION ALL SELECT x FROM t1) UNION ALL BY NAME (SELECT z FROM t2 UNION ALL SELECT y FROM t2) ORDER BY y, z;
----
1 NULL
1 NULL
3 NULL
3 NULL
3 NULL
3 NULL
NULL 2
NULL 2
NULL 4
NULL 4

# Limit

query III
SELECT 1 UNION BY NAME SELECT * FROM unnest(range(2, 100)) UNION BY NAME SELECT 999 ORDER BY 3, 1, 2 LIMIT 5;
----
NULL NULL 999
1 NULL NULL
NULL 2 NULL
NULL 3 NULL
NULL 4 NULL

query III
SELECT 1 UNION ALL BY NAME SELECT * FROM unnest(range(2, 100)) UNION ALL BY NAME SELECT 999 ORDER BY 3, 1 LIMIT 5;
----
NULL NULL 999
1 NULL NULL
NULL 2 NULL
NULL 3 NULL
NULL 4 NULL

# Order by

query III
SELECT x, y FROM t1 UNION BY NAME SELECT y, z FROM t2 ORDER BY y;
----
1 1 NULL
NULL 2 2
3 3 NULL
NULL 4 4

query III
SELECT x, y FROM t1 UNION ALL BY NAME SELECT y, z FROM t2 ORDER BY y;
----
1 1 NULL
NULL 2 2
3 3 NULL
3 3 NULL
NULL 4 4

query III
SELECT x, y FROM t1 UNION BY NAME SELECT y, z FROM t2 ORDER BY 3, 1;
----
NULL 2 2
NULL 4 4
1 1 NULL
3 3 NULL

query III
SELECT x, y FROM t1 UNION ALL BY NAME SELECT y, z FROM t2 ORDER BY 3, 1;
----
NULL 2 2
NULL 4 4
1 1 NULL
3 3 NULL
3 3 NULL

statement error
SELECT x, y FROM t1 UNION BY NAME SELECT y, z FROM t2 ORDER BY 4;
----
DataFusion error: Error during planning: Order by column out of bounds, specified: 4, max: 3


statement error
SELECT x, y FROM t1 UNION ALL BY NAME SELECT y, z FROM t2 ORDER BY 4;
----
DataFusion error: Error during planning: Order by column out of bounds, specified: 4, max: 3


# Multi set operations

query IIII rowsort
(SELECT 1 UNION BY NAME SELECT x, y FROM t1) UNION BY NAME SELECT y, z FROM t2;
----
1 NULL NULL NULL
NULL 1 1 NULL
NULL 3 3 NULL
NULL NULL 2 2
NULL NULL 4 4

query IIII rowsort
(SELECT 1 UNION ALL BY NAME SELECT x, y FROM t1) UNION ALL BY NAME SELECT y, z FROM t2;
----
1 NULL NULL NULL
NULL 1 1 NULL
NULL 3 3 NULL
NULL 3 3 NULL
NULL NULL 2 2
NULL NULL 4 4

query III
SELECT x, y FROM t1 UNION BY NAME (SELECT y, z FROM t2 INTERSECT SELECT 2, 2 as two FROM t1 ORDER BY 1) ORDER BY 1;
----
1 1 NULL
3 3 NULL
NULL 2 2

query III
SELECT x, y FROM t1 UNION ALL BY NAME (SELECT y, z FROM t2 INTERSECT SELECT 2, 2 as two FROM t1 ORDER BY 1) ORDER BY 1;
----
1 1 NULL
3 3 NULL
3 3 NULL
NULL 2 2

query III
(SELECT x, y FROM t1 UNION BY NAME SELECT y, z FROM t2 ORDER BY 1) EXCEPT SELECT NULL, 2, 2 as two FROM t1 ORDER BY 1;
----
1 1 NULL
3 3 NULL
NULL 4 4

# Alias in select list

query II
SELECT x as a FROM t1 UNION BY NAME SELECT x FROM t1 ORDER BY 1, 2;
----
1 NULL
3 NULL
NULL 1
NULL 3

query II
SELECT x as a FROM t1 UNION ALL BY NAME SELECT x FROM t1 ORDER BY 1, 2;
----
1 NULL
3 NULL
3 NULL
NULL 1
NULL 3
NULL 3

# Different types

query T rowsort
SELECT '0' as c UNION ALL BY NAME SELECT 0 as c;
----
0
0

# Regression tests for https://github.com/apache/datafusion/issues/15236
# Ensure that the correct output is produced even if the width of an input node's
# schema is the same as the resulting schema width after the union is applied.

statement ok
create table t3 (x varchar(255), y varchar(255), z varchar(255));

statement ok
create table t4 (x varchar(255), y varchar(255), z varchar(255));

statement ok
insert into t3 values ('a', 'b', 'c');

statement ok
insert into t4 values ('a', 'b', 'c');

query TTTT rowsort
select t3.x, t3.y, t3.z from t3 union by name select t3.z, t3.y, t3.x, 'd' as zz from t3;
----
a b c NULL
a b c d

query TTTT rowsort
select t3.x, t3.y, t3.z from t3 union by name select t4.z, t4.y, t4.x, 'd' as zz from t4;
----
a b c NULL
a b c d

query TTT rowsort
select x, y, z from t3 union all by name select z, y, x from t3;
----
a b c
a b c

query TTT rowsort
select x, y, z from t3 union all by name select z, y, x from t4;
----
a b c
a b c

query TTT
select x, y, z from t3 union all by name select z, y, x from t4 order by x;
----
a b c
a b c


# FIXME: The following should pass without error, but currently it is failing
# due to differing record batch schemas when the SLT runner collects results.
# This is due to the following issue: https://github.com/apache/datafusion/issues/15394#issue-2943811768
#
# More context can be found here: https://github.com/apache/datafusion/pull/15242#issuecomment-2746563234
query error
select x, y, z from t3 union all by name select z, y, x, 'd' as zz from t3;
----
DataFusion error: Internal error: Schema mismatch. Previously had
Schema {
    fields: [
        Field {
            name: "x",
            data_type: Utf8,
            nullable: true,
            dict_id: 0,
            dict_is_ordered: false,
            metadata: {},
        },
        Field {
            name: "y",
            data_type: Utf8,
            nullable: true,
            dict_id: 0,
            dict_is_ordered: false,
            metadata: {},
        },
        Field {
            name: "z",
            data_type: Utf8,
            nullable: true,
            dict_id: 0,
            dict_is_ordered: false,
            metadata: {},
        },
        Field {
            name: "zz",
            data_type: Utf8,
            nullable: false,
            dict_id: 0,
            dict_is_ordered: false,
            metadata: {},
        },
    ],
    metadata: {},
}

Got:
Schema {
    fields: [
        Field {
            name: "x",
            data_type: Utf8,
            nullable: true,
            dict_id: 0,
            dict_is_ordered: false,
            metadata: {},
        },
        Field {
            name: "y",
            data_type: Utf8,
            nullable: true,
            dict_id: 0,
            dict_is_ordered: false,
            metadata: {},
        },
        Field {
            name: "z",
            data_type: Utf8,
            nullable: true,
            dict_id: 0,
            dict_is_ordered: false,
            metadata: {},
        },
        Field {
            name: "zz",
            data_type: Utf8,
            nullable: true,
            dict_id: 0,
            dict_is_ordered: false,
            metadata: {},
        },
    ],
    metadata: {},
}.
This was likely caused by a bug in DataFusion's code and we would welcome that you file an bug report in our issue tracker
