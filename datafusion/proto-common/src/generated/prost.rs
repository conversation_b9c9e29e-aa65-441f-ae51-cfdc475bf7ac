// This file is @generated by prost-build.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ColumnRelation {
    #[prost(string, tag = "1")]
    pub relation: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Column {
    #[prost(string, tag = "1")]
    pub name: ::prost::alloc::string::String,
    #[prost(message, optional, tag = "2")]
    pub relation: ::core::option::Option<ColumnRelation>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DfField {
    #[prost(message, optional, tag = "1")]
    pub field: ::core::option::Option<Field>,
    #[prost(message, optional, tag = "2")]
    pub qualifier: ::core::option::Option<ColumnRelation>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DfSchema {
    #[prost(message, repeated, tag = "1")]
    pub columns: ::prost::alloc::vec::Vec<DfField>,
    #[prost(map = "string, string", tag = "2")]
    pub metadata: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        ::prost::alloc::string::String,
    >,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct CsvFormat {
    #[prost(message, optional, tag = "5")]
    pub options: ::core::option::Option<CsvOptions>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ParquetFormat {
    #[prost(message, optional, tag = "2")]
    pub options: ::core::option::Option<TableParquetOptions>,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct AvroFormat {}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct NdJsonFormat {
    #[prost(message, optional, tag = "1")]
    pub options: ::core::option::Option<JsonOptions>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PrimaryKeyConstraint {
    #[prost(uint64, repeated, tag = "1")]
    pub indices: ::prost::alloc::vec::Vec<u64>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UniqueConstraint {
    #[prost(uint64, repeated, tag = "1")]
    pub indices: ::prost::alloc::vec::Vec<u64>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Constraint {
    #[prost(oneof = "constraint::ConstraintMode", tags = "1, 2")]
    pub constraint_mode: ::core::option::Option<constraint::ConstraintMode>,
}
/// Nested message and enum types in `Constraint`.
pub mod constraint {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum ConstraintMode {
        #[prost(message, tag = "1")]
        PrimaryKey(super::PrimaryKeyConstraint),
        #[prost(message, tag = "2")]
        Unique(super::UniqueConstraint),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Constraints {
    #[prost(message, repeated, tag = "1")]
    pub constraints: ::prost::alloc::vec::Vec<Constraint>,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct AvroOptions {}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct ArrowOptions {}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Schema {
    #[prost(message, repeated, tag = "1")]
    pub columns: ::prost::alloc::vec::Vec<Field>,
    #[prost(map = "string, string", tag = "2")]
    pub metadata: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        ::prost::alloc::string::String,
    >,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Field {
    /// name of the field
    #[prost(string, tag = "1")]
    pub name: ::prost::alloc::string::String,
    #[prost(message, optional, boxed, tag = "2")]
    pub arrow_type: ::core::option::Option<::prost::alloc::boxed::Box<ArrowType>>,
    #[prost(bool, tag = "3")]
    pub nullable: bool,
    /// for complex data types like structs, unions
    #[prost(message, repeated, tag = "4")]
    pub children: ::prost::alloc::vec::Vec<Field>,
    #[prost(map = "string, string", tag = "5")]
    pub metadata: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        ::prost::alloc::string::String,
    >,
    #[prost(bool, tag = "6")]
    pub dict_ordered: bool,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Timestamp {
    #[prost(enumeration = "TimeUnit", tag = "1")]
    pub time_unit: i32,
    #[prost(string, tag = "2")]
    pub timezone: ::prost::alloc::string::String,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct Decimal {
    #[prost(uint32, tag = "3")]
    pub precision: u32,
    #[prost(int32, tag = "4")]
    pub scale: i32,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct Decimal256Type {
    #[prost(uint32, tag = "3")]
    pub precision: u32,
    #[prost(int32, tag = "4")]
    pub scale: i32,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct List {
    #[prost(message, optional, boxed, tag = "1")]
    pub field_type: ::core::option::Option<::prost::alloc::boxed::Box<Field>>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct FixedSizeList {
    #[prost(message, optional, boxed, tag = "1")]
    pub field_type: ::core::option::Option<::prost::alloc::boxed::Box<Field>>,
    #[prost(int32, tag = "2")]
    pub list_size: i32,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Dictionary {
    #[prost(message, optional, boxed, tag = "1")]
    pub key: ::core::option::Option<::prost::alloc::boxed::Box<ArrowType>>,
    #[prost(message, optional, boxed, tag = "2")]
    pub value: ::core::option::Option<::prost::alloc::boxed::Box<ArrowType>>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Struct {
    #[prost(message, repeated, tag = "1")]
    pub sub_field_types: ::prost::alloc::vec::Vec<Field>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Map {
    #[prost(message, optional, boxed, tag = "1")]
    pub field_type: ::core::option::Option<::prost::alloc::boxed::Box<Field>>,
    #[prost(bool, tag = "2")]
    pub keys_sorted: bool,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Union {
    #[prost(message, repeated, tag = "1")]
    pub union_types: ::prost::alloc::vec::Vec<Field>,
    #[prost(enumeration = "UnionMode", tag = "2")]
    pub union_mode: i32,
    #[prost(int32, repeated, tag = "3")]
    pub type_ids: ::prost::alloc::vec::Vec<i32>,
}
/// Used for List/FixedSizeList/LargeList/Struct/Map
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ScalarNestedValue {
    #[prost(bytes = "vec", tag = "1")]
    pub ipc_message: ::prost::alloc::vec::Vec<u8>,
    #[prost(bytes = "vec", tag = "2")]
    pub arrow_data: ::prost::alloc::vec::Vec<u8>,
    #[prost(message, optional, tag = "3")]
    pub schema: ::core::option::Option<Schema>,
    #[prost(message, repeated, tag = "4")]
    pub dictionaries: ::prost::alloc::vec::Vec<scalar_nested_value::Dictionary>,
}
/// Nested message and enum types in `ScalarNestedValue`.
pub mod scalar_nested_value {
    #[derive(Clone, PartialEq, ::prost::Message)]
    pub struct Dictionary {
        #[prost(bytes = "vec", tag = "1")]
        pub ipc_message: ::prost::alloc::vec::Vec<u8>,
        #[prost(bytes = "vec", tag = "2")]
        pub arrow_data: ::prost::alloc::vec::Vec<u8>,
    }
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct ScalarTime32Value {
    #[prost(oneof = "scalar_time32_value::Value", tags = "1, 2")]
    pub value: ::core::option::Option<scalar_time32_value::Value>,
}
/// Nested message and enum types in `ScalarTime32Value`.
pub mod scalar_time32_value {
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum Value {
        #[prost(int32, tag = "1")]
        Time32SecondValue(i32),
        #[prost(int32, tag = "2")]
        Time32MillisecondValue(i32),
    }
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct ScalarTime64Value {
    #[prost(oneof = "scalar_time64_value::Value", tags = "1, 2")]
    pub value: ::core::option::Option<scalar_time64_value::Value>,
}
/// Nested message and enum types in `ScalarTime64Value`.
pub mod scalar_time64_value {
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum Value {
        #[prost(int64, tag = "1")]
        Time64MicrosecondValue(i64),
        #[prost(int64, tag = "2")]
        Time64NanosecondValue(i64),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ScalarTimestampValue {
    #[prost(string, tag = "5")]
    pub timezone: ::prost::alloc::string::String,
    #[prost(oneof = "scalar_timestamp_value::Value", tags = "1, 2, 3, 4")]
    pub value: ::core::option::Option<scalar_timestamp_value::Value>,
}
/// Nested message and enum types in `ScalarTimestampValue`.
pub mod scalar_timestamp_value {
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum Value {
        #[prost(int64, tag = "1")]
        TimeMicrosecondValue(i64),
        #[prost(int64, tag = "2")]
        TimeNanosecondValue(i64),
        #[prost(int64, tag = "3")]
        TimeSecondValue(i64),
        #[prost(int64, tag = "4")]
        TimeMillisecondValue(i64),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ScalarDictionaryValue {
    #[prost(message, optional, tag = "1")]
    pub index_type: ::core::option::Option<ArrowType>,
    #[prost(message, optional, boxed, tag = "2")]
    pub value: ::core::option::Option<::prost::alloc::boxed::Box<ScalarValue>>,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct IntervalDayTimeValue {
    #[prost(int32, tag = "1")]
    pub days: i32,
    #[prost(int32, tag = "2")]
    pub milliseconds: i32,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct IntervalMonthDayNanoValue {
    #[prost(int32, tag = "1")]
    pub months: i32,
    #[prost(int32, tag = "2")]
    pub days: i32,
    #[prost(int64, tag = "3")]
    pub nanos: i64,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UnionField {
    #[prost(int32, tag = "1")]
    pub field_id: i32,
    #[prost(message, optional, tag = "2")]
    pub field: ::core::option::Option<Field>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UnionValue {
    /// Note that a null union value must have one or more fields, so we
    /// encode a null UnionValue as one with value_id == 128
    #[prost(int32, tag = "1")]
    pub value_id: i32,
    #[prost(message, optional, boxed, tag = "2")]
    pub value: ::core::option::Option<::prost::alloc::boxed::Box<ScalarValue>>,
    #[prost(message, repeated, tag = "3")]
    pub fields: ::prost::alloc::vec::Vec<UnionField>,
    #[prost(enumeration = "UnionMode", tag = "4")]
    pub mode: i32,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ScalarFixedSizeBinary {
    #[prost(bytes = "vec", tag = "1")]
    pub values: ::prost::alloc::vec::Vec<u8>,
    #[prost(int32, tag = "2")]
    pub length: i32,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ScalarValue {
    #[prost(
        oneof = "scalar_value::Value",
        tags = "33, 1, 2, 3, 23, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 32, 41, 20, 39, 21, 24, 35, 36, 37, 38, 26, 27, 28, 29, 22, 30, 25, 31, 34, 42"
    )]
    pub value: ::core::option::Option<scalar_value::Value>,
}
/// Nested message and enum types in `ScalarValue`.
pub mod scalar_value {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Value {
        /// was PrimitiveScalarType null_value = 19;
        /// Null value of any type
        #[prost(message, tag = "33")]
        NullValue(super::ArrowType),
        #[prost(bool, tag = "1")]
        BoolValue(bool),
        #[prost(string, tag = "2")]
        Utf8Value(::prost::alloc::string::String),
        #[prost(string, tag = "3")]
        LargeUtf8Value(::prost::alloc::string::String),
        #[prost(string, tag = "23")]
        Utf8ViewValue(::prost::alloc::string::String),
        #[prost(int32, tag = "4")]
        Int8Value(i32),
        #[prost(int32, tag = "5")]
        Int16Value(i32),
        #[prost(int32, tag = "6")]
        Int32Value(i32),
        #[prost(int64, tag = "7")]
        Int64Value(i64),
        #[prost(uint32, tag = "8")]
        Uint8Value(u32),
        #[prost(uint32, tag = "9")]
        Uint16Value(u32),
        #[prost(uint32, tag = "10")]
        Uint32Value(u32),
        #[prost(uint64, tag = "11")]
        Uint64Value(u64),
        #[prost(float, tag = "12")]
        Float32Value(f32),
        #[prost(double, tag = "13")]
        Float64Value(f64),
        /// Literal Date32 value always has a unit of day
        #[prost(int32, tag = "14")]
        Date32Value(i32),
        #[prost(message, tag = "15")]
        Time32Value(super::ScalarTime32Value),
        #[prost(message, tag = "16")]
        LargeListValue(super::ScalarNestedValue),
        #[prost(message, tag = "17")]
        ListValue(super::ScalarNestedValue),
        #[prost(message, tag = "18")]
        FixedSizeListValue(super::ScalarNestedValue),
        #[prost(message, tag = "32")]
        StructValue(super::ScalarNestedValue),
        #[prost(message, tag = "41")]
        MapValue(super::ScalarNestedValue),
        #[prost(message, tag = "20")]
        Decimal128Value(super::Decimal128),
        #[prost(message, tag = "39")]
        Decimal256Value(super::Decimal256),
        #[prost(int64, tag = "21")]
        Date64Value(i64),
        #[prost(int32, tag = "24")]
        IntervalYearmonthValue(i32),
        #[prost(int64, tag = "35")]
        DurationSecondValue(i64),
        #[prost(int64, tag = "36")]
        DurationMillisecondValue(i64),
        #[prost(int64, tag = "37")]
        DurationMicrosecondValue(i64),
        #[prost(int64, tag = "38")]
        DurationNanosecondValue(i64),
        #[prost(message, tag = "26")]
        TimestampValue(super::ScalarTimestampValue),
        #[prost(message, tag = "27")]
        DictionaryValue(::prost::alloc::boxed::Box<super::ScalarDictionaryValue>),
        #[prost(bytes, tag = "28")]
        BinaryValue(::prost::alloc::vec::Vec<u8>),
        #[prost(bytes, tag = "29")]
        LargeBinaryValue(::prost::alloc::vec::Vec<u8>),
        #[prost(bytes, tag = "22")]
        BinaryViewValue(::prost::alloc::vec::Vec<u8>),
        #[prost(message, tag = "30")]
        Time64Value(super::ScalarTime64Value),
        #[prost(message, tag = "25")]
        IntervalDaytimeValue(super::IntervalDayTimeValue),
        #[prost(message, tag = "31")]
        IntervalMonthDayNano(super::IntervalMonthDayNanoValue),
        #[prost(message, tag = "34")]
        FixedSizeBinaryValue(super::ScalarFixedSizeBinary),
        #[prost(message, tag = "42")]
        UnionValue(::prost::alloc::boxed::Box<super::UnionValue>),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Decimal128 {
    #[prost(bytes = "vec", tag = "1")]
    pub value: ::prost::alloc::vec::Vec<u8>,
    #[prost(int64, tag = "2")]
    pub p: i64,
    #[prost(int64, tag = "3")]
    pub s: i64,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Decimal256 {
    #[prost(bytes = "vec", tag = "1")]
    pub value: ::prost::alloc::vec::Vec<u8>,
    #[prost(int64, tag = "2")]
    pub p: i64,
    #[prost(int64, tag = "3")]
    pub s: i64,
}
/// Serialized data type
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ArrowType {
    #[prost(
        oneof = "arrow_type::ArrowTypeEnum",
        tags = "1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 35, 32, 15, 34, 16, 31, 17, 18, 19, 20, 21, 22, 23, 24, 36, 25, 26, 27, 28, 29, 30, 33"
    )]
    pub arrow_type_enum: ::core::option::Option<arrow_type::ArrowTypeEnum>,
}
/// Nested message and enum types in `ArrowType`.
pub mod arrow_type {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum ArrowTypeEnum {
        /// arrow::Type::NA
        #[prost(message, tag = "1")]
        None(super::EmptyMessage),
        /// arrow::Type::BOOL
        #[prost(message, tag = "2")]
        Bool(super::EmptyMessage),
        /// arrow::Type::UINT8
        #[prost(message, tag = "3")]
        Uint8(super::EmptyMessage),
        /// arrow::Type::INT8
        #[prost(message, tag = "4")]
        Int8(super::EmptyMessage),
        /// represents arrow::Type fields in src/arrow/type.h
        #[prost(message, tag = "5")]
        Uint16(super::EmptyMessage),
        #[prost(message, tag = "6")]
        Int16(super::EmptyMessage),
        #[prost(message, tag = "7")]
        Uint32(super::EmptyMessage),
        #[prost(message, tag = "8")]
        Int32(super::EmptyMessage),
        #[prost(message, tag = "9")]
        Uint64(super::EmptyMessage),
        #[prost(message, tag = "10")]
        Int64(super::EmptyMessage),
        #[prost(message, tag = "11")]
        Float16(super::EmptyMessage),
        #[prost(message, tag = "12")]
        Float32(super::EmptyMessage),
        #[prost(message, tag = "13")]
        Float64(super::EmptyMessage),
        #[prost(message, tag = "14")]
        Utf8(super::EmptyMessage),
        #[prost(message, tag = "35")]
        Utf8View(super::EmptyMessage),
        #[prost(message, tag = "32")]
        LargeUtf8(super::EmptyMessage),
        #[prost(message, tag = "15")]
        Binary(super::EmptyMessage),
        #[prost(message, tag = "34")]
        BinaryView(super::EmptyMessage),
        #[prost(int32, tag = "16")]
        FixedSizeBinary(i32),
        #[prost(message, tag = "31")]
        LargeBinary(super::EmptyMessage),
        #[prost(message, tag = "17")]
        Date32(super::EmptyMessage),
        #[prost(message, tag = "18")]
        Date64(super::EmptyMessage),
        #[prost(enumeration = "super::TimeUnit", tag = "19")]
        Duration(i32),
        #[prost(message, tag = "20")]
        Timestamp(super::Timestamp),
        #[prost(enumeration = "super::TimeUnit", tag = "21")]
        Time32(i32),
        #[prost(enumeration = "super::TimeUnit", tag = "22")]
        Time64(i32),
        #[prost(enumeration = "super::IntervalUnit", tag = "23")]
        Interval(i32),
        #[prost(message, tag = "24")]
        Decimal(super::Decimal),
        #[prost(message, tag = "36")]
        Decimal256(super::Decimal256Type),
        #[prost(message, tag = "25")]
        List(::prost::alloc::boxed::Box<super::List>),
        #[prost(message, tag = "26")]
        LargeList(::prost::alloc::boxed::Box<super::List>),
        #[prost(message, tag = "27")]
        FixedSizeList(::prost::alloc::boxed::Box<super::FixedSizeList>),
        #[prost(message, tag = "28")]
        Struct(super::Struct),
        #[prost(message, tag = "29")]
        Union(super::Union),
        #[prost(message, tag = "30")]
        Dictionary(::prost::alloc::boxed::Box<super::Dictionary>),
        #[prost(message, tag = "33")]
        Map(::prost::alloc::boxed::Box<super::Map>),
    }
}
/// Useful for representing an empty enum variant in rust
/// E.G. enum example{One, Two(i32)}
/// maps to
/// message example{
///     oneof{
///         EmptyMessage One = 1;
///         i32 Two = 2;
///    }
/// }
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct EmptyMessage {}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct JsonWriterOptions {
    #[prost(enumeration = "CompressionTypeVariant", tag = "1")]
    pub compression: i32,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct CsvWriterOptions {
    /// Compression type
    #[prost(enumeration = "CompressionTypeVariant", tag = "1")]
    pub compression: i32,
    /// Optional column delimiter. Defaults to `b','`
    #[prost(string, tag = "2")]
    pub delimiter: ::prost::alloc::string::String,
    /// Whether to write column names as file headers. Defaults to `true`
    #[prost(bool, tag = "3")]
    pub has_header: bool,
    /// Optional date format for date arrays
    #[prost(string, tag = "4")]
    pub date_format: ::prost::alloc::string::String,
    /// Optional datetime format for datetime arrays
    #[prost(string, tag = "5")]
    pub datetime_format: ::prost::alloc::string::String,
    /// Optional timestamp format for timestamp arrays
    #[prost(string, tag = "6")]
    pub timestamp_format: ::prost::alloc::string::String,
    /// Optional time format for time arrays
    #[prost(string, tag = "7")]
    pub time_format: ::prost::alloc::string::String,
    /// Optional value to represent null
    #[prost(string, tag = "8")]
    pub null_value: ::prost::alloc::string::String,
    /// Optional quote. Defaults to `b'"'`
    #[prost(string, tag = "9")]
    pub quote: ::prost::alloc::string::String,
    /// Optional escape. Defaults to `'\\'`
    #[prost(string, tag = "10")]
    pub escape: ::prost::alloc::string::String,
    /// Optional flag whether to double quotes, instead of escaping. Defaults to `true`
    #[prost(bool, tag = "11")]
    pub double_quote: bool,
}
/// Options controlling CSV format
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct CsvOptions {
    /// Indicates if the CSV has a header row
    #[prost(bytes = "vec", tag = "1")]
    pub has_header: ::prost::alloc::vec::Vec<u8>,
    /// Delimiter character as a byte
    #[prost(bytes = "vec", tag = "2")]
    pub delimiter: ::prost::alloc::vec::Vec<u8>,
    /// Quote character as a byte
    #[prost(bytes = "vec", tag = "3")]
    pub quote: ::prost::alloc::vec::Vec<u8>,
    /// Optional escape character as a byte
    #[prost(bytes = "vec", tag = "4")]
    pub escape: ::prost::alloc::vec::Vec<u8>,
    /// Compression type
    #[prost(enumeration = "CompressionTypeVariant", tag = "5")]
    pub compression: i32,
    /// Optional max records for schema inference
    #[prost(uint64, optional, tag = "6")]
    pub schema_infer_max_rec: ::core::option::Option<u64>,
    /// Optional date format
    #[prost(string, tag = "7")]
    pub date_format: ::prost::alloc::string::String,
    /// Optional datetime format
    #[prost(string, tag = "8")]
    pub datetime_format: ::prost::alloc::string::String,
    /// Optional timestamp format
    #[prost(string, tag = "9")]
    pub timestamp_format: ::prost::alloc::string::String,
    /// Optional timestamp with timezone format
    #[prost(string, tag = "10")]
    pub timestamp_tz_format: ::prost::alloc::string::String,
    /// Optional time format
    #[prost(string, tag = "11")]
    pub time_format: ::prost::alloc::string::String,
    /// Optional representation of null value
    #[prost(string, tag = "12")]
    pub null_value: ::prost::alloc::string::String,
    /// Optional representation of null loading regex
    #[prost(string, tag = "13")]
    pub null_regex: ::prost::alloc::string::String,
    /// Optional comment character as a byte
    #[prost(bytes = "vec", tag = "14")]
    pub comment: ::prost::alloc::vec::Vec<u8>,
    /// Indicates if quotes are doubled
    #[prost(bytes = "vec", tag = "15")]
    pub double_quote: ::prost::alloc::vec::Vec<u8>,
    /// Indicates if newlines are supported in values
    #[prost(bytes = "vec", tag = "16")]
    pub newlines_in_values: ::prost::alloc::vec::Vec<u8>,
    /// Optional terminator character as a byte
    #[prost(bytes = "vec", tag = "17")]
    pub terminator: ::prost::alloc::vec::Vec<u8>,
}
/// Options controlling CSV format
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct JsonOptions {
    /// Compression type
    #[prost(enumeration = "CompressionTypeVariant", tag = "1")]
    pub compression: i32,
    /// Optional max records for schema inference
    #[prost(uint64, optional, tag = "2")]
    pub schema_infer_max_rec: ::core::option::Option<u64>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TableParquetOptions {
    #[prost(message, optional, tag = "1")]
    pub global: ::core::option::Option<ParquetOptions>,
    #[prost(message, repeated, tag = "2")]
    pub column_specific_options: ::prost::alloc::vec::Vec<ParquetColumnSpecificOptions>,
    #[prost(map = "string, string", tag = "3")]
    pub key_value_metadata: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        ::prost::alloc::string::String,
    >,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ParquetColumnSpecificOptions {
    #[prost(string, tag = "1")]
    pub column_name: ::prost::alloc::string::String,
    #[prost(message, optional, tag = "2")]
    pub options: ::core::option::Option<ParquetColumnOptions>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ParquetColumnOptions {
    #[prost(oneof = "parquet_column_options::BloomFilterEnabledOpt", tags = "1")]
    pub bloom_filter_enabled_opt: ::core::option::Option<
        parquet_column_options::BloomFilterEnabledOpt,
    >,
    #[prost(oneof = "parquet_column_options::EncodingOpt", tags = "2")]
    pub encoding_opt: ::core::option::Option<parquet_column_options::EncodingOpt>,
    #[prost(oneof = "parquet_column_options::DictionaryEnabledOpt", tags = "3")]
    pub dictionary_enabled_opt: ::core::option::Option<
        parquet_column_options::DictionaryEnabledOpt,
    >,
    #[prost(oneof = "parquet_column_options::CompressionOpt", tags = "4")]
    pub compression_opt: ::core::option::Option<parquet_column_options::CompressionOpt>,
    #[prost(oneof = "parquet_column_options::StatisticsEnabledOpt", tags = "5")]
    pub statistics_enabled_opt: ::core::option::Option<
        parquet_column_options::StatisticsEnabledOpt,
    >,
    #[prost(oneof = "parquet_column_options::BloomFilterFppOpt", tags = "6")]
    pub bloom_filter_fpp_opt: ::core::option::Option<
        parquet_column_options::BloomFilterFppOpt,
    >,
    #[prost(oneof = "parquet_column_options::BloomFilterNdvOpt", tags = "7")]
    pub bloom_filter_ndv_opt: ::core::option::Option<
        parquet_column_options::BloomFilterNdvOpt,
    >,
    #[prost(oneof = "parquet_column_options::MaxStatisticsSizeOpt", tags = "8")]
    pub max_statistics_size_opt: ::core::option::Option<
        parquet_column_options::MaxStatisticsSizeOpt,
    >,
}
/// Nested message and enum types in `ParquetColumnOptions`.
pub mod parquet_column_options {
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum BloomFilterEnabledOpt {
        #[prost(bool, tag = "1")]
        BloomFilterEnabled(bool),
    }
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum EncodingOpt {
        #[prost(string, tag = "2")]
        Encoding(::prost::alloc::string::String),
    }
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum DictionaryEnabledOpt {
        #[prost(bool, tag = "3")]
        DictionaryEnabled(bool),
    }
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum CompressionOpt {
        #[prost(string, tag = "4")]
        Compression(::prost::alloc::string::String),
    }
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum StatisticsEnabledOpt {
        #[prost(string, tag = "5")]
        StatisticsEnabled(::prost::alloc::string::String),
    }
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum BloomFilterFppOpt {
        #[prost(double, tag = "6")]
        BloomFilterFpp(f64),
    }
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum BloomFilterNdvOpt {
        #[prost(uint64, tag = "7")]
        BloomFilterNdv(u64),
    }
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum MaxStatisticsSizeOpt {
        #[prost(uint32, tag = "8")]
        MaxStatisticsSize(u32),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ParquetOptions {
    /// Regular fields
    ///
    /// default = true
    #[prost(bool, tag = "1")]
    pub enable_page_index: bool,
    /// default = true
    #[prost(bool, tag = "2")]
    pub pruning: bool,
    /// default = true
    #[prost(bool, tag = "3")]
    pub skip_metadata: bool,
    /// default = false
    #[prost(bool, tag = "5")]
    pub pushdown_filters: bool,
    /// default = false
    #[prost(bool, tag = "6")]
    pub reorder_filters: bool,
    /// default = 1024 * 1024
    #[prost(uint64, tag = "7")]
    pub data_pagesize_limit: u64,
    /// default = 1024
    #[prost(uint64, tag = "8")]
    pub write_batch_size: u64,
    /// default = "1.0"
    #[prost(string, tag = "9")]
    pub writer_version: ::prost::alloc::string::String,
    /// bool bloom_filter_enabled = 20; // default = false
    ///
    /// default = true
    #[prost(bool, tag = "23")]
    pub allow_single_file_parallelism: bool,
    /// default = 1
    #[prost(uint64, tag = "24")]
    pub maximum_parallel_row_group_writers: u64,
    /// default = 2
    #[prost(uint64, tag = "25")]
    pub maximum_buffered_record_batches_per_stream: u64,
    /// default = true
    #[prost(bool, tag = "26")]
    pub bloom_filter_on_read: bool,
    /// default = false
    #[prost(bool, tag = "27")]
    pub bloom_filter_on_write: bool,
    /// default = false
    #[prost(bool, tag = "28")]
    pub schema_force_view_types: bool,
    /// default = false
    #[prost(bool, tag = "29")]
    pub binary_as_string: bool,
    /// default = false
    #[prost(bool, tag = "30")]
    pub skip_arrow_metadata: bool,
    #[prost(uint64, tag = "12")]
    pub dictionary_page_size_limit: u64,
    #[prost(uint64, tag = "18")]
    pub data_page_row_count_limit: u64,
    #[prost(uint64, tag = "15")]
    pub max_row_group_size: u64,
    #[prost(string, tag = "16")]
    pub created_by: ::prost::alloc::string::String,
    #[prost(oneof = "parquet_options::MetadataSizeHintOpt", tags = "4")]
    pub metadata_size_hint_opt: ::core::option::Option<
        parquet_options::MetadataSizeHintOpt,
    >,
    #[prost(oneof = "parquet_options::CompressionOpt", tags = "10")]
    pub compression_opt: ::core::option::Option<parquet_options::CompressionOpt>,
    #[prost(oneof = "parquet_options::DictionaryEnabledOpt", tags = "11")]
    pub dictionary_enabled_opt: ::core::option::Option<
        parquet_options::DictionaryEnabledOpt,
    >,
    #[prost(oneof = "parquet_options::StatisticsEnabledOpt", tags = "13")]
    pub statistics_enabled_opt: ::core::option::Option<
        parquet_options::StatisticsEnabledOpt,
    >,
    #[prost(oneof = "parquet_options::MaxStatisticsSizeOpt", tags = "14")]
    pub max_statistics_size_opt: ::core::option::Option<
        parquet_options::MaxStatisticsSizeOpt,
    >,
    #[prost(oneof = "parquet_options::ColumnIndexTruncateLengthOpt", tags = "17")]
    pub column_index_truncate_length_opt: ::core::option::Option<
        parquet_options::ColumnIndexTruncateLengthOpt,
    >,
    #[prost(oneof = "parquet_options::StatisticsTruncateLengthOpt", tags = "31")]
    pub statistics_truncate_length_opt: ::core::option::Option<
        parquet_options::StatisticsTruncateLengthOpt,
    >,
    #[prost(oneof = "parquet_options::EncodingOpt", tags = "19")]
    pub encoding_opt: ::core::option::Option<parquet_options::EncodingOpt>,
    #[prost(oneof = "parquet_options::BloomFilterFppOpt", tags = "21")]
    pub bloom_filter_fpp_opt: ::core::option::Option<parquet_options::BloomFilterFppOpt>,
    #[prost(oneof = "parquet_options::BloomFilterNdvOpt", tags = "22")]
    pub bloom_filter_ndv_opt: ::core::option::Option<parquet_options::BloomFilterNdvOpt>,
    #[prost(oneof = "parquet_options::CoerceInt96Opt", tags = "32")]
    pub coerce_int96_opt: ::core::option::Option<parquet_options::CoerceInt96Opt>,
}
/// Nested message and enum types in `ParquetOptions`.
pub mod parquet_options {
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum MetadataSizeHintOpt {
        #[prost(uint64, tag = "4")]
        MetadataSizeHint(u64),
    }
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum CompressionOpt {
        #[prost(string, tag = "10")]
        Compression(::prost::alloc::string::String),
    }
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum DictionaryEnabledOpt {
        #[prost(bool, tag = "11")]
        DictionaryEnabled(bool),
    }
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum StatisticsEnabledOpt {
        #[prost(string, tag = "13")]
        StatisticsEnabled(::prost::alloc::string::String),
    }
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum MaxStatisticsSizeOpt {
        #[prost(uint64, tag = "14")]
        MaxStatisticsSize(u64),
    }
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum ColumnIndexTruncateLengthOpt {
        #[prost(uint64, tag = "17")]
        ColumnIndexTruncateLength(u64),
    }
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum StatisticsTruncateLengthOpt {
        #[prost(uint64, tag = "31")]
        StatisticsTruncateLength(u64),
    }
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum EncodingOpt {
        #[prost(string, tag = "19")]
        Encoding(::prost::alloc::string::String),
    }
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum BloomFilterFppOpt {
        #[prost(double, tag = "21")]
        BloomFilterFpp(f64),
    }
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum BloomFilterNdvOpt {
        #[prost(uint64, tag = "22")]
        BloomFilterNdv(u64),
    }
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum CoerceInt96Opt {
        #[prost(string, tag = "32")]
        CoerceInt96(::prost::alloc::string::String),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Precision {
    #[prost(enumeration = "PrecisionInfo", tag = "1")]
    pub precision_info: i32,
    #[prost(message, optional, tag = "2")]
    pub val: ::core::option::Option<ScalarValue>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Statistics {
    #[prost(message, optional, tag = "1")]
    pub num_rows: ::core::option::Option<Precision>,
    #[prost(message, optional, tag = "2")]
    pub total_byte_size: ::core::option::Option<Precision>,
    #[prost(message, repeated, tag = "3")]
    pub column_stats: ::prost::alloc::vec::Vec<ColumnStats>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ColumnStats {
    #[prost(message, optional, tag = "1")]
    pub min_value: ::core::option::Option<Precision>,
    #[prost(message, optional, tag = "2")]
    pub max_value: ::core::option::Option<Precision>,
    #[prost(message, optional, tag = "5")]
    pub sum_value: ::core::option::Option<Precision>,
    #[prost(message, optional, tag = "3")]
    pub null_count: ::core::option::Option<Precision>,
    #[prost(message, optional, tag = "4")]
    pub distinct_count: ::core::option::Option<Precision>,
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum JoinType {
    Inner = 0,
    Left = 1,
    Right = 2,
    Full = 3,
    Leftsemi = 4,
    Leftanti = 5,
    Rightsemi = 6,
    Rightanti = 7,
    Leftmark = 8,
}
impl JoinType {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::Inner => "INNER",
            Self::Left => "LEFT",
            Self::Right => "RIGHT",
            Self::Full => "FULL",
            Self::Leftsemi => "LEFTSEMI",
            Self::Leftanti => "LEFTANTI",
            Self::Rightsemi => "RIGHTSEMI",
            Self::Rightanti => "RIGHTANTI",
            Self::Leftmark => "LEFTMARK",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "INNER" => Some(Self::Inner),
            "LEFT" => Some(Self::Left),
            "RIGHT" => Some(Self::Right),
            "FULL" => Some(Self::Full),
            "LEFTSEMI" => Some(Self::Leftsemi),
            "LEFTANTI" => Some(Self::Leftanti),
            "RIGHTSEMI" => Some(Self::Rightsemi),
            "RIGHTANTI" => Some(Self::Rightanti),
            "LEFTMARK" => Some(Self::Leftmark),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum JoinConstraint {
    On = 0,
    Using = 1,
}
impl JoinConstraint {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::On => "ON",
            Self::Using => "USING",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "ON" => Some(Self::On),
            "USING" => Some(Self::Using),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum TimeUnit {
    Second = 0,
    Millisecond = 1,
    Microsecond = 2,
    Nanosecond = 3,
}
impl TimeUnit {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::Second => "Second",
            Self::Millisecond => "Millisecond",
            Self::Microsecond => "Microsecond",
            Self::Nanosecond => "Nanosecond",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "Second" => Some(Self::Second),
            "Millisecond" => Some(Self::Millisecond),
            "Microsecond" => Some(Self::Microsecond),
            "Nanosecond" => Some(Self::Nanosecond),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum IntervalUnit {
    YearMonth = 0,
    DayTime = 1,
    MonthDayNano = 2,
}
impl IntervalUnit {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::YearMonth => "YearMonth",
            Self::DayTime => "DayTime",
            Self::MonthDayNano => "MonthDayNano",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "YearMonth" => Some(Self::YearMonth),
            "DayTime" => Some(Self::DayTime),
            "MonthDayNano" => Some(Self::MonthDayNano),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum UnionMode {
    Sparse = 0,
    Dense = 1,
}
impl UnionMode {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::Sparse => "sparse",
            Self::Dense => "dense",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "sparse" => Some(Self::Sparse),
            "dense" => Some(Self::Dense),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum CompressionTypeVariant {
    Gzip = 0,
    Bzip2 = 1,
    Xz = 2,
    Zstd = 3,
    Uncompressed = 4,
}
impl CompressionTypeVariant {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::Gzip => "GZIP",
            Self::Bzip2 => "BZIP2",
            Self::Xz => "XZ",
            Self::Zstd => "ZSTD",
            Self::Uncompressed => "UNCOMPRESSED",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "GZIP" => Some(Self::Gzip),
            "BZIP2" => Some(Self::Bzip2),
            "XZ" => Some(Self::Xz),
            "ZSTD" => Some(Self::Zstd),
            "UNCOMPRESSED" => Some(Self::Uncompressed),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum JoinSide {
    LeftSide = 0,
    RightSide = 1,
    None = 2,
}
impl JoinSide {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::LeftSide => "LEFT_SIDE",
            Self::RightSide => "RIGHT_SIDE",
            Self::None => "NONE",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "LEFT_SIDE" => Some(Self::LeftSide),
            "RIGHT_SIDE" => Some(Self::RightSide),
            "NONE" => Some(Self::None),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum PrecisionInfo {
    Exact = 0,
    Inexact = 1,
    Absent = 2,
}
impl PrecisionInfo {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::Exact => "EXACT",
            Self::Inexact => "INEXACT",
            Self::Absent => "ABSENT",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "EXACT" => Some(Self::Exact),
            "INEXACT" => Some(Self::Inexact),
            "ABSENT" => Some(Self::Absent),
            _ => None,
        }
    }
}
