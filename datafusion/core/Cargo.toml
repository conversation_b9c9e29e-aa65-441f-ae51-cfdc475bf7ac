# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

[package]
name = "datafusion"
description = "DataFusion is an in-memory query engine that uses Apache Arrow as the memory model"
keywords = ["arrow", "query", "sql"]
include = ["benches/*.rs", "src/**/*.rs", "Cargo.toml", "LICENSE.txt", "NOTICE.txt"]
readme = "../../README.md"
version = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }
license = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }

[package.metadata.docs.rs]
all-features = true

[lints]
workspace = true

[features]
nested_expressions = ["datafusion-functions-nested"]
# This feature is deprecated. Use the `nested_expressions` feature instead.
array_expressions = ["nested_expressions"]
# Used to enable the avro format
avro = ["datafusion-common/avro", "datafusion-datasource-avro"]
backtrace = ["datafusion-common/backtrace"]
compression = [
    "xz2",
    "bzip2",
    "flate2",
    "zstd",
    "datafusion-datasource/compression",
]
crypto_expressions = ["datafusion-functions/crypto_expressions"]
datetime_expressions = ["datafusion-functions/datetime_expressions"]
default = [
    "nested_expressions",
    "crypto_expressions",
    "datetime_expressions",
    "encoding_expressions",
    "regex_expressions",
    "string_expressions",
    "unicode_expressions",
    "compression",
    "parquet",
    "recursive_protection",
]
encoding_expressions = ["datafusion-functions/encoding_expressions"]
# Used for testing ONLY: causes all values to hash to the same value (test for collisions)
force_hash_collisions = ["datafusion-physical-plan/force_hash_collisions", "datafusion-common/force_hash_collisions"]
math_expressions = ["datafusion-functions/math_expressions"]
parquet = ["datafusion-common/parquet", "dep:parquet", "datafusion-datasource-parquet"]
pyarrow = ["datafusion-common/pyarrow", "parquet"]
regex_expressions = [
    "datafusion-functions/regex_expressions",
]
recursive_protection = [
    "datafusion-common/recursive_protection",
    "datafusion-expr/recursive_protection",
    "datafusion-optimizer/recursive_protection",
    "datafusion-physical-optimizer/recursive_protection",
    "datafusion-sql/recursive_protection",
]
serde = [
    "dep:serde",
    # Enable `#[cfg_attr(feature = "serde", derive(serde::Serialize, serde::Deserialize))]`
    # statements in `arrow-schema` crate
    "arrow-schema/serde",
]
string_expressions = ["datafusion-functions/string_expressions"]
unicode_expressions = [
    "datafusion-sql/unicode_expressions",
    "datafusion-functions/unicode_expressions",
]
extended_tests = []

[dependencies]
arrow = { workspace = true }
arrow-ipc = { workspace = true }
arrow-schema = { workspace = true }
async-trait = { workspace = true }
bytes = { workspace = true }
bzip2 = { version = "0.5.2", optional = true }
chrono = { workspace = true }
datafusion-catalog = { workspace = true }
datafusion-catalog-listing = { workspace = true }
datafusion-common = { workspace = true, features = ["object_store"] }
datafusion-common-runtime = { workspace = true }
datafusion-datasource = { workspace = true }
datafusion-datasource-avro = { workspace = true, optional = true }
datafusion-datasource-csv = { workspace = true }
datafusion-datasource-json = { workspace = true }
datafusion-datasource-parquet = { workspace = true, optional = true }
datafusion-execution = { workspace = true }
datafusion-expr = { workspace = true }
datafusion-expr-common = { workspace = true }
datafusion-functions = { workspace = true }
datafusion-functions-aggregate = { workspace = true }
datafusion-functions-nested = { workspace = true, optional = true }
datafusion-functions-table = { workspace = true }
datafusion-functions-window = { workspace = true }
datafusion-macros = { workspace = true }
datafusion-optimizer = { workspace = true }
datafusion-physical-expr = { workspace = true }
datafusion-physical-expr-common = { workspace = true }
datafusion-physical-optimizer = { workspace = true }
datafusion-physical-plan = { workspace = true }
datafusion-session = { workspace = true }
datafusion-sql = { workspace = true }
flate2 = { version = "1.1.1", optional = true }
futures = { workspace = true }
itertools = { workspace = true }
log = { workspace = true }
object_store = { workspace = true }
parking_lot = { workspace = true }
parquet = { workspace = true, optional = true, default-features = true }
rand = { workspace = true }
regex = { workspace = true }
serde = { version = "1.0", default-features = false, features = ["derive"], optional = true }
sqlparser = { workspace = true }
tempfile = { workspace = true }
tokio = { workspace = true }
url = { workspace = true }
uuid = { version = "1.16", features = ["v4", "js"] }
xz2 = { version = "0.1", optional = true, features = ["static"] }
zstd = { version = "0.13", optional = true, default-features = false }

[dev-dependencies]
async-trait = { workspace = true }
criterion = { workspace = true, features = ["async_tokio", "async_futures"] }
ctor = { workspace = true }
dashmap = "6.1.0"
datafusion-doc = { workspace = true }
datafusion-functions-window-common = { workspace = true }
datafusion-physical-optimizer = { workspace = true }
doc-comment = { workspace = true }
env_logger = { workspace = true }
insta = { workspace = true }
paste = "^1.0"
rand = { workspace = true, features = ["small_rng"] }
rand_distr = "0.4.3"
regex = { workspace = true }
rstest = { workspace = true }
serde_json = { workspace = true }
sysinfo = "0.34.2"
test-utils = { path = "../../test-utils" }
tokio = { workspace = true, features = ["rt-multi-thread", "parking_lot", "fs"] }

[target.'cfg(not(target_os = "windows"))'.dev-dependencies]
nix = { version = "0.29.0", features = ["fs"] }

[[bench]]
harness = false
name = "aggregate_query_sql"

[[bench]]
harness = false
name = "csv_load"

[[bench]]
harness = false
name = "distinct_query_sql"

[[bench]]
harness = false
name = "sort_limit_query_sql"

[[bench]]
harness = false
name = "math_query_sql"

[[bench]]
harness = false
name = "filter_query_sql"

[[bench]]
harness = false
name = "struct_query_sql"

[[bench]]
harness = false
name = "window_query_sql"

[[bench]]
harness = false
name = "scalar"

[[bench]]
harness = false
name = "physical_plan"

[[bench]]
harness = false
name = "parquet_query_sql"
required-features = ["parquet"]

[[bench]]
harness = false
name = "sql_planner"

[[bench]]
harness = false
name = "sql_query_with_io"

[[bench]]
harness = false
name = "sort"

[[bench]]
harness = false
name = "topk_aggregate"

[[bench]]
harness = false
name = "map_query_sql"
required-features = ["nested_expressions"]

[[bench]]
harness = false
name = "dataframe"

[[bench]]
harness = false
name = "spm"
