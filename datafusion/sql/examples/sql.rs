// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

use std::{collections::HashMap, sync::Arc};

use arrow::datatypes::{DataType, Field, Schema};

use datafusion_common::config::ConfigOptions;
use datafusion_common::{plan_err, Result, TableReference};
use datafusion_expr::planner::ExprPlanner;
use datafusion_expr::WindowUDF;
use datafusion_expr::{
    logical_plan::builder::LogicalTableSource, AggregateUDF, ScalarUDF, TableSource,
};
use datafusion_functions::core::planner::CoreFunctionPlanner;
use datafusion_functions_aggregate::count::count_udaf;
use datafusion_functions_aggregate::sum::sum_udaf;
use datafusion_sql::{
    planner::{ContextProvider, SqlToRel},
    sqlparser::{dialect::GenericDialect, parser::Parser},
};

use datafusion_optimizer::*;

fn main() {
    let sql = "SELECT \
            c.id, c.first_name, c.last_name, \
            COUNT(*) as num_orders, \
            sum(o.price) AS total_price, \
            sum(o.price * s.sales_tax) AS state_tax \
        FROM customer c \
        JOIN state s ON c.state = s.id \
        JOIN orders o ON c.id = o.customer_id \
        WHERE o.price > 0 \
        AND c.last_name LIKE 'G%' \
        GROUP BY 1, 2, 3 \
        ORDER BY state_tax DESC";

    let sql2 = "select *  FROM customer c" ;
    
    let sql3 = "select 1 + 3, c.id * 2  FROM customer c";


    //  Projection: c.id, c.first_name, c.last_name, count(*) AS num_orders, sum(o.price) AS total_price, sum(o.price * s.sales_tax) AS state_tax
    //     Aggregate: groupBy=[[c.id, c.first_name, c.last_name]], aggr=[[count(*), sum(o.price), sum(o.price * s.sales_tax)]]
    //       Filter: o.price > Int64(0) AND c.last_name LIKE Utf8("G%")
    //         Inner Join:  Filter: c.id = o.customer_id
    //           Inner Join:  Filter: c.state = s.id
    //             SubqueryAlias: c
    //               TableScan: customer
    //             SubqueryAlias: s
    //               TableScan: state
    //           SubqueryAlias: o
    //             TableScan: orders
    
    
    // let sql = "SELECT \
    // c.id, c.first_name, c.last_name, \
    // COUNT(*) as num_orders, \
    // sum(o.price) AS total_price, \
    // sum(o.price * s.sales_tax) AS state_tax \
    // FROM customer c, state s, orders o \
    // WHERE c.state = s.id \
    // AND c.id = o.customer_id \
    // AND o.price > 0 \
    // AND c.last_name LIKE 'G%' \
    // GROUP BY 1, 2, 3 \
    // ORDER BY state_tax DESC";
    
    // Sort: state_tax DESC NULLS FIRST
    //   Projection: c.id, c.first_name, c.last_name, count(*) AS num_orders, sum(o.price) AS total_price, sum(o.price * s.sales_tax) AS state_tax
    //     Aggregate: groupBy=[[c.id, c.first_name, c.last_name]], aggr=[[count(*), sum(o.price), sum(o.price * s.sales_tax)]]
    //       Filter: c.state = s.id AND c.id = o.customer_id AND o.price > Int64(0) AND c.last_name LIKE Utf8("G%")
    //         Cross Join: 
    //           Cross Join: 
    //             SubqueryAlias: c
    //               TableScan: customer
    //             SubqueryAlias: s
    //               TableScan: state
    //           SubqueryAlias: o
    //             TableScan: orders

    let update_sql = "UPDATE customer \
    SET first_name = 'John', last_name = 'Doe' \
    WHERE id = 123";
    
    
    let simple_sql = "select * FROM customer c";
    
    
    // parse the SQL
    let dialect = GenericDialect {}; // or AnsiDialect, or your own dialect ...
    let ast = Parser::parse_sql(&dialect, sql3).unwrap();
    println!("recover: {}", ast[0].to_string());
    let statement = &ast[0];
    println!("{:?}", statement);
    
    // create a logical query plan
    let context_provider = MyContextProvider::new()
        .with_udaf(sum_udaf())
        .with_udaf(count_udaf())
        .with_expr_planner(Arc::new(CoreFunctionPlanner::default()));
    let sql_to_rel = SqlToRel::new(&context_provider);
    let plan = sql_to_rel.sql_statement_to_plan(statement.clone()).unwrap();
    
    println!("raw plan: \n {plan}");
    let optimized_plan = Optimizer::default()
        .optimize(plan, &OptimizerContext::default(), |_, _| {})
        .unwrap();
    
    // show the optimized plan
    println!("\noptimized plan: \n {optimized_plan}");


    //let dialect = GenericDialect {}; // or AnsiDialect, or your own dialect ...
    // let ast = Parser::parse_sql(&dialect, update_sql).unwrap();
    // let statement = &ast[0];
    // 
    // // create a logical query plan
    // let context_provider = MyContextProvider::new()
    //     .with_udaf(sum_udaf())
    //     .with_udaf(count_udaf())
    //     .with_expr_planner(Arc::new(CoreFunctionPlanner::default()));
    // let sql_to_rel = SqlToRel::new(&context_provider);
    // let update_plan = sql_to_rel.sql_statement_to_plan(statement.clone()).unwrap();
    // 
    // println!("raw plan: \n {update_plan}");
    // let optimized_plan = Optimizer::default()
    //     .optimize(update_plan, &OptimizerContext::default(), |_, _| {})
    //     .unwrap();
    // 
    // // show the optimized plan
    // println!("\noptimized plan: \n {optimized_plan}");
    
    
    // Create TABLE
//     let sql_create = "CREATE TABLE orders(
//   order_id INT PRIMARY KEY,
//   customer_id VARCHAR(255) NOT NULL,
//   order_date DATE NOT NULL
// )";

    // let dialect = GenericDialect {}; // or AnsiDialect, or your own dialect ...
    // let ast_create = Parser::parse_sql(&dialect, sql_create).unwrap();
    // let statement_create = &ast_create[0];
    // 
    // let context_provider = MyContextProvider::new()
    //     .with_udaf(sum_udaf())
    //     .with_udaf(count_udaf())
    //     .with_expr_planner(Arc::new(CoreFunctionPlanner::default()));
    // let sql_to_rel = SqlToRel::new(&context_provider);
    // // create a logical query plan
    // let create_plan = sql_to_rel.sql_statement_to_plan(statement_create.clone()).unwrap();
    // 
    // println!("raw plan: \n {create_plan}");
    // let optimized_plan_create = Optimizer::default()
    //     .optimize(create_plan, &OptimizerContext::default(), |_, _| {})
    //     .unwrap();
    // 
    // // show the optimized plan
    // println!("\noptimized plan: \n {optimized_plan_create}");
    // 
}

struct MyContextProvider {
    options: ConfigOptions,
    tables: HashMap<String, Arc<dyn TableSource>>,
    udafs: HashMap<String, Arc<AggregateUDF>>,
    expr_planners: Vec<Arc<dyn ExprPlanner>>,
}

impl MyContextProvider {
    fn with_udaf(mut self, udaf: Arc<AggregateUDF>) -> Self {
        self.udafs.insert(udaf.name().to_string(), udaf);
        self
    }

    fn with_expr_planner(mut self, planner: Arc<dyn ExprPlanner>) -> Self {
        self.expr_planners.push(planner);
        self
    }

    fn new() -> Self {
        let mut tables = HashMap::new();
        tables.insert(
            "customer".to_string(),
            create_table_source(vec![
                Field::new("id", DataType::Int32, false),
                Field::new("first_name", DataType::Utf8, false),
                Field::new("last_name", DataType::Utf8, false),
                Field::new("state", DataType::Utf8, false),
            ]),
        );
        tables.insert(
            "state".to_string(),
            create_table_source(vec![
                Field::new("id", DataType::Int32, false),
                Field::new("sales_tax", DataType::Decimal128(10, 2), false),
            ]),
        );
        tables.insert(
            "orders".to_string(),
            create_table_source(vec![
                Field::new("id", DataType::Int32, false),
                Field::new("customer_id", DataType::Int32, false),
                Field::new("item_id", DataType::Int32, false),
                Field::new("quantity", DataType::Int32, false),
                Field::new("price", DataType::Decimal128(10, 2), false),
            ]),
        );
        Self {
            tables,
            options: Default::default(),
            udafs: Default::default(),
            expr_planners: vec![],
        }
    }
}

fn create_table_source(fields: Vec<Field>) -> Arc<dyn TableSource> {
    Arc::new(LogicalTableSource::new(Arc::new(
        Schema::new_with_metadata(fields, HashMap::new()),
    )))
}

impl ContextProvider for MyContextProvider {
    fn get_table_source(&self, name: TableReference) -> Result<Arc<dyn TableSource>> {
        match self.tables.get(name.table()) {
            Some(table) => Ok(Arc::clone(table)),
            _ => plan_err!("Table not found: {}", name.table()),
        }
    }

    fn get_function_meta(&self, _name: &str) -> Option<Arc<ScalarUDF>> {
        None
    }

    fn get_aggregate_meta(&self, name: &str) -> Option<Arc<AggregateUDF>> {
        self.udafs.get(name).cloned()
    }

    fn get_variable_type(&self, _variable_names: &[String]) -> Option<DataType> {
        None
    }

    fn get_window_meta(&self, _name: &str) -> Option<Arc<WindowUDF>> {
        None
    }

    fn options(&self) -> &ConfigOptions {
        &self.options
    }

    fn udf_names(&self) -> Vec<String> {
        Vec::new()
    }

    fn udaf_names(&self) -> Vec<String> {
        Vec::new()
    }

    fn udwf_names(&self) -> Vec<String> {
        Vec::new()
    }

    fn get_expr_planners(&self) -> &[Arc<dyn ExprPlanner>] {
        &self.expr_planners
    }
}
