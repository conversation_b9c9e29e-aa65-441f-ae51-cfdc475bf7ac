# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

[package]
name = "datafusion-physical-plan"
description = "Physical (ExecutionPlan) implementations for DataFusion query engine"
keywords = ["arrow", "query", "sql"]
readme = "README.md"
version = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }
license = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }

[package.metadata.docs.rs]
all-features = true

[lints]
workspace = true

[features]
force_hash_collisions = []

[lib]
name = "datafusion_physical_plan"

[dependencies]
ahash = { workspace = true }
arrow = { workspace = true }
arrow-ord = { workspace = true }
arrow-schema = { workspace = true }
async-trait = { workspace = true }
chrono = { workspace = true }
datafusion-common = { workspace = true, default-features = true }
datafusion-common-runtime = { workspace = true, default-features = true }
datafusion-execution = { workspace = true }
datafusion-expr = { workspace = true }
datafusion-functions-window-common = { workspace = true }
datafusion-physical-expr = { workspace = true, default-features = true }
datafusion-physical-expr-common = { workspace = true }
futures = { workspace = true }
half = { workspace = true }
hashbrown = { workspace = true }
indexmap = { workspace = true }
itertools = { workspace = true, features = ["use_std"] }
log = { workspace = true }
parking_lot = { workspace = true }
pin-project-lite = "^0.2.7"
tokio = { workspace = true }

[dev-dependencies]
criterion = { workspace = true, features = ["async_futures"] }
datafusion-functions-aggregate = { workspace = true }
datafusion-functions-window = { workspace = true }
insta = { workspace = true }
rand = { workspace = true }
rstest = { workspace = true }
rstest_reuse = "0.7.0"
tempfile = "3.19.1"
tokio = { workspace = true, features = [
    "rt-multi-thread",
    "fs",
    "parking_lot",
] }

[[bench]]
harness = false
name = "partial_ordering"

[[bench]]
harness = false
name = "spill_io"
