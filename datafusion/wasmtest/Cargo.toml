# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

[package]
name = "datafusion-wasmtest"
description = "Test library to compile datafusion crates to wasm"
readme = "README.md"
version = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }
license = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }

[package.metadata.docs.rs]
all-features = true

[lints]
workspace = true

[lib]
crate-type = ["cdylib", "rlib"]

[dependencies]
# chrono must be compiled with wasmbind feature
chrono = { version = "0.4", features = ["wasmbind"] }

# The `console_error_panic_hook` crate provides better debugging of panics by
# logging them with `console.error`. This is great for development, but requires
# all the `std::fmt` and `std::panicking` infrastructure, so isn't great for
# code size when deploying.
console_error_panic_hook = { version = "0.1.1", optional = true }
datafusion = { workspace = true, features = ["parquet"] }
datafusion-common = { workspace = true, default-features = true }
datafusion-execution = { workspace = true }
datafusion-expr = { workspace = true }
datafusion-optimizer = { workspace = true, default-features = true }
datafusion-physical-plan = { workspace = true }
datafusion-sql = { workspace = true }
# getrandom must be compiled with js feature
getrandom = { version = "0.2.8", features = ["js"] }

wasm-bindgen = "0.2.99"

[dev-dependencies]
insta = { workspace = true }
object_store = { workspace = true }
tokio = { workspace = true }
url = { workspace = true }
wasm-bindgen-test = "0.3.49"
