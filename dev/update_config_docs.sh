#!/bin/bash
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

set -e

SOURCE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "${SOURCE_DIR}/../" && pwd

TARGET_FILE="docs/source/user-guide/configs.md"
PRINT_CONFIG_DOCS_COMMAND="cargo run --manifest-path datafusion/core/Cargo.toml --bin print_config_docs"

echo "Inserting header"
cat <<'EOF' > "$TARGET_FILE"
<!---
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->

<!---
This file was generated by the dev/update_config_docs.sh script.
Do not edit it manually as changes will be overwritten.
Instead, edit dev/update_config_docs.sh or the docstrings in datafusion/core/src/config.rs.
-->

# Configuration Settings

The following configuration options can be passed to `SessionConfig` to control various aspects of query execution.

For applications which do not expose `SessionConfig`, like `datafusion-cli`, these options may also be set via environment variables.
To construct a session with options from the environment, use `SessionConfig::from_env`.
The name of the environment variable is the option's key, transformed to uppercase and with periods replaced with underscores.
For example, to configure `datafusion.execution.batch_size` you would set the `DATAFUSION_EXECUTION_BATCH_SIZE` environment variable.
Values are parsed according to the [same rules used in casts from Utf8](https://docs.rs/arrow/latest/arrow/compute/kernels/cast/fn.cast.html).
If the value in the environment variable cannot be cast to the type of the configuration option, the default value will be used instead and a warning emitted.
Environment variables are read during `SessionConfig` initialisation so they must be set beforehand and will not affect running sessions.

EOF

echo "Running CLI and inserting config docs table"
$PRINT_CONFIG_DOCS_COMMAND >> "$TARGET_FILE"

echo "Running prettier"
npx prettier@2.3.2 --write "$TARGET_FILE"

echo "'$TARGET_FILE' successfully updated!"
