*.npmrc
*.gitignore
*.dockerignore
.gitmodules
*_generated.h
*_generated.js
*_generated.ts
*.csv
*.json
*.snap
.github/ISSUE_TEMPLATE/*.md
.github/pull_request_template.md
ci/etc/rprofile
ci/etc/*.patch
ci/vcpkg/*.patch
CHANGELOG.md
datafusion/CHANGELOG.md
dev/requirements*.txt
dev/release/rat_exclude_files.txt
pax_global_header
MANIFEST.in
__init__.pxd
__init__.py
requirements.txt
*.html
*.sgml
*.css
*.png
*.ico
*.svg
*.devhelp2
*.scss
.gitattributes
rust-toolchain
benchmarks/queries/q*.sql
**/testdata/*
benchmarks/queries/*
benchmarks/expected-plans/*
benchmarks/data/*
datafusion-cli/tests/data/*
ci/*
**/*.svg
**/*.csv
**/*.json
**/*.sql
venv/*
testing/*
target/*
**/target/*
Cargo.lock
**/Cargo.lock
.history
parquet-testing/*
*rat.txt
datafusion/proto/src/generated/datafusion_proto_common.rs
datafusion/proto/src/generated/pbjson.rs
datafusion/proto/src/generated/prost.rs
datafusion/proto-common/src/generated/pbjson.rs
datafusion/proto-common/src/generated/prost.rs
.github/ISSUE_TEMPLATE/bug_report.yml
.github/ISSUE_TEMPLATE/feature_request.yml
.github/workflows/docs.yaml
**/node_modules/*
datafusion/wasmtest/pkg/*
clippy.toml
