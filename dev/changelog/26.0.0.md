<!---
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->

## [26.0.0](https://github.com/apache/datafusion/tree/26.0.0) (2023-06-02)

[Full Changelog](https://github.com/apache/datafusion/compare/25.0.0...26.0.0)

**Breaking changes:**

- feat: implement serialize/deserialize for extension logical plan [#6378](https://github.com/apache/datafusion/pull/6378) (waynexia)
- Use `std::ops` traits for `Exprs` rather than custom function names [#6465](https://github.com/apache/datafusion/pull/6465) (LouisGariepy)
- Support Defining Ordering Equivalence at the Source [#6469](https://github.com/apache/datafusion/pull/6469) (berkaysynnada)

**Implemented enhancements:**

- feat: support type cast in SchemaAdapter [#6404](https://github.com/apache/datafusion/pull/6404) (e1ijah1)
- feat: eliminate useless join | convert inner to outer when condition is true [#6443](https://github.com/apache/datafusion/pull/6443) (jackwener)
- feat: datafusion-cli support executes sql with escaped characters [#6498](https://github.com/apache/datafusion/pull/6498) (r4ntix)
- feat: fix docs [#6534](https://github.com/apache/datafusion/pull/6534) (Folyd)

**Fixed bugs:**

- fix: error instead of panic when date_bin interval is 0 [#6522](https://github.com/apache/datafusion/pull/6522) (NGA-TRAN)

**Documentation updates:**

- User guide lists window functions [#6402](https://github.com/apache/datafusion/pull/6402) (toppyy)
- Improve getting started guide, add note about compatible `arrow` versions [#6472](https://github.com/apache/datafusion/pull/6472) (alamb)
- Add link to Python Bindings in top-level README [#6532](https://github.com/apache/datafusion/pull/6532) (andygrove)

**Merged pull requests:**

- Minor: Update docs with extended file format support [#6356](https://github.com/apache/datafusion/pull/6356) (alamb)
- chore(deps): update async-compression requirement from 0.3.14 to 0.4.0 [#6336](https://github.com/apache/datafusion/pull/6336) (dependabot[bot])
- INSERT returns number of rows written, add `InsertExec` to handle common case. [#6354](https://github.com/apache/datafusion/pull/6354) (alamb)
- Minor: used named constant for the schema inference concurrency limit [#6389](https://github.com/apache/datafusion/pull/6389) (alamb)
- Prepare for 25.0.0 Release [#6390](https://github.com/apache/datafusion/pull/6390) (andygrove)
- minor: Add Python script for generating changelog content [#6391](https://github.com/apache/datafusion/pull/6391) (andygrove)
- Support is [not] distinct from for binaryarray types [#6394](https://github.com/apache/datafusion/pull/6394) (Dandandan)
- Minor: Improve documentation of `MemoryPool` [#6388](https://github.com/apache/datafusion/pull/6388) (alamb)
- More scalar subqueries support [#6372](https://github.com/apache/datafusion/pull/6372) (mingmwang)
- feat: support type cast in SchemaAdapter [#6404](https://github.com/apache/datafusion/pull/6404) (e1ijah1)
- Update arrow 40 [#6412](https://github.com/apache/datafusion/pull/6412) (tustvold)
- feat: implement serialize/deserialize for extension logical plan [#6378](https://github.com/apache/datafusion/pull/6378) (waynexia)
- Do not run avro sqllogictests tests unless the avro feature is enabled [#6429](https://github.com/apache/datafusion/pull/6429) (alamb)
- chore(deps): update sqlparser requirement from 0.33 to 0.34 [#6416](https://github.com/apache/datafusion/pull/6416) (alamb)
- run tests with avro enabled when verifying a release candidate [#6401](https://github.com/apache/datafusion/pull/6401) (andygrove)
- Minor: remove unecessary cranelift dependency [#6430](https://github.com/apache/datafusion/pull/6430) (alamb)
- Improve error message for wrong built-in scalar function signatures. [#6415](https://github.com/apache/datafusion/pull/6415) (2010YOUY01)
- refactor: split `CommonSubexprEliminate::try_optimize` [#6348](https://github.com/apache/datafusion/pull/6348) (crepererum)
- chore(deps): update criterion requirement from 0.4 to 0.5 [#6434](https://github.com/apache/datafusion/pull/6434) (dependabot[bot])
- Fix UNION ALL aliasing with more complex queries [#6417](https://github.com/apache/datafusion/pull/6417) (comphead)
- Reduce output when `sqllogictest` runs successfully, and run tests in parallel [#6393](https://github.com/apache/datafusion/pull/6393) (alamb)
- Named window support [#6419](https://github.com/apache/datafusion/pull/6419) (berkaysynnada)
- Lexicographical Ordering Equivalence Support [#6431](https://github.com/apache/datafusion/pull/6431) (mustafasrepo)
- Fix Select Into with Order By and Limit [#6442](https://github.com/apache/datafusion/pull/6442) (berkaysynnada)
- Fix typo in faq.md [#6451](https://github.com/apache/datafusion/pull/6451) (haoxins)
- Skip casting result array for binary numerical operators result between array and scalar if possible [#6438](https://github.com/apache/datafusion/pull/6438) (viirya)
- Minor fix for the Debug of FileScanConfig [#6454](https://github.com/apache/datafusion/pull/6454) (yahoNanJing)
- Improve Display for BuiltinScalarFunction [#6448](https://github.com/apache/datafusion/pull/6448) (2010YOUY01)
- Fix wrong benchmark column names in `compare.py` after the last refactor [#6459](https://github.com/apache/datafusion/pull/6459) (nvartolomei)
- Minor: remove dead code `sort_expr_list_eq_strict_order` [#6460](https://github.com/apache/datafusion/pull/6460) (alamb)
- feat: eliminate useless join | convert inner to outer when condition is true [#6443](https://github.com/apache/datafusion/pull/6443) (jackwener)
- Minor add more docs to equivalence class code [#6461](https://github.com/apache/datafusion/pull/6461) (alamb)
- Add support for FIRST_VALUE, LAST_VALUE Aggregate Functions [#6445](https://github.com/apache/datafusion/pull/6445) (mustafasrepo)
- Ordering Equivalence Builder [#6452](https://github.com/apache/datafusion/pull/6452) (mustafasrepo)
- Fix protobuf install command for Fedora Linux. [#6466](https://github.com/apache/datafusion/pull/6466) (LouisGariepy)
- Continue PR 4757 [#6456](https://github.com/apache/datafusion/pull/6456) (yahoNanJing)
- minor: Use parse_multipart_identifier from sqlparser [#6467](https://github.com/apache/datafusion/pull/6467) (Jefffrey)
- User guide lists window functions [#6402](https://github.com/apache/datafusion/pull/6402) (toppyy)
- minor: fix comment docs [#6473](https://github.com/apache/datafusion/pull/6473) (Folyd)
- Clippy lints [#6464](https://github.com/apache/datafusion/pull/6464) (LouisGariepy)
- Use `std::ops` traits for `Exprs` rather than custom function names [#6465](https://github.com/apache/datafusion/pull/6465) (LouisGariepy)
- Refactor temporal arithmetic [#6433](https://github.com/apache/datafusion/pull/6433) (berkaysynnada)
- Fix case evaluation with NULL [#6477](https://github.com/apache/datafusion/pull/6477) (byteink)
- Substrait: Support Expr::ScalarFunction [#6471](https://github.com/apache/datafusion/pull/6471) (jayzhan211)
- Support Defining Ordering Equivalence at the Source [#6469](https://github.com/apache/datafusion/pull/6469) (berkaysynnada)
- Improve getting started guide, add note about compatible `arrow` versions [#6472](https://github.com/apache/datafusion/pull/6472) (alamb)
- Add tpch test cases with data. [#6435](https://github.com/apache/datafusion/pull/6435) (liurenjie1024)
- Minor: Improve the tpch sqllogictest docs [#6493](https://github.com/apache/datafusion/pull/6493) (alamb)
- reduce search complexity for BuiltinScalarFunction [#6479](https://github.com/apache/datafusion/pull/6479) (comphead)
- Fix builds fail with error: symbol `init___rust_ctor___ctor` is already defined #6495 [#6505](https://github.com/apache/datafusion/pull/6505) (alamb)
- Don't optimize AnalyzeExec (#6379) (try 2) [#6494](https://github.com/apache/datafusion/pull/6494) (alamb)
- Rewrite large OR chains as IN lists [#6414](https://github.com/apache/datafusion/pull/6414) (aprimadi)
- Add SELECT _ EXCLUDE, SELECT _ EXCEPT support [#6481](https://github.com/apache/datafusion/pull/6481) (mustafasrepo)
- Bug fix, First accumulator multiple batch aware [#6503](https://github.com/apache/datafusion/pull/6503) (mustafasrepo)
- Make `FileStream` error handling configurable [#6491](https://github.com/apache/datafusion/pull/6491) (thinkharderdev)
- Minimize clone in OrInListSimplifier [#6509](https://github.com/apache/datafusion/pull/6509) (aprimadi)
- Add serde for `DROP VIEW` [#6499](https://github.com/apache/datafusion/pull/6499) (Dandandan)
- Support simplifying expressions like `~ ^(bar|foo)$` [#6487](https://github.com/apache/datafusion/pull/6487) (tanruixiang)
- minor: make window frame error messages more consistent [#6519](https://github.com/apache/datafusion/pull/6519) (comphead)
- Minor: Clean up `use`s to point at real crates [#6515](https://github.com/apache/datafusion/pull/6515) (alamb)
- Standardize `RUST_LOG` configuration test setup [#6506](https://github.com/apache/datafusion/pull/6506) (alamb)
- Fix new clippy lint [#6535](https://github.com/apache/datafusion/pull/6535) (alamb)
- feat: datafusion-cli support executes sql with escaped characters [#6498](https://github.com/apache/datafusion/pull/6498) (r4ntix)
- Minor: Add EXCEPT/EXCLUDE to SQL guide [#6512](https://github.com/apache/datafusion/pull/6512) (alamb)
- fix: error instead of panic when date_bin interval is 0 [#6522](https://github.com/apache/datafusion/pull/6522) (NGA-TRAN)
- Add link to Python Bindings in top-level README [#6532](https://github.com/apache/datafusion/pull/6532) (andygrove)
- feat: fix docs [#6534](https://github.com/apache/datafusion/pull/6534) (Folyd)
- Resolve contradictory requirements by conversion of ordering sensitive aggregators [#6482](https://github.com/apache/datafusion/pull/6482) (mustafasrepo)
