<!---
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->

## [37.1.0](https://github.com/apache/datafusion/tree/37.1.0) (2024-04-18)

[Full Changelog](https://github.com/apache/datafusion/compare/37.0.0...37.1.0)

**Merged pull requests:**

- Backport to 37: fix `NamedStructField` should be rewritten in OperatorToFunction in subquery regression [#10103](https://github.com/apache/datafusion/pull/10103) (alamb)
- Backport to 37: fix Coercion stopped working for coalesce on a dictionary column [#10104](https://github.com/apache/datafusion/pull/10104) (alamb)
- Backport to 37: group by count distinct doesn't work for timestamps with time zone [#10105](https://github.com/apache/datafusion/pull/10105) (alamb)
- Backport to 37: make udf structs public [#10107](https://github.com/apache/datafusion/pull/10107) (alamb)
- Backport to 37: Reduce DataFrame stack size and fix large futures warnings [#10123](https://github.com/apache/datafusion/pull/10123) (sergiimk)
