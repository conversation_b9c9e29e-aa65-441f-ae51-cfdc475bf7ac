<!---
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->

## [16.1.0](https://github.com/apache/datafusion/tree/16.1.0) (2023-01-19)

[Full Changelog](https://github.com/apache/datafusion/compare/16.1.0-rc1...16.0.0)

**Merged pull requests:**

- Fix column indices in EnforceDistribution optimizer in Partial AggregateMode \(\#4878\) [\#4959](https://github.com/apache/datafusion/pull/4959)
- Make it able to specify a session id for SessionState \(\#4933\) [\#4951](https://github.com/apache/datafusion/pull/4951)
