# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Labeler

concurrency:
  group: ${{ github.repository }}-${{ github.head_ref || github.sha }}-${{ github.workflow }}
  cancel-in-progress: true

on:
  pull_request_target:
    types:
      - opened
      - edited
      - synchronize

jobs:
  process:
    name: Process
    runs-on: ubuntu-latest
    # only run for users whose permissions allow them to update PRs
    # otherwise labeler is failing:
    # https://github.com/apache/datafusion/issues/3743
    permissions:
      contents: read
      pull-requests: write
    steps:
      - uses: actions/checkout@v4

      - name: Assign GitHub labels
        if: |
          github.event_name == 'pull_request_target' &&
            (github.event.action == 'opened' ||
             github.event.action == 'synchronize')
        uses: actions/labeler@v5.0.0
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          configuration-path: .github/workflows/dev_pr/labeler.yml
          sync-labels: true

      # TODO: Enable this when eps1lon/actions-label-merge-conflict is available.
      # - name: Checks if PR needs rebase
      #   if: |
      #     github.event_name == 'push' ||
      #     (github.event_name == 'pull_request_target' &&
      #        (github.event.action == 'opened' ||
      #         github.event.action == 'synchronize'))
      #   uses: eps1lon/actions-label-merge-conflict@releases/2.x
      #   with:
      #     dirtyLabel: "needs-rebase"
      #     repoToken: "${{ secrets.GITHUB_TOKEN }}"
