# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

name: Dev
on: [push, pull_request]

concurrency:
  group: ${{ github.repository }}-${{ github.head_ref || github.sha }}-${{ github.workflow }}
  cancel-in-progress: true

jobs:
  license-header-check:
    runs-on: ubuntu-latest
    name: Check License Header
    steps:
      - uses: actions/checkout@v4
      - uses: korandoru/hawkeye@v6

  prettier:
    name: Use prettier to check formatting of documents
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
      - name: Prettier check
        run: |
          # if you encounter error, rerun the command below and commit the changes
          #
          # ignore subproject CHANGELOG.md because they are machine generated
          npx prettier@2.7.1 --write \
            '{datafusion,datafusion-cli,datafusion-examples,dev,docs}/**/*.md' \
            '!datafusion/CHANGELOG.md' \
            README.md \
            CONTRIBUTING.md
          git diff --exit-code
