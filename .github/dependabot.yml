# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

version: 2
updates:
  - package-ecosystem: cargo
    directory: "/"
    schedule:
      interval: daily
    target-branch: main
    labels: [auto-dependencies]
    ignore:
      # major version bumps of arrow* and parquet are handled manually
      - dependency-name: "arrow*"
        update-types: ["version-update:semver-major"]
      - dependency-name: "parquet"
        update-types: ["version-update:semver-major"]
    groups:
      # minor and patch bumps of arrow* and parquet are grouped
      arrow-parquet:
        applies-to: version-updates
        patterns:
          - "arrow*"
          - "parquet"
        update-types:
          - "minor"
          - "patch"
      proto:
        applies-to: version-updates
        patterns:
          - "prost*"
          - "pbjson*"
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 10
    labels: [auto-dependencies]
