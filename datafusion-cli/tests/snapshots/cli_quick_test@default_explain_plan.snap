---
source: datafusion-cli/tests/cli_integration.rs
info:
  program: datafusion-cli
  args:
    - "--command"
    - EXPLAIN SELECT 123
snapshot_kind: text
---
success: true
exit_code: 0
----- stdout -----
[CLI_VERSION]
+---------------+-------------------------------+
| plan_type     | plan                          |
+---------------+-------------------------------+
| physical_plan | ┌───────────────────────────┐ |
|               | │       ProjectionExec      │ |
|               | │    --------------------   │ |
|               | │      Int64(123): 123      │ |
|               | └─────────────┬─────────────┘ |
|               | ┌─────────────┴─────────────┐ |
|               | │     PlaceholderRowExec    │ |
|               | └───────────────────────────┘ |
|               |                               |
+---------------+-------------------------------+
1 row(s) fetched. 
[ELAPSED]


----- stderr -----
