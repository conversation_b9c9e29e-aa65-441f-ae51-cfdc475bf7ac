---
source: datafusion-cli/tests/cli_integration.rs
info:
  program: datafusion-cli
  args:
    - "--command"
    - EXPLAIN SELECT 123
  env:
    DATAFUSION_EXPLAIN_FORMAT: pgjson
snapshot_kind: text
---
success: true
exit_code: 0
----- stdout -----
[CLI_VERSION]
+--------------+-----------------------------------------+
| plan_type    | plan                                    |
+--------------+-----------------------------------------+
| logical_plan | [                                       |
|              |   {                                     |
|              |     "Plan": {                           |
|              |       "Expressions": [                  |
|              |         "Int64(123)"                    |
|              |       ],                                |
|              |       "Node Type": "Projection",        |
|              |       "Output": [                       |
|              |         "Int64(123)"                    |
|              |       ],                                |
|              |       "Plans": [                        |
|              |         {                               |
|              |           "Node Type": "EmptyRelation", |
|              |           "Output": [],                 |
|              |           "Plans": []                   |
|              |         }                               |
|              |       ]                                 |
|              |     }                                   |
|              |   }                                     |
|              | ]                                       |
+--------------+-----------------------------------------+
1 row(s) fetched. 
[ELAPSED]


----- stderr -----
