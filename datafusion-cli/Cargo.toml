# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

[package]
name = "datafusion-cli"
description = "Command Line Client for DataFusion query engine."
readme = "README.md"
version = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }
license = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }

[package.metadata.docs.rs]
all-features = true

[features]
default = []
backtrace = ["datafusion/backtrace"]

[dependencies]
arrow = { workspace = true }
async-trait = { workspace = true }
aws-config = "1.6.1"
aws-credential-types = "1.2.0"
clap = { version = "4.5.37", features = ["derive", "cargo"] }
datafusion = { workspace = true, features = [
    "avro",
    "crypto_expressions",
    "datetime_expressions",
    "encoding_expressions",
    "nested_expressions",
    "parquet",
    "recursive_protection",
    "regex_expressions",
    "unicode_expressions",
    "compression",
] }
dirs = "6.0.0"
env_logger = { workspace = true }
futures = { workspace = true }
mimalloc = { version = "0.1", default-features = false }
object_store = { workspace = true, features = ["aws", "gcp", "http"] }
parking_lot = { workspace = true }
parquet = { workspace = true, default-features = false }
regex = { workspace = true }
rustyline = "15.0"
tokio = { workspace = true, features = ["macros", "rt", "rt-multi-thread", "sync", "parking_lot", "signal"] }
url = { workspace = true }

[dev-dependencies]
assert_cmd = "2.0"
ctor = { workspace = true }
insta = { workspace = true }
insta-cmd = "0.6.0"
predicates = "3.0"
rstest = { workspace = true }
