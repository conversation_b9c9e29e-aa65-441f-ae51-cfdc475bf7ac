<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl"?>
<rdf:RDF xml:lang="en"
         xmlns="http://usefulinc.com/ns/doap#"
         xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
         xmlns:asfext="http://projects.apache.org/ns/asfext#"
         xmlns:foaf="http://xmlns.com/foaf/0.1/">
<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
  <!-- 
    Apache DOAP file that drives https://projects.apache.org/project.html?datafusion
    See https://projects.apache.org/create.html for syntax and details
    --> 
  <Project rdf:about="https://datafusion.apache.org">
    <created>2024-04-17</created>
    <license rdf:resource="https://spdx.org/licenses/Apache-2.0" />
    <name>Apache DataFusion</name>
    <homepage rdf:resource="https://datafusion.apache.org" />
    <asfext:pmc rdf:resource="https://datafusion.apache.org" />
    <shortdesc>Apache DataFusion is a fast, extensible query engine for building high-quality data-centric systems in Rust.</shortdesc>
    <description>
        Apache DataFusion is a fast, extensible query engine for building high-quality data-centric systems
        in Rust, using the Apache Arrow in-memory format. Python Bindings are also available. DataFusion offers SQL
        and Dataframe APIs, excellent performance, built-in support for CSV, Parquet, JSON, and Avro,
        extensive customization, and a great community.
    </description>
    <bug-database rdf:resource="https://github.com/apache/datafusion/issues" />
    <mailing-list rdf:resource="https://lists.apache.org/list.html?<EMAIL>" />
    <download-page rdf:resource="https://github.com/apache/datafusion/releases" />
    <programming-language>Python</programming-language>
    <programming-language>Rust</programming-language>
    <category rdf:resource="https://projects.apache.org/category/big-data" />
    <category rdf:resource="https://projects.apache.org/category/database" />
    <category rdf:resource="https://projects.apache.org/category/data-engineering" />
    <category rdf:resource="https://projects.apache.org/category/library" />
    <repository>
      <GitRepository>
        <location rdf:resource="https://github.com/apache/datafusion.git"/>
        <browse rdf:resource="https://github.com/apache/datafusion/"/>
      </GitRepository>
    </repository>
  </Project>
</rdf:RDF>
